/**
 * 推送通知管理工具
 * 支持H5、APP、小程序的消息推送
 */

// 推送权限状态
export enum PushPermissionStatus {
  DEFAULT = 'default',
  GRANTED = 'granted', 
  DENIED = 'denied'
}

// 推送消息类型
export interface PushMessage {
  title: string
  body: string
  icon?: string
  tag?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
}

class PushManager {
  private isInitialized = false
  private permission: PushPermissionStatus = PushPermissionStatus.DEFAULT

  /**
   * 初始化推送服务
   */
  async init(): Promise<boolean> {
    try {
      // #ifdef H5
      await this.initWebPush()
      // #endif
      
      // #ifdef APP-PLUS
      await this.initAppPush()
      // #endif
      
      // #ifdef MP-WEIXIN
      await this.initMiniProgramPush()
      // #endif
      
      this.isInitialized = true
      return true
    } catch (error) {
      console.error('推送服务初始化失败:', error)
      return false
    }
  }

  /**
   * 初始化Web推送
   */
  private async initWebPush(): Promise<void> {
    // #ifdef H5
    if (!('Notification' in window)) {
      throw new Error('浏览器不支持通知功能')
    }

    // 检查当前权限状态
    this.permission = Notification.permission as PushPermissionStatus

    // 如果支持Service Worker，注册推送服务
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker注册成功:', registration)
        
        // 检查推送订阅
        const subscription = await registration.pushManager.getSubscription()
        if (subscription) {
          console.log('已有推送订阅:', subscription)
        }
      } catch (error) {
        console.error('Service Worker注册失败:', error)
      }
    }
    // #endif
  }

  /**
   * 初始化APP推送
   */
  private async initAppPush(): Promise<void> {
    // #ifdef APP-PLUS
    // 监听推送消息点击事件
    plus.push.addEventListener('click', (message) => {
      console.log('推送消息被点击:', message)
      this.handlePushClick(message)
    })

    // 监听推送消息接收事件
    plus.push.addEventListener('receive', (message) => {
      console.log('收到推送消息:', message)
      this.handlePushReceive(message)
    })

    // 获取客户端推送标识
    const clientInfo = plus.push.getClientInfo()
    console.log('推送客户端信息:', clientInfo)
    // #endif
  }

  /**
   * 初始化小程序推送
   */
  private async initMiniProgramPush(): Promise<void> {
    // #ifdef MP-WEIXIN
    // 小程序推送需要通过模板消息实现
    // 这里主要是准备工作
    console.log('小程序推送初始化完成')
    // #endif
  }

  /**
   * 请求推送权限
   */
  async requestPermission(): Promise<PushPermissionStatus> {
    // #ifdef H5
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      this.permission = permission as PushPermissionStatus
      return this.permission
    }
    // #endif

    // #ifdef APP-PLUS
    // APP端通常默认有推送权限
    this.permission = PushPermissionStatus.GRANTED
    return this.permission
    // #endif

    // #ifdef MP-WEIXIN
    // 小程序需要用户主动触发
    this.permission = PushPermissionStatus.GRANTED
    return this.permission
    // #endif

    return PushPermissionStatus.DENIED
  }

  /**
   * 发送本地通知
   */
  async sendLocalNotification(message: PushMessage): Promise<boolean> {
    if (!this.isInitialized) {
      console.warn('推送服务未初始化')
      return false
    }

    try {
      // #ifdef H5
      if (this.permission === PushPermissionStatus.GRANTED) {
        const notification = new Notification(message.title, {
          body: message.body,
          icon: message.icon || '/static/icons/message.png',
          tag: message.tag,
          data: message.data,
          requireInteraction: false,
          silent: false
        })

        // 设置点击事件
        notification.onclick = () => {
          this.handleNotificationClick(message)
          notification.close()
        }

        // 自动关闭
        setTimeout(() => {
          notification.close()
        }, 5000)

        return true
      }
      // #endif

      // #ifdef APP-PLUS
      plus.push.createMessage(
        message.body,
        JSON.stringify(message.data || {}),
        {
          title: message.title,
          when: new Date(),
          icon: message.icon || '/static/icons/message.png'
        }
      )
      return true
      // #endif

      // #ifdef MP-WEIXIN
      // 小程序显示toast
      uni.showToast({
        title: message.title,
        icon: 'none',
        duration: 2000
      })
      return true
      // #endif

      return false
    } catch (error) {
      console.error('发送本地通知失败:', error)
      return false
    }
  }

  /**
   * 订阅推送服务
   */
  async subscribePush(userId: string): Promise<string | null> {
    // #ifdef H5
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(
            process.env.VITE_VAPID_PUBLIC_KEY || ''
          )
        })

        // 将订阅信息发送到服务器
        await this.sendSubscriptionToServer(userId, subscription)
        
        return JSON.stringify(subscription)
      } catch (error) {
        console.error('订阅推送失败:', error)
        return null
      }
    }
    // #endif

    // #ifdef APP-PLUS
    // APP端获取推送token
    const clientInfo = plus.push.getClientInfo()
    if (clientInfo) {
      // 将token发送到服务器
      await this.sendTokenToServer(userId, clientInfo.token)
      return clientInfo.token
    }
    // #endif

    return null
  }

  /**
   * 取消推送订阅
   */
  async unsubscribePush(userId: string): Promise<boolean> {
    try {
      // #ifdef H5
      if ('serviceWorker' in navigator && 'PushManager' in window) {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.getSubscription()
        if (subscription) {
          await subscription.unsubscribe()
          await this.removeSubscriptionFromServer(userId)
          return true
        }
      }
      // #endif

      // #ifdef APP-PLUS
      // 通知服务器移除token
      await this.removeTokenFromServer(userId)
      return true
      // #endif

      return false
    } catch (error) {
      console.error('取消推送订阅失败:', error)
      return false
    }
  }

  /**
   * 处理推送消息点击
   */
  private handlePushClick(message: any): void {
    try {
      const data = typeof message.payload === 'string' 
        ? JSON.parse(message.payload) 
        : message.payload

      if (data?.type === 'chat') {
        // 跳转到聊天页面
        const isGroup = data.conversationType === 'GROUP'
        const targetId = isGroup ? data.to : data.from
        const targetName = data.nick || targetId

        const url = isGroup 
          ? `/pages/chat/chat?groupId=${targetId}&groupName=${targetName}`
          : `/pages/chat/chat?userId=${targetId}&username=${targetName}`

        uni.navigateTo({ url })
      }
    } catch (error) {
      console.error('处理推送点击失败:', error)
    }
  }

  /**
   * 处理推送消息接收
   */
  private handlePushReceive(message: any): void {
    console.log('收到推送消息:', message)
    // 可以在这里处理消息接收的逻辑
  }

  /**
   * 处理通知点击
   */
  private handleNotificationClick(message: PushMessage): void {
    if (message.data?.type === 'chat') {
      const data = message.data
      const isGroup = data.conversationType === 'GROUP'
      const targetId = isGroup ? data.to : data.from
      const targetName = data.nick || targetId

      const url = isGroup 
        ? `#/pages/chat/chat?groupId=${targetId}&groupName=${targetName}`
        : `#/pages/chat/chat?userId=${targetId}&username=${targetName}`

      window.location.hash = url
    }
  }

  /**
   * 将订阅信息发送到服务器
   */
  private async sendSubscriptionToServer(userId: string, subscription: PushSubscription): Promise<void> {
    try {
      await uni.request({
        url: '/api/v1/push/subscribe',
        method: 'POST',
        data: {
          userId,
          subscription: JSON.stringify(subscription),
          platform: 'web'
        }
      })
    } catch (error) {
      console.error('发送订阅信息到服务器失败:', error)
    }
  }

  /**
   * 将推送token发送到服务器
   */
  private async sendTokenToServer(userId: string, token: string): Promise<void> {
    try {
      await uni.request({
        url: '/api/v1/push/token',
        method: 'POST',
        data: {
          userId,
          token,
          platform: 'app'
        }
      })
    } catch (error) {
      console.error('发送推送token到服务器失败:', error)
    }
  }

  /**
   * 从服务器移除订阅
   */
  private async removeSubscriptionFromServer(userId: string): Promise<void> {
    try {
      await uni.request({
        url: '/api/v1/push/unsubscribe',
        method: 'POST',
        data: { userId }
      })
    } catch (error) {
      console.error('从服务器移除订阅失败:', error)
    }
  }

  /**
   * 从服务器移除token
   */
  private async removeTokenFromServer(userId: string): Promise<void> {
    try {
      await uni.request({
        url: '/api/v1/push/remove-token',
        method: 'POST',
        data: { userId }
      })
    } catch (error) {
      console.error('从服务器移除token失败:', error)
    }
  }

  /**
   * 转换VAPID密钥格式
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // Getters
  get hasPermission(): boolean {
    return this.permission === PushPermissionStatus.GRANTED
  }

  get permissionStatus(): PushPermissionStatus {
    return this.permission
  }
}

// 导出单例
export const pushManager = new PushManager()
export default pushManager
