<route type="home" lang="json">
{
  "style": {
    "navigationBarTitleText": "圈子",
    "enablePullDownRefresh": false,
    "navigationStyle": "custom",
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen hasBottomBar>
    <view class="bg w-full absolute h-[512rpx] overflow-hidden">
      <image
        :src="$imgUrl('/statics/images/header_bg.png')"
        class="w-full"
        mode="aspectFill"
      ></image>
    </view>
    <view class="header sticky top-0 z-20 h-[200rpx] overflow-hidden">
      <view class="w-full h-[512rpx] absolute">
        <image
          :src="$imgUrl('/statics/images/header_bg.png')"
          class="w-full h-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col justify-start pb-[30rpx]">
        <c-titlebar title="圈子"></c-titlebar>
        <!-- <view class="px-[16rpx] py-[20rpx]">
          <c-search></c-search>
        </view> -->
      </view>
    </view>
    <view class="relative z-10">
      <view
        class="card flex-row items-center bg-white m-sm rounded-[30rpx] h-[214rpx]"
        hover-class="hover-bg"
        @tap="toCliqueList('carSource')"
      >
        <view class="left flex-1 ml-5xl">
          <view class="flex-row items-center mb-base">
            <text class="text-[40rpx] font-bold text-title">车源圈</text>
            <c-icon type="jinru" size="32" color="#1a1a1a"></c-icon>
          </view>
          <view>
            <text class="text-base text-secondary">一键发布车源，快速匹配买家！</text>
          </view>
        </view>
        <view class="right">
          <image
            :src="$imgUrl('/statics/images/car1.png')"
            class="w-[180rpx] h-[180rpx]"
            mode="widthFix"
          ></image>
        </view>
      </view>
      <view
        class="card flex-row items-center bg-white m-sm rounded-[30rpx] h-[214rpx]"
        hover-class="hover-bg"
        @tap="toCliqueList('demand')"
      >
        <view class="left flex-1 ml-5xl">
          <view class="flex-row items-center mb-base">
            <text class="text-[40rpx] font-bold text-title">求购圈</text>
            <c-icon type="jinru" size="32" color="#1a1a1a"></c-icon>
          </view>
          <view>
            <text class="text-base text-secondary">买二手车？发布需求，好车速达！</text>
          </view>
        </view>
        <view class="right">
          <image
            :src="$imgUrl('/statics/images/car2.png')"
            class="w-[180rpx] h-[180rpx]"
            mode="widthFix"
          ></image>
        </view>
      </view>
      <view
        class="card flex-row items-center bg-white m-sm rounded-[30rpx] h-[214rpx]"
        hover-class="hover-bg"
        @tap="toCliqueList('friend')"
      >
        <view class="left flex-1 ml-5xl">
          <view class="flex-row items-center mb-base">
            <text class="text-[40rpx] font-bold text-title">朋友圈</text>
            <c-icon type="jinru" size="32" color="#1a1a1a"></c-icon>
          </view>
          <view>
            <text class="text-base text-secondary">分享生活点滴，一键发布动态！</text>
          </view>
        </view>
        <view class="right mr-xs">
          <image
            :src="$imgUrl('/statics/images/friend.png')"
            class="w-[140rpx] h-[140rpx]"
            mode="widthFix"
          ></image>
        </view>
      </view>
    </view>
    <c-tabbar></c-tabbar>
  </c-page>
</template>

<script setup lang="ts">
import { navigateTo } from '@/utils/util'

// import { navigateTo } from '@/utils/util'
const toCliqueList = function (type) {
  navigateTo({
    path: '/clique/list',
    query: {
      type,
    },
  })
}
</script>

<style lang="scss">
.hover-bg {
  background-color: $bg-color;
}
</style>
