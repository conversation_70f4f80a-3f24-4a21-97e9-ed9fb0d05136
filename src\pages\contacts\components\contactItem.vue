<template>
  <view class="flex-row items-center p-[10rpx] mb-[20rpx] bg-white" @click="openProfile">
    <!-- 头像 -->
    <view class="avatar-cell w-[100rpx] h-[100rpx] rounded-[50rpx] overflow-hidden bg-light mr-[20rpx]">
      <image
        :src="props.data.avatar || '/static/images/user/default_avatar.png'"
        class="w-[100%] h-[100%]"
      ></image>
    </view>
    
    <!-- 内容区域 -->
    <view class="content flex-1 overflow-hidden">
      <view class="title mb-[10rpx]">
        <text class="text-lg">{{ props.data.username }}</text>
      </view>
      <view v-if="props.data.remark" class="remark">
        <text class="text-[24rpx] text-muted">备注: {{ props.data.remark }}</text>
      </view>
    </view>
    
    <!-- 好友标识 -->
    <view v-if="props.data.isFriend" class="friend-badge mr-[12rpx]">
      <view class="bg-green-100 text-green-600 text-[20rpx] px-[8rpx] py-[4rpx] rounded-[12rpx]">
        好友
      </view>
    </view>
    
    <!-- 更多操作 -->
    <view class="ml-[12rpx]">
      <c-icon type="gengduo" color="#999999"></c-icon>
    </view>
  </view>
</template>

<script setup lang="ts">
import { type PropType } from 'vue'

interface IContact {
  id: string
  avatar: string
  username: string
  remark?: string
  isFriend: boolean
}

const props = defineProps({
  data: {
    type: Object as PropType<IContact>,
    default: () => ({}),
  },
})

const openProfile = () => {
  // 跳转到用户详情页面
  uni.navigateTo({
    url: `/pages/user/profile?userId=${props.data.id}`
  })
}
</script>

<style scoped lang="scss">
.friend-badge {
  .bg-green-100 {
    background-color: #dcfce7;
  }
  .text-green-600 {
    color: #16a34a;
  }
}
</style>
