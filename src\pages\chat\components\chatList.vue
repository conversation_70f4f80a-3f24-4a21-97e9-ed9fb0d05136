<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <view class="flex-row items-center justify-between mb-[24rpx]">
      <view class="flex-row h-[66rpx] items-center p-[6rpx] rounded-[24rpx] bg-[#F4F6FA]">
        <view
          class="justify-center items-center w-[94rpx] h-full rounded-[20rpx]"
          :class="[state.queryForm.status === 'all' ? 'bg-white' : '']"
          @click="switchStatus('all')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'all' ? 'bg-white text-title' : 'text-muted']"
            >全部</text
          >
        </view>
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'unread' ? 'bg-white' : '']"
          @click="switchStatus('unread')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'unread' ? 'bg-white text-title' : 'text-muted']"
            >未读</text
          >
        </view>
      </view>
      <view class="flex-row w-[166rpx] h-[56rpx] rounded-[28rpx] items-center justify-center btn">
        <c-icon size="28" type="qingchu" color="#999999"></c-icon>
        <text class="ml-[10rpx] text-[24rpx] text-muted">一键已读</text>
      </view>
    </view>
    <chatListItem v-for="item in state.dataList" :key="item.id" :data="item"></chatListItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import type { IChatListItem } from 'types/chat.interface'
import chatListItem from './chatListItem.vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
type Status = 'all' | 'unread'
const state = reactive({
  currentTab: 'carSource',
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all' as Status,
  },
  dataList: [] as IChatListItem[],
})
const dataList: IChatListItem[] = [
  {
    id: '1',
    avatar: '',
    title: '福特车源服务交流群',
    message: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    unready: 16,
  },
  {
    id: '2',
    avatar: '',
    title: '福特车源服务交流群',
    message: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    unready: 16,
  },
  {
    id: '3',
    avatar: '',
    title: '福特车源服务交流群',
    message: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    unready: 16,
  },
  {
    id: '4',
    avatar: '',
    title: '福特车源服务交流群',
    message: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    unready: 16,
  },
  {
    id: '5',
    avatar: '',
    title: '福特车源服务交流群',
    message: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    unready: 16,
  },
]

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((reslove) => {
    setTimeout(() => {
      const _dataList = cloneDeep(dataList)
      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      reslove(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}
// getData()
const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

const switchStatus = function (status: Status) {
  state.queryForm.status = status
  initData()
}

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss">
.btn {
  border: 1rpx solid #f4f6fa;
}
</style>
