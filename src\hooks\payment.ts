import { createOrder } from '@/api/vip.api'
import { message } from '@/utils/util'

export default function usePayment() {
  const handlePay = async function (
    provider: 'wxpay' | 'alipay',
    reqOrderInfo: { goodsId?: string | number } = {}
  ) {
    let orderInfo
    try {
      let applicationType = 'app'
      // #ifdef MP-WEIXIN
      applicationType = 'mpweixin'
      // #endif
      // #ifdef APP
      applicationType = 'app'
      // #endif
      // console.log({ payChannel: provider, applicationType, ...reqOrderInfo })
      const res = await createOrder({ payChannel: provider, applicationType, ...reqOrderInfo })
      if (res.code === 0) {
        if (res.data.params) {
          orderInfo = res.data.params
        }
        console.log(orderInfo)
      } else {
        throw new Error('创建订单失败!')
      }
    } catch (error) {
      console.log(error)
      throw new Error('创建订单失败!')
    }
    // #ifdef APP || MP
    return new Promise((resolve, reject) => {
      // #ifdef APP
      uni.requestPayment({
        provider: provider,
        orderInfo: orderInfo,
        fail: (err) => {
          reject(err)
        },
        success: (res) => {
          resolve(res)
        },
      })
      // #endif
      // #ifdef MP
      uni.requestPayment({
        provider: provider,
        ...orderInfo,
        fail: (err) => {
          reject(err)
        },
        success: (res) => {
          resolve(res)
        },
      })
      // #endif
    })
    // #endif
  }
  return {
    handlePay,
    createOrder,
  }
}
