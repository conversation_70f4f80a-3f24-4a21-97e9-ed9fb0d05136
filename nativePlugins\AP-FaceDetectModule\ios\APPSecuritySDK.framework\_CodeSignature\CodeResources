<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>APPSecuritySDK-Info.plist</key>
		<data>
		Po//BA5tM1BE58borc33LNpqXyw=
		</data>
		<key>Headers/APDID.h</key>
		<data>
		YaNffM20hdQMiNpnIoEPIHF8o+A=
		</data>
		<key>Headers/APDeviceColor.h</key>
		<data>
		sqZwHJLZ8j2L+9pkCmTqWHaRPoo=
		</data>
		<key>Headers/APDynamic.h</key>
		<data>
		TBp9H7ZpcLh5hWGFJbObSnR3sSQ=
		</data>
		<key>Headers/APRiskConfigReportRequest.h</key>
		<data>
		5vPyFFL4v0DrNrSc35DrU0204M4=
		</data>
		<key>Headers/APRiskConfigReportResponse.h</key>
		<data>
		WODwFqptygg++OMfRxg5nJhV1tM=
		</data>
		<key>Headers/APRiskConfigServiceProtocol.h</key>
		<data>
		SJJczKTD81Beaeutnk5eh5yYk6s=
		</data>
		<key>Headers/APRiskFileUploadProtocol.h</key>
		<data>
		LLqKfgEm/gVHeBO+chBoA32ce6w=
		</data>
		<key>Headers/APRiskLoggerProtocol.h</key>
		<data>
		KaoFaIX0tcWToF7p3GQSPdm1fNI=
		</data>
		<key>Headers/APRiskRPCProtocol.h</key>
		<data>
		/OdHTVHBuEGxpO5dJbde5mXoV3Q=
		</data>
		<key>Headers/APRiskRemoteLoggerProtocol.h</key>
		<data>
		L9Ca3T+cAEzjRZxJTwHTaKytgQs=
		</data>
		<key>Headers/APRiskReportRequest.h</key>
		<data>
		p0bXoJMwS+46ZRrgLqdvbEFjAmI=
		</data>
		<key>Headers/APRiskReportResponse.h</key>
		<data>
		3pzJU3RgAQBnPg/R2tEjuGJLfvc=
		</data>
		<key>Headers/APRiskRpcConfiguration.h</key>
		<data>
		ypma2woz2iRq5pghoIkfFTGVm+8=
		</data>
		<key>Headers/APRiskSyncHandlerProtocol.h</key>
		<data>
		Vum7s9ObS3JRBHwQBG4BUdk6SN8=
		</data>
		<key>Headers/APRiskSyncServiceProtocol.h</key>
		<data>
		V0kCGcKZFd1gG+a2uuDzVkzj218=
		</data>
		<key>Headers/APSecRDS.h</key>
		<data>
		ZKM/B2Jcl/et+wyK8FepeewKCi8=
		</data>
		<key>Headers/APSecStore.h</key>
		<data>
		rEuJJetfP9Aj7GLQd5R3IgSZw/c=
		</data>
		<key>Headers/APSecureSdk.h</key>
		<data>
		zA/Cjy1JENcHldp4rH8g7GNkvnY=
		</data>
		<key>Headers/APSign.h</key>
		<data>
		UxWq8miIIfr3zl7GiKnKdG8PS5s=
		</data>
		<key>Headers/ASSCommon.h</key>
		<data>
		lrrlG7ZqNaz4ciXX/i5kj/NnG3A=
		</data>
		<key>Headers/ASSDynamicManager.h</key>
		<data>
		4Vk+CxCMPtR9isbOl7FqtE5jZuo=
		</data>
		<key>Headers/ASSLogger.h</key>
		<data>
		WvM9Fp3B5ZaI8D+rnHiUcBTxsdk=
		</data>
		<key>Headers/ASSSecureInfo.h</key>
		<data>
		C7uQ4VE5uwtF5ws1blMIKLKpfmI=
		</data>
		<key>Headers/ASSSecureOpenSdk.h</key>
		<data>
		KmdnMxfQN0cqG23Vuu092URfYCs=
		</data>
		<key>Headers/ASSSgomInfoOpen.h</key>
		<data>
		/AhECuNKFdWPVi1Q6oH8rMN66XM=
		</data>
		<key>Headers/ASSTokenResult.h</key>
		<data>
		X5xdpK/2lE52tWoVFEg/ZivE9UQ=
		</data>
		<key>Headers/AntSecurityManager.h</key>
		<data>
		w5tB5gEPNoVoucc6rkAlUnUhzV4=
		</data>
		<key>Info.plist</key>
		<data>
		fCkWnN84BgeGq/LGq3C5PF1vL7M=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>module.map</key>
		<data>
		nJxa72k41Z2wyhSJqqJgUyYvSgA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>APPSecuritySDK-Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Po//BA5tM1BE58borc33LNpqXyw=
			</data>
			<key>hash2</key>
			<data>
			B2XyqSNoreUIQ8uDojzpys2o9qi8XuLbypPrkCR+l8w=
			</data>
		</dict>
		<key>Headers/APDID.h</key>
		<dict>
			<key>hash</key>
			<data>
			YaNffM20hdQMiNpnIoEPIHF8o+A=
			</data>
			<key>hash2</key>
			<data>
			0nNHe4UB7qz6XFIk/QzRt79MyXYVEOukTfqa3YBJYbY=
			</data>
		</dict>
		<key>Headers/APDeviceColor.h</key>
		<dict>
			<key>hash</key>
			<data>
			sqZwHJLZ8j2L+9pkCmTqWHaRPoo=
			</data>
			<key>hash2</key>
			<data>
			c/mo/+N/KECjwx+fLMepqE+OqRtyfnn2hHshYhmO9IU=
			</data>
		</dict>
		<key>Headers/APDynamic.h</key>
		<dict>
			<key>hash</key>
			<data>
			TBp9H7ZpcLh5hWGFJbObSnR3sSQ=
			</data>
			<key>hash2</key>
			<data>
			tUrg2Z43qGA67I0mIGCqRWU6Gc2I9Yx9mn5e2mu9XQU=
			</data>
		</dict>
		<key>Headers/APRiskConfigReportRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			5vPyFFL4v0DrNrSc35DrU0204M4=
			</data>
			<key>hash2</key>
			<data>
			OMGRtuphIpujn+dr+tCsCMIUw+KV8WQ2zE+bsdDUVCw=
			</data>
		</dict>
		<key>Headers/APRiskConfigReportResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			WODwFqptygg++OMfRxg5nJhV1tM=
			</data>
			<key>hash2</key>
			<data>
			NvTd6RhL7dbwHKHDPiqBqkIc9aWUQLV5SwFFsEorcqg=
			</data>
		</dict>
		<key>Headers/APRiskConfigServiceProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			SJJczKTD81Beaeutnk5eh5yYk6s=
			</data>
			<key>hash2</key>
			<data>
			RyMjAfHvdfRW5n+MDPHVlPZ6VM0Kr/T2A133j9ytwDQ=
			</data>
		</dict>
		<key>Headers/APRiskFileUploadProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			LLqKfgEm/gVHeBO+chBoA32ce6w=
			</data>
			<key>hash2</key>
			<data>
			b2wnReDRJkaR7JxbLjLQXdNsfQbd29tkXtSh/5sjuGo=
			</data>
		</dict>
		<key>Headers/APRiskLoggerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			KaoFaIX0tcWToF7p3GQSPdm1fNI=
			</data>
			<key>hash2</key>
			<data>
			k2gnCevVtGYZ1cgzxh9FrAlnVnnDzD/3l8lIuDPVuDc=
			</data>
		</dict>
		<key>Headers/APRiskRPCProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			/OdHTVHBuEGxpO5dJbde5mXoV3Q=
			</data>
			<key>hash2</key>
			<data>
			1v7HTHTTRf+k61+lf5L1URwSh/MFv72gdnHxTodGrJA=
			</data>
		</dict>
		<key>Headers/APRiskRemoteLoggerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			L9Ca3T+cAEzjRZxJTwHTaKytgQs=
			</data>
			<key>hash2</key>
			<data>
			BmYD66C5squNiQNaLya1JrWDDCJ9V8w6C+AVGpr8QF4=
			</data>
		</dict>
		<key>Headers/APRiskReportRequest.h</key>
		<dict>
			<key>hash</key>
			<data>
			p0bXoJMwS+46ZRrgLqdvbEFjAmI=
			</data>
			<key>hash2</key>
			<data>
			XH1Y2gtlfBaBoOANsxALY7w6fBn81SMhxHKHNVbEFcQ=
			</data>
		</dict>
		<key>Headers/APRiskReportResponse.h</key>
		<dict>
			<key>hash</key>
			<data>
			3pzJU3RgAQBnPg/R2tEjuGJLfvc=
			</data>
			<key>hash2</key>
			<data>
			PAxPqKwwsKVZNGSWI01WquBx9DZcg0tTsnYe8/UwlAM=
			</data>
		</dict>
		<key>Headers/APRiskRpcConfiguration.h</key>
		<dict>
			<key>hash</key>
			<data>
			ypma2woz2iRq5pghoIkfFTGVm+8=
			</data>
			<key>hash2</key>
			<data>
			xucoo82E5P13oVpu65VVAqbLK11kcVmtbwRCVlEQI5E=
			</data>
		</dict>
		<key>Headers/APRiskSyncHandlerProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			Vum7s9ObS3JRBHwQBG4BUdk6SN8=
			</data>
			<key>hash2</key>
			<data>
			4wA6oLvZ4U8u4Ff5SSuprJISDBd1X+Zx5MqlV0aGoik=
			</data>
		</dict>
		<key>Headers/APRiskSyncServiceProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			V0kCGcKZFd1gG+a2uuDzVkzj218=
			</data>
			<key>hash2</key>
			<data>
			oSMsgkxXfg8rdX0ZWmUqRmlTF1fgLfINWgGqYcBeMfI=
			</data>
		</dict>
		<key>Headers/APSecRDS.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZKM/B2Jcl/et+wyK8FepeewKCi8=
			</data>
			<key>hash2</key>
			<data>
			N3/Ych+ScRXiaFWFlsCWXqyGbWdqHhBCHpyp0sQFYPc=
			</data>
		</dict>
		<key>Headers/APSecStore.h</key>
		<dict>
			<key>hash</key>
			<data>
			rEuJJetfP9Aj7GLQd5R3IgSZw/c=
			</data>
			<key>hash2</key>
			<data>
			R7UHcZTpvTV6HWYJm1bEji6L2ry1wSoCprSTRHEqP0M=
			</data>
		</dict>
		<key>Headers/APSecureSdk.h</key>
		<dict>
			<key>hash</key>
			<data>
			zA/Cjy1JENcHldp4rH8g7GNkvnY=
			</data>
			<key>hash2</key>
			<data>
			TYDA7YfV7+iBX1UXlWID3/MpbnlQxjL0vt+aAGKO0Vo=
			</data>
		</dict>
		<key>Headers/APSign.h</key>
		<dict>
			<key>hash</key>
			<data>
			UxWq8miIIfr3zl7GiKnKdG8PS5s=
			</data>
			<key>hash2</key>
			<data>
			Te3d2WjOmORK+v6JoH87htyWMIRPvOVgrOi25YxMBWY=
			</data>
		</dict>
		<key>Headers/ASSCommon.h</key>
		<dict>
			<key>hash</key>
			<data>
			lrrlG7ZqNaz4ciXX/i5kj/NnG3A=
			</data>
			<key>hash2</key>
			<data>
			adc/BhjRDcEbnGdNnfGBsTu2jTNZbT2Csi+oHGqx2oY=
			</data>
		</dict>
		<key>Headers/ASSDynamicManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			4Vk+CxCMPtR9isbOl7FqtE5jZuo=
			</data>
			<key>hash2</key>
			<data>
			lMtcIcfdivy04/miLJ4swfqpcY6/+5XYbnQMhMiToRw=
			</data>
		</dict>
		<key>Headers/ASSLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			WvM9Fp3B5ZaI8D+rnHiUcBTxsdk=
			</data>
			<key>hash2</key>
			<data>
			JNJIdG9385MDYXjB+F1Mj1NXHTx8faIKEft1KjkceeI=
			</data>
		</dict>
		<key>Headers/ASSSecureInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			C7uQ4VE5uwtF5ws1blMIKLKpfmI=
			</data>
			<key>hash2</key>
			<data>
			VS1+TQV7GpG7Z2dfhSkfmGi3KjE6cGBUxof1/s+bLPo=
			</data>
		</dict>
		<key>Headers/ASSSecureOpenSdk.h</key>
		<dict>
			<key>hash</key>
			<data>
			KmdnMxfQN0cqG23Vuu092URfYCs=
			</data>
			<key>hash2</key>
			<data>
			Xq/bbsjzFy4rMCr4osH17Nif1NZQqxmWKvgRHq4THr0=
			</data>
		</dict>
		<key>Headers/ASSSgomInfoOpen.h</key>
		<dict>
			<key>hash</key>
			<data>
			/AhECuNKFdWPVi1Q6oH8rMN66XM=
			</data>
			<key>hash2</key>
			<data>
			j8Lnl+QqgD+rwdHhl7yEh7Op76XuicUYflL4/LNrFXE=
			</data>
		</dict>
		<key>Headers/ASSTokenResult.h</key>
		<dict>
			<key>hash</key>
			<data>
			X5xdpK/2lE52tWoVFEg/ZivE9UQ=
			</data>
			<key>hash2</key>
			<data>
			7O9oGJk95fMdxJyn2mOlI1Iqe1vPcmPRvP5Rgj8J9zk=
			</data>
		</dict>
		<key>Headers/AntSecurityManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			w5tB5gEPNoVoucc6rkAlUnUhzV4=
			</data>
			<key>hash2</key>
			<data>
			5QS2NUN5NsRJ5dYsLrHlgLY/GLXNwZDSsR5gN4lin6k=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>module.map</key>
		<dict>
			<key>hash</key>
			<data>
			nJxa72k41Z2wyhSJqqJgUyYvSgA=
			</data>
			<key>hash2</key>
			<data>
			XRW9liRaYf8O2c1bSPVShfimQ3Yfcd1SbIKvIE+sv7c=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
