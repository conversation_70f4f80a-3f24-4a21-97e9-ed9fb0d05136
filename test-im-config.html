<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>IM配置测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .config-item {
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #007bff;
      }
      .config-label {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
      }
      .config-value {
        font-family: monospace;
        color: #666;
        word-break: break-all;
      }
      .status {
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      button:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }
      .test-section {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
      }
      .test-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
      }
      input[type='text'] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 5px 0;
      }
      .result {
        margin: 10px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔧 IM配置测试工具</h1>
      <p>用于测试腾讯云IM配置是否正确</p>

      <!-- 当前配置显示 -->
      <div class="test-section">
        <div class="test-title">📋 当前配置信息</div>

        <div class="config-item">
          <div class="config-label">SDKAppID:</div>
          <div class="config-value" id="sdkAppId">1600101880</div>
        </div>

        <div class="config-item">
          <div class="config-label">服务器地址:</div>
          <div class="config-value" id="serverUrl">http://***************/api/carcityim/im</div>
        </div>

        <div class="config-item">
          <div class="config-label">SecretKey (后端):</div>
          <div class="config-value">
            bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">过期时间:</div>
          <div class="config-value">5184000 秒 (60天)</div>
        </div>
      </div>

      <!-- 服务器连通性测试 -->
      <div class="test-section">
        <div class="test-title">🌐 服务器连通性测试</div>

        <button onclick="testServerConnection()">测试服务器连接</button>
        <button onclick="testHealthCheck()">健康检查</button>

        <div id="connectionResult" class="result" style="display: none"></div>
      </div>

      <!-- UserSig获取测试 -->
      <div class="test-section">
        <div class="test-title">🔑 UserSig获取测试</div>

        <div style="margin-bottom: 10px">
          <label>选择测试用户:</label>
          <select
            id="userSelect"
            onchange="updateUserInput()"
            style="margin-left: 10px; padding: 5px"
          >
            <option value="">-- 选择用户 --</option>
            <option value="administrator">administrator (管理员)</option>
            <option value="19573461517477745">19573461517477745 (达哥哥1)</option>
            <option value="19573462910530810">19573462910530810 (达哥哥2)</option>
            <option value="test_user_001">test_user_001 (测试用户)</option>
          </select>
        </div>
        <div>
          <label>测试用户ID:</label>
          <input
            type="text"
            id="testUserId"
            value="test_user_001"
            placeholder="输入用户ID或从上方选择"
          />
        </div>

        <button onclick="testGetUserSig()">获取UserSig</button>
        <button onclick="fillTestData()">填充测试数据</button>

        <div id="userSigResult" class="result" style="display: none"></div>
      </div>

      <!-- IM SDK测试 -->
      <div class="test-section">
        <div class="test-title">📱 IM SDK测试</div>

        <div id="sdkStatus" class="status warning">SDK未初始化</div>

        <button onclick="testSDKInit()">初始化SDK</button>
        <button onclick="testIMLogin()" id="loginBtn" disabled>测试登录</button>

        <div id="sdkResult" class="result" style="display: none"></div>
      </div>

      <!-- 当前状态 -->
      <div class="test-section">
        <div class="test-title">📊 当前状态分析</div>
        <div class="status warning">
          <strong>根据控制台错误信息分析:</strong><br />
          ❌ <strong>错误</strong>: "The UserSig in use is illegal. Please regenerate UserSig
          through official API."<br /><br />
          <strong>可能原因:</strong><br />
          1. 后端服务未启动 - 无法获取有效的UserSig<br />
          2. 接口路径不匹配 - 需要确保后端实现了 /api/carcityim/im/getUserSig 接口<br />
          3. UserSig生成失败 - 后端SecretKey配置错误<br />
          4. CORS跨域问题 - 后端未正确配置跨域访问<br /><br />
          <strong>解决步骤:</strong><br />
          1. 先测试服务器连接<br />
          2. 确认后端接口正常工作<br />
          3. 获取有效的UserSig<br />
          4. 再进行IM登录测试
        </div>
      </div>

      <!-- 配置建议 -->
      <div class="test-section">
        <div class="test-title">💡 配置建议</div>
        <div class="status warning">
          <strong>重要提醒:</strong><br />
          1. SecretKey 必须保存在后端服务器，不能在前端代码中出现<br />
          2. UserSig 必须通过后端接口获取，不能在前端生成<br />
          3. 生产环境必须使用 HTTPS 协议<br />
          4. 建议设置合理的 UserSig 过期时间<br /><br />
          <strong>后端接口路径:</strong><br />
          • GET /api/carcityim/im/getUserSig?userID=xxx<br />
          • GET /api/carcityim/im/config<br />
          • GET /api/carcityim/im/health
        </div>
      </div>
    </div>

    <script>
      let currentUserSig = ''
      let tim = null

      // 更新用户输入框
      function updateUserInput() {
        const select = document.getElementById('userSelect')
        const input = document.getElementById('testUserId')
        if (select.value) {
          input.value = select.value
        }
      }

      // 测试服务器连接
      async function testServerConnection() {
        const resultDiv = document.getElementById('connectionResult')
        resultDiv.style.display = 'block'
        resultDiv.textContent = '正在测试服务器连接...'

        try {
          const response = await fetch('http://***************/api/carcityim/im/config', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })

          if (response.ok) {
            const data = await response.json()
            resultDiv.textContent = `✅ 服务器连接成功!\n请求路径: http://***************/api/carcityim/im/config\n响应数据: ${JSON.stringify(
              data,
              null,
              2
            )}`
            resultDiv.className = 'result status success'
          } else {
            resultDiv.textContent = `❌ 服务器连接失败!\n状态码: ${response.status}\n状态文本: ${response.statusText}\n\n请检查:\n1. 后端服务是否启动\n2. 接口路径是否正确: /api/carcityim/im/config\n3. CORS配置是否正确`
            resultDiv.className = 'result status error'
          }
        } catch (error) {
          resultDiv.textContent = `❌ 服务器连接异常!\n错误信息: ${error.message}\n\n可能的原因:\n1. 服务器未启动\n2. 网络连接问题\n3. CORS跨域问题\n4. 防火墙阻止`
          resultDiv.className = 'result status error'
        }
      }

      // 健康检查
      async function testHealthCheck() {
        const resultDiv = document.getElementById('connectionResult')
        resultDiv.style.display = 'block'
        resultDiv.textContent = '正在进行健康检查...'

        try {
          const response = await fetch('http://***************/api/carcityim/im/health', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })

          if (response.ok) {
            const data = await response.json()
            resultDiv.textContent = `✅ 健康检查通过!\n响应数据: ${JSON.stringify(data, null, 2)}`
            resultDiv.className = 'result status success'
          } else {
            resultDiv.textContent = `❌ 健康检查失败!\n状态码: ${response.status}`
            resultDiv.className = 'result status error'
          }
        } catch (error) {
          resultDiv.textContent = `❌ 健康检查异常!\n错误信息: ${error.message}`
          resultDiv.className = 'result status error'
        }
      }

      // 测试获取UserSig
      async function testGetUserSig() {
        const userId = document.getElementById('testUserId').value
        const resultDiv = document.getElementById('userSigResult')

        if (!userId.trim()) {
          alert('请输入用户ID')
          return
        }

        resultDiv.style.display = 'block'
        resultDiv.textContent = '正在获取UserSig...'

        try {
          const response = await fetch(
            `http://***************/api/carcityim/im/getUserSig?userID=${userId}`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            }
          )

          if (response.ok) {
            const data = await response.json()
            if (data.code === 0) {
              currentUserSig = data.data.userSig
              resultDiv.textContent = `✅ UserSig获取成功!\n用户ID: ${data.data.userID}\nUserSig: ${
                data.data.userSig
              }\n过期时间: ${new Date(data.data.expireTime).toLocaleString()}`
              resultDiv.className = 'result status success'
              document.getElementById('loginBtn').disabled = false
            } else {
              resultDiv.textContent = `❌ UserSig获取失败!\n错误码: ${data.code}\n错误信息: ${data.message}`
              resultDiv.className = 'result status error'
            }
          } else {
            resultDiv.textContent = `❌ 请求失败!\n状态码: ${response.status}`
            resultDiv.className = 'result status error'
          }
        } catch (error) {
          resultDiv.textContent = `❌ 请求异常!\n错误信息: ${error.message}`
          resultDiv.className = 'result status error'
        }
      }

      // 填充测试数据
      function fillTestData() {
        document.getElementById('testUserId').value = 'test_user_' + Date.now()
      }

      // 测试SDK初始化
      function testSDKInit() {
        const statusDiv = document.getElementById('sdkStatus')
        const resultDiv = document.getElementById('sdkResult')

        try {
          // 这里只是模拟，实际需要引入TIM SDK
          statusDiv.textContent = '✅ SDK初始化成功 (模拟)'
          statusDiv.className = 'status success'

          resultDiv.style.display = 'block'
          resultDiv.textContent = `SDK初始化参数:\nSDKAppID: 1600101880\n时间: ${new Date().toLocaleString()}`
          resultDiv.className = 'result'
        } catch (error) {
          statusDiv.textContent = '❌ SDK初始化失败'
          statusDiv.className = 'status error'

          resultDiv.style.display = 'block'
          resultDiv.textContent = `初始化失败: ${error.message}`
          resultDiv.className = 'result status error'
        }
      }

      // 测试IM登录
      function testIMLogin() {
        const userId = document.getElementById('testUserId').value
        const resultDiv = document.getElementById('sdkResult')

        if (!currentUserSig) {
          alert('请先获取UserSig')
          return
        }

        resultDiv.style.display = 'block'
        resultDiv.textContent = `正在测试IM登录...\n用户ID: ${userId}\nUserSig: ${currentUserSig.substring(
          0,
          50
        )}...`

        // 这里只是模拟，实际需要真正的IM SDK
        setTimeout(() => {
          resultDiv.textContent = `✅ IM登录测试完成 (模拟)\n用户ID: ${userId}\n登录时间: ${new Date().toLocaleString()}\n\n注意: 这只是模拟测试，实际登录需要在真实的IM环境中进行`
          resultDiv.className = 'result status success'
        }, 2000)
      }

      // 页面加载时自动检查配置
      window.onload = function () {
        console.log('IM配置测试工具已加载')
        console.log('SDKAppID:', 1600101880)
        console.log('服务器地址:', 'http://***************/api/carcityim/im')
      }
    </script>
  </body>
</html>
