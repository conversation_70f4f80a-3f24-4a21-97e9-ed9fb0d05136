import { getToken } from '@/utils/auth'
import { routes } from './routes'
// import { navigateTo } from '@/utils/util'

const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
list.forEach((item) => {
  uni.addInterceptor(item, {
    invoke(e) {
      // 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
      const url = e.url.split('?')[0]
      const currentRoute = routes.find((item) => {
        return url === item.path
      })
      // 不需要登录
      if (!currentRoute?.auth) {
        return e
      }
      // 需要登录并且没有token
      if (currentRoute?.auth && !getToken()) {
        // navigateTo('/user/authLogin')
        // #ifdef MP
        uni.navigateTo({
          url: '/user/authLogin',
        })
        // #endif
        // #ifndef MP-WEIXIN
        uni.navigateTo({
          url: '/user/accountLogin',
        })
        // #endif
        return false
      }
      return e
    },
    fail(err) {
      // 失败回调拦截
      console.log(err)
    },
  })
})
