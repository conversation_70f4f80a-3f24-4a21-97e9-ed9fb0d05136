{
    "extends": "@vue/tsconfig/tsconfig.json",
    "compilerOptions": {
        "target": "esnext",
        "useDefineForClassFields": true,
        "module": "esnext",
        "noImplicitAny": false,
        "moduleResolution": "node",
        "strict": true,
        "jsx": "preserve",
        "sourceMap": true,
        "resolveJsonModule": true,
        "esModuleInterop": true,
        "lib": [
            "esnext",
            "dom"
        ],
        "types": [
            "@dcloudio/types"
        ],
        "isolatedModules": true,
        "baseUrl": ".",
        // 关闭仅类型导入
        // "verbatimModuleSyntax": false,
        "paths": {
            "@/*": [
                "./src/*"
            ],
            "/$/*": [
                "./src/uni_modules/*"
            ]
        }
    },
    "include": [
        "types/*.ts",
        "src/**/*.ts",
        "src/**/*.d.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "typings/**/*.d.ts",
        "../admin/src/api/finance.ts"
    ],
    "exclude": [
        "node_modules",
        "dist"
    ]
}