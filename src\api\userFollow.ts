/**
 * 用户关注管理API
 */
import request from '@/utils/request'

export interface UserFollow {
  id: string
  userId: string
  followUserId: string
  followUserName: string
  followUserAvatar?: string
  remarkName?: string
  isDoNotDisturb: boolean
  isBlocked: boolean
  followTime: string
  updateTime?: string
  status: 'active' | 'cancelled' | 'blocked'
  isMutualFollow: boolean
  groupId?: string // 分组ID
  groupName?: string // 分组名称
  tags?: string[] // 标签
}

export interface UserFollowResponse {
  code: number
  message: string
  data: UserFollow
}

export interface UserFollowListResponse {
  code: number
  message: string
  data: UserFollow[]
  total?: number
}

export interface UserFollowPageParams {
  current?: number
  size?: number
  userId?: string
  followUserId?: string
  keyword?: string
  status?: string
  groupId?: string
  isDoNotDisturb?: boolean
  isMutualFollow?: boolean
}

export interface FollowGroup {
  id: string
  userId: string
  groupName: string
  groupOrder: number
  memberCount: number
  createTime: string
}

/**
 * 修改用户关注
 */
export const updateUserFollow = (followId: string, data: Partial<UserFollow>) => {
  return request.put<{ code: number; message: string }>({
    url: '/imUserFollow',
    data: {
      id: followId,
      ...data,
    },
  })
}

/**
 * 通过id删除用户关注
 */
export const deleteUserFollow = (followId: string) => {
  return request.delete<{ code: number; message: string }>({
    url: '/imUserFollow',
    data: {
      id: followId,
    },
  })
}

/**
 * 修改好友关注
 */
export const updateFollowRemarkName = (
  userId: string,
  followUserId: string,
  remarkName: string
) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/updateRemarkName',
    data: {
      userId,
      followUserId,
      remarkName,
    },
  })
}

/**
 * 好友关注设置免打扰
 */
export const setFollowDoNotDisturb = (
  userId: string,
  followUserId: string,
  doNotDisturb: boolean
) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/noDisturbingConfig',
    data: {
      userId,
      followUserId,
      doNotDisturb,
    },
  })
}

/**
 * 批量导入关注
 */
export const importUserFollows = (follows: Array<Partial<UserFollow>>) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/import',
    data: {
      follows,
    },
  })
}

/**
 * 取消用户关注
 */
export const cancelUserFollow = (userId: string, followUserId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/imUserFollowCancel',
    data: {
      userId,
      followUserId,
    },
  })
}

/**
 * 新增用户关注
 */
export const addUserFollow = (
  userId: string,
  followUserId: string,
  remarkName?: string,
  groupId?: string
) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/imUserFollowAdd',
    data: {
      userId,
      followUserId,
      remarkName,
      groupId,
    },
  })
}

/**
 * 好友朋友关闭设置免打扰
 */
export const cancelFollowDoNotDisturb = (userId: string, followUserId: string) => {
  return request.post<{ code: number; message: string }>({
    url: '/imUserFollow/cancelNoDisturbingConfig',
    data: {
      userId,
      followUserId,
    },
  })
}

/**
 * 分页查询用户关注表
 */
export const getUserFollowsPage = (params: UserFollowPageParams = {}) => {
  return request.get<UserFollowListResponse>({
    url: '/imUserFollow/page',
    data: {
      current: params.current || 1,
      size: params.size || 20,
      userId: params.userId,
      followUserId: params.followUserId,
      keyword: params.keyword,
      status: params.status,
      groupId: params.groupId,
      isDoNotDisturb: params.isDoNotDisturb,
      isMutualFollow: params.isMutualFollow,
    },
  })
}

/**
 * 查询好友朋友设置
 */
export const getFriendsConfig = (userId: string, followUserId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      remarkName?: string
      isDoNotDisturb: boolean
      groupId?: string
      groupName?: string
      tags?: string[]
      isMutualFollow: boolean
    }
  }>({
    url: '/imUserFollow/getFriendsConfig',
    data: {
      userId,
      followUserId,
    },
  })
}

/**
 * 导出关注列表
 */
export const exportUserFollows = (params?: UserFollowPageParams) => {
  return request<Blob>({
    url: '/imUserFollow/export',
    method: 'GET',
    params,
    responseType: 'blob',
  })
}

/**
 * 通过条件查询关注详情
 */
export const getUserFollowDetails = (followId: string) => {
  return request<UserFollowResponse>({
    url: '/imUserFollow/details',
    method: 'GET',
    params: {
      id: followId,
    },
  })
}

/**
 * 获取用户的关注列表
 */
export const getUserFollowings = (userId: string) => {
  return request<UserFollowListResponse>({
    url: '/imUserFollow/followings',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 获取用户的粉丝列表
 */
export const getUserFollowers = (userId: string) => {
  return request<UserFollowListResponse>({
    url: '/imUserFollow/followers',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 获取互相关注的好友列表
 */
export const getMutualFollows = (userId: string) => {
  return request<UserFollowListResponse>({
    url: '/imUserFollow/mutual-follows',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 检查关注关系
 */
export const checkFollowRelation = (userId: string, targetUserId: string) => {
  return request.get<{
    code: number
    message: string
    data: {
      isFollowing: boolean
      isFollower: boolean
      isMutualFollow: boolean
      followTime?: string
      remarkName?: string
    }
  }>({
    url: '/imUserFollow/check-relation',
    data: {
      userId,
      targetUserId,
    },
  })
}

/**
 * 批量关注用户
 */
export const batchFollowUsers = (userId: string, followUserIds: string[], groupId?: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUserFollow/batch-follow',
    method: 'POST',
    data: {
      userId,
      followUserIds,
      groupId,
    },
  })
}

/**
 * 批量取消关注
 */
export const batchUnfollowUsers = (userId: string, followUserIds: string[]) => {
  return request<{ code: number; message: string }>({
    url: '/imUserFollow/batch-unfollow',
    method: 'POST',
    data: {
      userId,
      followUserIds,
    },
  })
}

/**
 * 移动关注到分组
 */
export const moveFollowToGroup = (followId: string, groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUserFollow/move-to-group',
    method: 'POST',
    data: {
      followId,
      groupId,
    },
  })
}

/**
 * 获取关注分组列表
 */
export const getFollowGroups = (userId: string) => {
  return request<{
    code: number
    message: string
    data: FollowGroup[]
  }>({
    url: '/imUserFollow/groups',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 创建关注分组
 */
export const createFollowGroup = (userId: string, groupName: string, groupOrder?: number) => {
  return request<{ code: number; message: string; data: { groupId: string } }>({
    url: '/imUserFollow/create-group',
    method: 'POST',
    data: {
      userId,
      groupName,
      groupOrder,
    },
  })
}

/**
 * 删除关注分组
 */
export const deleteFollowGroup = (groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUserFollow/delete-group',
    method: 'POST',
    data: {
      groupId,
    },
  })
}

/**
 * 获取关注统计信息
 */
export const getFollowStatistics = (userId: string) => {
  return request<{
    code: number
    message: string
    data: {
      followingCount: number
      followerCount: number
      mutualFollowCount: number
      groupStats: Array<{ groupId: string; groupName: string; count: number }>
      recentFollows: Array<{ userId: string; userName: string; followTime: string }>
    }
  }>({
    url: '/imUserFollow/statistics',
    method: 'GET',
    params: {
      userId,
    },
  })
}
