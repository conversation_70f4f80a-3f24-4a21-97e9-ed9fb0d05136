<route lang="json">
{
  "style": {
    "navigationBarTitleText": "好友申请",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="friend-request-container">
      <!-- 申请人信息 -->
      <view class="user-info-section">
        <view class="user-avatar-wrapper">
          <image :src="requestInfo.avatar" class="user-avatar" mode="aspectFill" />
        </view>
        <view class="user-details">
          <text class="user-name">{{ requestInfo.nickname }}</text>
          <text class="user-id">ID: {{ requestInfo.userID }}</text>
        </view>
      </view>

      <!-- 申请信息 -->
      <view class="request-info-section">
        <view class="info-item">
          <text class="info-label">申请时间</text>
          <text class="info-value">{{ formatTime(requestInfo.requestTime) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">验证消息</text>
          <text class="info-value">{{ requestInfo.message || '我是' + requestInfo.nickname }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">来源</text>
          <text class="info-value">{{ getSourceText(requestInfo.source) }}</text>
        </view>
      </view>

      <!-- 备注设置 -->
      <view class="remark-section">
        <view class="section-header">
          <text class="section-title">设置备注</text>
        </view>
        <view class="remark-input-wrapper">
          <input 
            v-model="remarkName"
            class="remark-input"
            placeholder="请输入备注名（可选）"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <view class="action-buttons">
          <view 
            class="action-btn reject-btn"
            @click="rejectRequest"
            :class="{ 'disabled': isProcessing }"
          >
            <text class="btn-text">拒绝</text>
          </view>
          <view 
            class="action-btn accept-btn"
            @click="acceptRequest"
            :class="{ 'disabled': isProcessing }"
          >
            <text class="btn-text">同意</text>
          </view>
        </view>
      </view>

      <!-- 处理状态显示 -->
      <view v-if="requestInfo.status !== 'pending'" class="status-section">
        <view class="status-card" :class="requestInfo.status">
          <image 
            :src="getStatusIcon(requestInfo.status)" 
            class="status-icon" 
            mode="aspectFit" 
          />
          <text class="status-text">{{ getStatusText(requestInfo.status) }}</text>
          <text class="status-time">{{ formatTime(requestInfo.handleTime) }}</text>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view v-if="requestInfo.status === 'accepted'" class="quick-actions">
        <view class="quick-action-btn" @click="startChat">
          <image src="/static/images/im/icon.png" class="quick-icon" mode="aspectFit" />
          <text class="quick-text">发送消息</text>
        </view>
        <view class="quick-action-btn" @click="viewProfile">
          <image src="/static/images/user/tx.png" class="quick-icon" mode="aspectFit" />
          <text class="quick-text">查看资料</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import imManager from '@/utils/im'
import { message } from '@/utils/util'
import dayjs from 'dayjs'

interface FriendRequest {
  userID: string
  nickname: string
  avatar: string
  message: string
  requestTime: number
  source: string
  status: 'pending' | 'accepted' | 'rejected'
  handleTime?: number
}

// 响应式数据
const requestInfo = reactive<FriendRequest>({
  userID: '',
  nickname: '',
  avatar: '/static/images/user/tx.png',
  message: '',
  requestTime: Date.now(),
  source: 'search',
  status: 'pending'
})

const remarkName = ref('')
const isProcessing = ref(false)

// 生命周期
onLoad((options) => {
  if (options.requestData) {
    try {
      const data = JSON.parse(decodeURIComponent(options.requestData))
      Object.assign(requestInfo, data)
      remarkName.value = data.remark || data.nickname || ''
    } catch (error) {
      console.error('解析请求数据失败:', error)
    }
  }
})

// 格式化时间
const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

// 获取来源文本
const getSourceText = (source: string): string => {
  const sourceMap: Record<string, string> = {
    'search': '通过搜索添加',
    'qrcode': '通过二维码添加',
    'group': '通过群聊添加',
    'phone': '通过手机号添加',
    'recommend': '通过推荐添加'
  }
  return sourceMap[source] || '其他方式'
}

// 获取状态图标
const getStatusIcon = (status: string): string => {
  const iconMap: Record<string, string> = {
    'accepted': '/static/images/im/icon.png',
    'rejected': '/static/images/im/fj.png'
  }
  return iconMap[status] || '/static/images/im/icon.png'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    'accepted': '已同意好友申请',
    'rejected': '已拒绝好友申请'
  }
  return textMap[status] || ''
}

// 同意好友申请
const acceptRequest = async () => {
  if (isProcessing.value) return

  isProcessing.value = true
  
  try {
    await imManager.addFriend({
      userID: requestInfo.userID,
      remark: remarkName.value || requestInfo.nickname
    })

    requestInfo.status = 'accepted'
    requestInfo.handleTime = Date.now()
    
    message.success('已同意好友申请')
    
    // 可以选择跳转到聊天页面
    setTimeout(() => {
      uni.showModal({
        title: '添加成功',
        content: '是否立即开始聊天？',
        success: (res) => {
          if (res.confirm) {
            startChat()
          }
        }
      })
    }, 1000)
    
  } catch (error) {
    console.error('同意好友申请失败:', error)
    message.error('操作失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

// 拒绝好友申请
const rejectRequest = async () => {
  if (isProcessing.value) return

  uni.showModal({
    title: '确认拒绝',
    content: '确定要拒绝此好友申请吗？',
    success: async (res) => {
      if (res.confirm) {
        isProcessing.value = true
        
        try {
          // TODO: 调用拒绝好友申请的API
          
          requestInfo.status = 'rejected'
          requestInfo.handleTime = Date.now()
          
          message.success('已拒绝好友申请')
          
        } catch (error) {
          console.error('拒绝好友申请失败:', error)
          message.error('操作失败，请重试')
        } finally {
          isProcessing.value = false
        }
      }
    }
  })
}

// 开始聊天
const startChat = () => {
  uni.navigateTo({
    url: `/pages/chat/chat?userID=${requestInfo.userID}&username=${encodeURIComponent(requestInfo.nickname)}`
  })
}

// 查看资料
const viewProfile = () => {
  uni.navigateTo({
    url: `/pages/chat/settings?userId=${requestInfo.userID}&username=${encodeURIComponent(requestInfo.nickname)}`
  })
}
</script>

<style scoped lang="scss">
.friend-request-container {
  padding: 40rpx;
}

.user-info-section {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.user-avatar-wrapper {
  margin-right: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.request-info-section {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.remark-section {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-header {
  padding: 32rpx 32rpx 20rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
}

.remark-input-wrapper {
  padding: 0 32rpx 32rpx;
}

.remark-input {
  width: 100%;
  padding: 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
}

.action-section {
  margin-bottom: 30rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.action-btn.disabled {
  opacity: 0.5;
}

.reject-btn {
  background-color: #f8f8f8;
  border: 1rpx solid #e0e0e0;
}

.reject-btn .btn-text {
  color: #666;
}

.accept-btn {
  background-color: #1989fa;
}

.accept-btn .btn-text {
  color: white;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
}

.status-section {
  margin-bottom: 30rpx;
}

.status-card {
  padding: 40rpx;
  background-color: white;
  border-radius: 16rpx;
  text-align: center;
  border-left: 8rpx solid #1989fa;
}

.status-card.rejected {
  border-left-color: #ff4757;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.status-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.status-time {
  font-size: 24rpx;
  color: #999;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
}

.quick-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-radius: 16rpx;
  transition: background-color 0.2s;
}

.quick-action-btn:active {
  background-color: #f8f8f8;
}

.quick-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.quick-text {
  font-size: 24rpx;
  color: #666;
}
</style>
