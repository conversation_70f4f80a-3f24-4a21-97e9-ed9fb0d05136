<template>
  <view class="flex-row items-center p-[10rpx] mb-[20rpx] bg-white" @click="startChat">
    <view
      class="avatar-cell w-[100rpx] h-[100rpx] rounded-[50rpx] overflow-hidden bg-light mr-[20rpx]"
    >
      <image
        :src="props.data.avatar || '/statics/user/default_avatar.png'"
        class="w-[100%] h-[100%]"
      ></image>
    </view>
    <view class="content flex-1 overflow-hidden">
      <view class="title mb-[10rpx]">
        <text class="text-lg">{{ props.data.username }}</text>
      </view>
      <view v-if="props.data.userID" class="subtitle">
        <text class="text-sm text-muted">ID: {{ props.data.userID }}</text>
      </view>
    </view>
    <view class="ml-[12rpx]">
      <c-icon type="gengduo" color="#999999"></c-icon>
    </view>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import type { IFriend } from 'types/chat.interface'
import { type PropType } from 'vue'

const props = defineProps({
  data: {
    type: Object as PropType<IFriend>,
    default: () => ({}),
  },
})

// 开始聊天
const startChat = () => {
  if (props.data.userID) {
    // 跳转到聊天页面，传递用户信息
    uni.navigateTo({
      url: `/pages/chat/chat?userID=${props.data.userID}&username=${encodeURIComponent(
        props.data.username
      )}&avatar=${encodeURIComponent(props.data.avatar || '')}&userSig=${encodeURIComponent(
        props.data.userSig || ''
      )}`,
    })
  } else {
    // 如果没有userID，可能是群聊或其他类型
    uni.showToast({
      title: '暂不支持此类型聊天',
      icon: 'none',
    })
  }
}
</script>

<style scoped lang="scss">
.badge {
  height: 26rpx;
  border-radius: 13rpx;
  min-width: 36rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}
.message {
  text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
