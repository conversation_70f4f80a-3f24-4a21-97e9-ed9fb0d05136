import request from '@/utils/request'
import { encryption, encryptionBase64 } from '@/utils/util'
import qs from 'qs'

const basicAuth = 'Basic ' + encryptionBase64(import.meta.env.VITE_OAUTH2_APP_CLIENT)

const basicMobileAuth = 'Basic ' + encryptionBase64(import.meta.env.VITE_OAUTH2_MOBILE_CLIENT)

// 手机验证码登录
export function mobileLogin({ mobile, code }: Record<string, any>) {
  const form = {
    mobile: `APP-SMS@${mobile}`,
    code,
    grant_type: 'mobile',
    scope: 'server',
  }

  return request.post(
    {
      url: '/auth/oauth2/token?' + qs.stringify(form),
      header: {
        Authorization: basicMobileAuth, //"mini:mini"
        'content-type': 'application/x-www-form-urlencoded',
      },
    },
    { withToken: false }
  )
}

// 账号密码登录
export function accountLogin(data: Record<string, any>) {
  data.grant_type = 'password'
  data.scope = 'server'

  return request.post(
    {
      url: '/auth/oauth2/token?' + qs.stringify(data),
      header: {
        Authorization: basicAuth,
        'content-type': 'application/x-www-form-urlencoded',
      },
    },
    { withToken: false }
  )
}

// 微信小程序登录

export function mnpLogin(code: string) {
  const form = {
    mobile: `APP-MINI@${code}`,
    code: code,
    grant_type: 'mobile',
    scope: 'server',
  }

  return request.post(
    {
      url: '/auth/oauth2/token?' + qs.stringify(form),
      header: {
        Authorization: basicAuth, //"mini:mini"
        'content-type': 'application/x-www-form-urlencoded',
      },
    },
    { withToken: false }
  )
}

//注册
export function register(data: Record<string, any>) {
  return request.post({ url: '/app/appuser/register', data })
}

//忘记密码
export function forgotPassword(data: Record<string, any>) {
  return request.post({ url: '/app/login/forgotPassword', data })
}

//向微信请求code的链接
export function getWxCodeUrl() {
  return request.get({
    url: '/app/appsocial/oaCodeUrl',
    data: { url: location.href },
  })
}

// 公众号登录
export function OALogin(code: string) {
  const form = {
    mobile: `APP-WX@${code}`,
    code: code,
    grant_type: 'mobile',
    scope: 'server',
  }

  return request.post(
    {
      url: '/auth/oauth2/token?' + qs.stringify(form),
      header: {
        Authorization: basicAuth, //"mini:mini"
        'content-type': 'application/x-www-form-urlencoded',
      },
    },
    { withToken: false }
  )
}
