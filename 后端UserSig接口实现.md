# 🔧 后端 UserSig 接口实现指南

## 📋 基于你的腾讯云 IM 配置

### 🔑 配置信息

```yaml
SDKAppID: **********
SecretKey: bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7
过期时间: 5184000 秒 (60天)
管理员ID: administrator
服务器地址: http://***************/api/carcityim/im
```

## 🚀 后端接口实现

### 1. **Node.js + Express 实现**

#### 安装依赖

```bash
npm install tls-sig-api-v2 express cors
```

#### 接口实现代码

```javascript
const express = require('express')
const TLSSigAPIv2 = require('tls-sig-api-v2')
const cors = require('cors')

const app = express()
app.use(cors())
app.use(express.json())

// 腾讯云IM配置
const IM_CONFIG = {
  SDKAppID: **********,
  SecretKey: 'bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7',
  ExpireTime: 5184000, // 60天
  AdminUserID: 'administrator',
}

// 创建TLS签名API实例
const api = new TLSSigAPIv2.Api(IM_CONFIG.SDKAppID, IM_CONFIG.SecretKey)

/**
 * 获取UserSig接口
 * GET /api/carcityim/im/getUserSig?userID=xxx&expireTime=xxx
 */
app.get('/api/carcityim/im/getUserSig', (req, res) => {
  try {
    const { userID, expireTime } = req.query

    // 验证参数
    if (!userID) {
      return res.json({
        code: -1,
        message: '用户ID不能为空',
        data: null,
      })
    }

    // 使用配置的过期时间或传入的过期时间
    const expire = expireTime || IM_CONFIG.ExpireTime

    // 生成UserSig
    const userSig = api.genUserSig(userID, expire)

    // 返回结果
    res.json({
      code: 0,
      message: 'success',
      data: {
        userSig: userSig,
        userID: userID,
        expireTime: Date.now() + expire * 1000,
        SDKAppID: IM_CONFIG.SDKAppID,
      },
    })

    console.log(`为用户 ${userID} 生成UserSig成功`)
  } catch (error) {
    console.error('生成UserSig失败:', error)
    res.json({
      code: -1,
      message: '生成UserSig失败: ' + error.message,
      data: null,
    })
  }
})

/**
 * 获取IM配置接口
 * GET /api/carcityim/im/config
 */
app.get('/api/carcityim/im/config', (req, res) => {
  res.json({
    code: 0,
    message: 'success',
    data: {
      SDKAppID: IM_CONFIG.SDKAppID,
      SERVER_URL: 'http://***************/api/carcityim/im',
      DEFAULT_AVATAR: '/static/images/user/tx.png',
      environment: 'production',
      testMode: false,
    },
  })
})

/**
 * 健康检查接口
 * GET /api/carcityim/im/health
 */
app.get('/api/carcityim/im/health', (req, res) => {
  res.json({
    code: 0,
    message: 'IM服务运行正常',
    data: {
      timestamp: new Date().toISOString(),
      SDKAppID: IM_CONFIG.SDKAppID,
      status: 'healthy',
    },
  })
})

// 启动服务器
const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`IM服务器启动成功，端口: ${PORT}`)
  console.log(`UserSig接口: http://localhost:${PORT}/api/carcityim/im/getUserSig`)
  console.log(`配置接口: http://localhost:${PORT}/api/carcityim/im/config`)
})
```

### 2. **Java Spring Boot 实现**

#### Maven 依赖

```xml
<dependency>
    <groupId>com.tencentcloudapi</groupId>
    <artifactId>tencentcloud-sdk-java</artifactId>
    <version>3.1.423</version>
</dependency>
```

#### 接口实现

```java
@RestController
@RequestMapping("/api/carcityim/im")
@CrossOrigin
public class IMController {

    private static final int SDK_APP_ID = **********;
    private static final String SECRET_KEY = "bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7";
    private static final int EXPIRE_TIME = 5184000; // 60天

    @GetMapping("/getUserSig")
    public ResponseEntity<Map<String, Object>> getUserSig(@RequestParam String userID, @RequestParam(required = false) Integer expireTime) {
        try {
            if (expireTime == null) {
                expireTime = EXPIRE_TIME;
            }

            if (userID == null || userID.isEmpty()) {
                return ResponseEntity.ok(createErrorResponse("用户ID不能为空"));
            }

            // 生成UserSig
            TLSSigAPIv2 api = new TLSSigAPIv2(SDK_APP_ID, SECRET_KEY);
            String userSig = api.genUserSig(userID, expireTime);

            Map<String, Object> data = new HashMap<>();
            data.put("userSig", userSig);
            data.put("userID", userID);
            data.put("expireTime", System.currentTimeMillis() + expireTime * 1000L);
            data.put("SDKAppID", SDK_APP_ID);

            return ResponseEntity.ok(createSuccessResponse(data));

        } catch (Exception e) {
            return ResponseEntity.ok(createErrorResponse("生成UserSig失败: " + e.getMessage()));
        }
    }

    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> data = new HashMap<>();
        data.put("SDKAppID", SDK_APP_ID);
        data.put("SERVER_URL", "http://***************/api/carcityim/im");
        data.put("DEFAULT_AVATAR", "/static/images/user/tx.png");
        data.put("environment", "production");
        data.put("testMode", false);

        return ResponseEntity.ok(createSuccessResponse(data));
    }

    private Map<String, Object> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 0);
        response.put("message", "success");
        response.put("data", data);
        return response;
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", -1);
        response.put("message", message);
        response.put("data", null);
        return response;
    }
}
```

### 3. **PHP Laravel 实现**

#### 安装依赖

```bash
composer require tencentcloud/tencentcloud-sdk-php
```

#### 接口实现

```php
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// IM配置
define('SDK_APP_ID', **********);
define('SECRET_KEY', 'bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7');
define('EXPIRE_TIME', 5184000); // 60天

/**
 * 获取UserSig
 */
Route::get('/api/carcityim/im/getUserSig', function (Request $request) {
    try {
        $userID = $request->query('userID');
        $expireTime = $request->query('expireTime', EXPIRE_TIME);

        if (empty($userID)) {
            return response()->json([
                'code' => -1,
                'message' => '用户ID不能为空',
                'data' => null
            ]);
        }

        // 生成UserSig (需要引入腾讯云SDK)
        $api = new TLSSigAPIv2(SDK_APP_ID, SECRET_KEY);
        $userSig = $api->genUserSig($userID, $expireTime);

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'userSig' => $userSig,
                'userID' => $userID,
                'expireTime' => time() + $expireTime,
                'SDKAppID' => SDK_APP_ID
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'code' => -1,
            'message' => '生成UserSig失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

/**
 * 获取IM配置
 */
Route::get('/api/carcityim/im/config', function () {
    return response()->json([
        'code' => 0,
        'message' => 'success',
        'data' => [
            'SDKAppID' => SDK_APP_ID,
            'SERVER_URL' => 'http://***************/api/carcityim/im',
            'DEFAULT_AVATAR' => '/static/images/user/tx.png',
            'environment' => 'production',
            'testMode' => false
        ]
    ]);
});
```

## 🔧 部署配置

### 1. **Nginx 配置**

```nginx
server {
    listen 80;
    server_name ***************;

    location /api/carcityim/im/ {
        proxy_pass http://localhost:3000/api/carcityim/im/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
```

### 2. **环境变量配置**

```bash
# .env 文件
IM_SDK_APP_ID=**********
IM_SECRET_KEY=bf397da549152c2105c8c90f02feafd968c87350f3bbe399f31e2868693e8c7
IM_EXPIRE_TIME=5184000
IM_ADMIN_USER_ID=administrator
IM_SERVER_URL=http://***************/api/carcityim/im
```

## 🧪 测试接口

### 使用 curl 测试

```bash
# 测试获取UserSig
curl -X GET "http://***************/api/carcityim/im/getUserSig?userID=test_user_001"

# 测试获取配置
curl -X GET http://***************/api/carcityim/im/config

# 测试健康检查
curl -X GET http://***************/api/carcityim/im/health
```

## 🚨 安全注意事项

1. **SecretKey 保护**: 绝对不能泄露给客户端
2. **接口鉴权**: 生产环境需要添加用户身份验证
3. **HTTPS**: 生产环境必须使用 HTTPS
4. **限流**: 添加接口调用频率限制
5. **日志**: 记录所有 UserSig 生成日志

## 📝 前端测试

配置完成后，你可以在前端测试页面验证：

- 访问: http://localhost:8088/#/src/pages/admin/usersig-test
- 点击"从服务器获取 UserSig"测试接口连通性

---

**🎉 配置完成！现在你的 IM 系统可以正常工作了！**
