import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  // 你也可以定义 pages 字段，它具有最高的优先级。
  globalStyle: {
    rpxCalcMaxDeviceWidth: 600,
    rpxCalcBaseDeviceWidth: 375,
    rpxCalcIncludeWidth: 375,
    navigationBarTextStyle: 'black',
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#ffffff',
    backgroundColor: '#F8F8F8',
    backgroundColorContent: '#F8F8F8',
    backgroundTextStyle: 'dark',
    onReachBottomDistance: 120,
    animationDuration: 260,
    animationType: 'pop-in',
    'app-plus': {
      animationDuration: 260,
      animationType: 'pop-in',
      scrollIndicator: 'none',
    },
  },
  easycom: {
    custom: {
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
      '^w-(.*)': '@/components/widgets/$1/$1.vue',
    },
  },
  tabBar: {
    color: '#395286',
    selectedColor: '#0767FF',
    backgroundColor: '#ffffff',
    borderStyle: 'white',
    height: '55px',
    fontSize: '12px',
    iconWidth: '20px',
    spacing: '2px',
    midButton: {
      width: '76px',
      height: '76px',
      iconPath: '/static/icons/midButton.png',
      // text: '',
      iconWidth: '76px',
      // backgroundImage: 'static/images/icon/wechat.png',
    },
    list: [
      {
        text: '车源',
        iconPath: 'static/icons/car_resource.png',
        selectedIconPath: 'static/icons/car_resource_actived.png',
        pagePath: 'pages/index/index',
      },
      {
        text: '圈子',
        iconPath: 'static/icons/clique.png',
        selectedIconPath: 'static/icons/clique_actived.png',
        pagePath: 'pages/clique/list',
      },
      {
        text: '消息',
        iconPath: 'static/icons/message.png',
        selectedIconPath: 'static/icons/message_actived.png',
        pagePath: 'pages/contacts/contacts',
      },
      {
        text: '我的',
        iconPath: 'static/icons/my.png',
        selectedIconPath: 'static/icons/my_actived.png',
        pagePath: 'pages/user/user',
      },
    ],
  },
})
