import request from '@/utils/request'

export interface IArea {
  id: string
  adcode: string
  name: string
  parentId: string
}
export interface IRegisterVipForm {
  userId: string
  name: string
  wxNumber: string
  avatar: string
  nickname: string
  phone: string
  mobileCode: string
  password: string
  password2: string
  carDealershipAddress: string
  carDealershipLat: string
  carDealershipLng: string
  carDealershipName: string
  region: string
  provinceCode: string
  cityCode: string
  districtCode: string
  detailAddress: string
  idCard: string
  idCardCorrect: string
  idCardImageReverse: string
  businessLicense: string
  faceVerify: boolean
  memberPay: boolean
}

export function getUserTocCenter(header?: any, config?: {}) {
  return request.get({ url: '/app/appuser/info', header }, config)
}

export function getUserCenter(header?: any, config?: {}) {
  return request.get({ url: '/admin/user/info', header }, config)
}

// 个人编辑
export function userEdit(data: any) {
  return request.put({ url: '/app/appuser/edit', data })
}

// 绑定手机
export function userBindMobile(data: any, header?: any) {
  return request.post({ url: '/app/user/bindMobile', data, header }, { isAuth: true })
}

export function userChangePwd(data: any) {
  return request.put({ url: '/app/appuser/edit', data })
}

// 绑定小程序
export function mnpAuthBind(data: any) {
  return request.post({ url: '/app/user/bindMnp', data })
}

// 绑定公众号
export function oaAuthBind(data: any) {
  return request.post({
    url: '/app/appsocial/bind',
    data,
    header: {
      'content-type': 'application/x-www-form-urlencoded',
    },
  })
}

//更新微信小程序头像昵称
export function updateUser(data: Record<string, any>, header: any) {
  return request.post({ url: '/app/user/updateUser', data, header })
}

/**
 * 获取注册认证信息
 * @param userId
 * @returns
 */
export function getVerifyInfo() {
  return request.get({ url: '/app/appUserAuthentication/findByUserId' })
}

/**
 * 身份证OCR识别
 * @param data
 * @returns
 */
export function cardOcr(data) {
  return request.post({ url: '/app/appUserAuthentication/ocr', data, timeout: 30000 })
}

/**
 * 阿里云身份证OCR识别
 * @param data
 * @returns
 */
export function cardAliOcr(data) {
  return request.get({ url: '/app/ali/ocr/recognizeIdCard', data, timeout: 30000 })
}

/**
 * 获取阿里人脸认证id
 * @param data
 * @returns
 */
export function getAliCertifyId(data: { cardNo: string; cardName: string; metaInfo: string }) {
  return request.post({ url: '/app/face/verify/init', data })
}

/**
 * 获取阿里人脸认证结果
 * @param data
 * @returns
 */
export function getAliCertifyRes(certifyId: string) {
  return request.get({ url: '/app/face/verify/describe', data: { certifyId } })
}

export function getAreaData(areaType: '0' | '1' | '2' | '3', pid?: string) {
  return request.post<IArea[]>({
    url: `/app/area/tree`,
    data: { areaType, pid, areaStatus: '1' },
  })
}

export function registerVip(data: IRegisterVipForm) {
  return request.post({
    url: `/app/appUserAuthentication/save`,
    data,
  })
}
