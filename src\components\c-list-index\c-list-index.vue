<template>
  <view class="list-index flex items-center h-full bg-white">
    <!-- 内容区域 -->
    <scroll-view
      scroll-y
      enable-back-to-top
      :scroll-with-animation="false"
      :show-scrollbar="false"
      :scroll-into-view="`index-${state.current.letter || 'first'}`"
      class="h-[100%] flex-1"
      @scroll="onScroll"
    >
      <view
        class="index-item"
        :id="`index-${item.letter}`"
        v-for="item in props.dataList"
        :key="item.letter"
      >
        <slot name="letter" :letter="item.letter">
          <!-- 字母 -->
          <view class="letter">
            <text>{{ item.letter }}</text>
          </view>
        </slot>
        <slot name="defalut" :data="item.data">
          <!-- 数据内容 -->
          <view class="content" v-for="(cItem, index) in item.data" :key="index">
            <view class="h-[100rpx]">{{ cItem.name }}</view>
          </view>
        </slot>
      </view>
    </scroll-view>
    <!-- 字母导航栏 -->
    <view class="letter-container flex-col justify-center" v-if="showLatter">
      <view
        class="letter-bar p-[8rpx]"
        @touchmove.stop.prevent="barMove"
        @touchstart.stop.prevent="barMove"
        @touchend="barEnd"
      >
        <view
          v-for="item in props.dataList"
          :key="item.letter"
          class="letter-bar-item w-[36rpx] h-[36rpx] flex-row items-center justify-center"
          :class="[state.current.letter === item.letter ? 'actived' : '']"
        >
          <text class="text-xl">{{ item.letter }}</text>
        </view>
      </view>
    </view>
    <view
      v-if="state.currentLetterIsShow && state.current.letter"
      class="curr-tetter flex-row items-center justify-center"
    >
      <text class="text-white font-medium text-4xl">{{ state.current.letter }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getCurrentInstance, nextTick, onMounted, reactive, ref, type PropType } from 'vue'

const props = defineProps({
  offsetTop: {
    type: Number,
    defalut: 0,
  },
  dataList: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => [],
  },
  delay: {
    type: Number,
    default: 300,
  },
  showLatter: {
    type: Boolean,
    default: true,
  },
})

const state = reactive({
  current: {} as Record<string, any>,
  latterBarInfo: {
    top: 0,
    height: 0,
  },
  currentLetter: '',
  currentLetterIsShow: false,
})

// 每项距离顶部的高度
const tops = ref<any[]>([])
const { proxy }: any = getCurrentInstance()

// 计算节点信息
function computeNodeInfo() {
  nextTick(() => {
    setTimeout(() => {
      // 获取索引栏大小
      uni
        .createSelectorQuery()
        .in(proxy)
        .select('.letter-container .letter-bar')
        .boundingClientRect((res: any) => {
          if (res) {
            state.latterBarInfo.top = res.top
            state.latterBarInfo.height = res.height / props.dataList.length
          }
        })
        .exec()

      // 获取当前距离顶部的高度
      uni
        .createSelectorQuery()
        .in(proxy)
        .select('.list-index')
        .boundingClientRect((res: any) => {
          // 获取每项距离顶部的高度
          uni
            .createSelectorQuery()
            .in(proxy)
            .selectAll('.letter')
            .fields(
              {
                rect: true,
                size: true,
              },
              () => {}
            )
            .exec((d) => {
              tops.value = d[0].map((e: { top: number; height: number }) => e.top - res.top)
              // console.log(tops.value)
            })
        })
        .exec()
    }, props.delay || 0)
  })
}
// 监听滚动
const onScroll = function (e: { detail: { scrollTop: number } }) {
  // 对比每个高度计算
  let num =
    tops.value.filter((top) => e.detail.scrollTop >= top - (props.offsetTop || 0)).length - 1

  if (num < 0) {
    num = 0
  }

  // 设置当前
  // state.current = props.dataList[num]
}

// 定位到某行
function toRow(item: any) {
  state.currentLetterIsShow = true
  const currentLetter = item.letter
  if (state.current.letter !== currentLetter) {
    nextTick(() => {
      uni.vibrateShort({
        success(res) {
          console.log(res)
        },
      })
      state.current = item
    })
  }
}

// 移动
function barMove(e: TouchEvent) {
  const max = props.dataList.length

  let index = parseInt(
    String((e.touches[0].clientY - state.latterBarInfo.top) / state.latterBarInfo.height)
  )

  if (index >= max) {
    index = max - 1
  }

  if (index < 0) {
    index = 0
  }

  // state.current = props.dataList[index]
  // console.log(index)
  toRow(props.dataList[index])
}

// 离开
function barEnd() {
  state.currentLetterIsShow = false
}

// onMounted(() => {
//   computeNodeInfo()
// })
defineExpose({
  computeNodeInfo,
})
</script>

<style scoped lang="scss">
.list-index {
  height: 100%;
  .index-item {
    .letter {
      padding: 12rpx 12rpx;
      position: sticky;
      top: 0;
      background-color: $bg-color;
      z-index: 2;
    }
  }
}
.letter-container {
  position: absolute;
  top: 0;
  right: 12rpx;
  height: 100%;
  z-index: 3;
  .letter-bar {
    .letter-bar-item {
      margin: 6rpx 8rpx;
      &.actived {
        border-radius: 50%;
        background-color: $color-primary;
        text {
          color: #ffffff;
        }
      }
      text {
        font-size: 22rpx;
      }
    }
  }
}

.curr-tetter {
  position: absolute;
  top: 50%;
  margin-top: -50rpx;
  right: 150rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  color: #ffffff;
  background-color: rgba($color: #000000, $alpha: 0.5);
  z-index: 3;
}
</style>
