<template>
  <view class="col row" :class="[empty && 'empty', vertical ? 'vertical' : 'horizontal']">
    <view class="label" :style="{ width: labelWidth || parentLabelWidth }">{{ label }}</view>
    <slot>
      <view class="text">{{ content }}</view>
    </slot>
  </view>
</template>

<script>
export default {
  name: 'CCol',
  components: {},
  data() {
    return {
      parentLabelWidth: '',
    }
  },
  props: {
    label: {
      type: String,
      default: '',
    },
    vertical: {
      type: Boolean,
      default: false,
    },
    labelWidth: {
      type: String,
      default: '',
    },
    empty: {
      type: Boolean,
      default: false,
    },
    content: {
      type: [String, Number],
      default: '',
    },
  },
  mounted() {
    const tzRow = this.getForm(this.$parent)
    if (tzRow?.labelWidth) {
      this.parentLabelWidth = tzRow.labelWidth
    }
  },
  methods: {
    getForm(parent) {
      const parentInfo = this.getParentInfo(parent)
      if (parentInfo.$options?.name === 'TzRow') {
        return parentInfo
      } else if (parentInfo.$parent) {
        return this.getForm(parentInfo.$parent)
      } else {
        return undefined
      }
    },
    getParentInfo(parent) {
      return parent
    },
  },
}
</script>

<style scoped lang="scss">
.col {
  min-width: 540rpx;
  margin-bottom: 36rpx;
  line-height: 32rpx;
  flex: 1;
  margin-right: 12rpx;
  // ~ .col {
  //   margin-right: 12rpx;
  // }
  &.horizontal {
    display: flex;
  }
  &.vertical {
    .label {
      margin-bottom: 12rpx;
    }
  }
  &.empty {
    margin: 0;
    padding: 0;
  }
  .label {
    width: 160rpx;
    flex-shrink: 0;
    font-size: 32rpx;
    color: #888;
  }
  .text {
    flex: 1;
    word-break: break-all;
    font-size: 32rpx;
    color: #000;
  }
}
</style>
