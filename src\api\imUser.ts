/**
 * IM用户管理API
 */
import { request } from '@/utils/request'

export interface IMUser {
  id: string
  userId: string
  nickname: string
  avatar?: string
  signature?: string
  gender?: 'male' | 'female' | 'unknown'
  birthday?: string
  location?: string
  language?: string
  status: 'active' | 'inactive' | 'banned'
  isOnline: boolean
  lastLoginTime?: string
  createTime: string
  updateTime?: string
  remarkName?: string
  isVerified?: boolean
  level?: number
  tags?: string[]
}

export interface IMUserResponse {
  code: number
  message: string
  data: IMUser
}

export interface IMUserListResponse {
  code: number
  message: string
  data: IMUser[]
  total?: number
}

export interface IMUserPageParams {
  current?: number
  size?: number
  keyword?: string
  status?: string
  isOnline?: boolean
}

/**
 * 修改用户关联IM用户
 */
export const updateIMUser = (userId: string, data: Partial<IMUser>) => {
  return request<{ code: number; message: string }>({
    url: '/imUser',
    method: 'PUT',
    data: {
      userId,
      ...data,
    },
  })
}

/**
 * 新增用户关联IM用户
 */
export const createIMUser = (data: {
  userId: string
  nickname: string
  avatar?: string
  signature?: string
  gender?: string
  birthday?: string
  location?: string
  language?: string
}) => {
  return request<{ code: number; message: string }>({
    url: '/imUser',
    method: 'POST',
    data,
  })
}

/**
 * 通过id删除用户关联IM用户
 */
export const deleteIMUser = (userId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUser',
    method: 'DELETE',
    data: {
      userId,
    },
  })
}

/**
 * 批量导入IM用户
 */
export const importIMUsers = (users: Array<Partial<IMUser>>) => {
  return request<{ code: number; message: string }>({
    url: '/imUser/import',
    method: 'POST',
    data: {
      users,
    },
  })
}

/**
 * 分页查询IM用户
 */
export const getIMUsersPage = (params: IMUserPageParams = {}) => {
  return request<IMUserListResponse>({
    url: '/imUser/page',
    method: 'GET',
    params: {
      current: params.current || 1,
      size: params.size || 20,
      keyword: params.keyword,
      status: params.status,
      isOnline: params.isOnline,
    },
  })
}

/**
 * 导出IM用户列表
 */
export const exportIMUsers = () => {
  return request<Blob>({
    url: '/imUser/export',
    method: 'GET',
    responseType: 'blob',
  })
}

/**
 * 通过id查询IM用户详情
 */
export const getIMUserDetails = (userId: string) => {
  return request<IMUserResponse>({
    url: '/imUser/details',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 获取用户在线状态
 */
export const getUserOnlineStatus = (userIds: string[]) => {
  return request<{
    code: number
    message: string
    data: Array<{ userId: string; isOnline: boolean; lastActiveTime?: string }>
  }>({
    url: '/imUser/online-status',
    method: 'POST',
    data: {
      userIds,
    },
  })
}

/**
 * 设置用户状态
 */
export const updateUserStatus = (userId: string, status: 'active' | 'inactive' | 'banned') => {
  return request<{ code: number; message: string }>({
    url: '/imUser/status',
    method: 'PUT',
    data: {
      userId,
      status,
    },
  })
}

/**
 * 批量设置用户状态
 */
export const batchUpdateUserStatus = (userIds: string[], status: 'active' | 'inactive' | 'banned') => {
  return request<{ code: number; message: string }>({
    url: '/imUser/batch-status',
    method: 'PUT',
    data: {
      userIds,
      status,
    },
  })
}

/**
 * 搜索IM用户
 */
export const searchIMUsers = (keyword: string, limit?: number) => {
  return request<IMUserListResponse>({
    url: '/imUser/search',
    method: 'GET',
    params: {
      keyword,
      limit: limit || 20,
    },
  })
}

/**
 * 获取用户统计信息
 */
export const getUserStatistics = () => {
  return request<{
    code: number
    message: string
    data: {
      totalUsers: number
      activeUsers: number
      onlineUsers: number
      bannedUsers: number
      newUsersToday: number
      newUsersThisWeek: number
      newUsersThisMonth: number
    }
  }>({
    url: '/imUser/statistics',
    method: 'GET',
  })
}

/**
 * 重置用户密码/重新生成UserSig
 */
export const resetUserCredentials = (userId: string) => {
  return request<{
    code: number
    message: string
    data: {
      userSig: string
      expireTime: number
    }
  }>({
    url: '/imUser/reset-credentials',
    method: 'POST',
    data: {
      userId,
    },
  })
}

/**
 * 获取用户的好友列表
 */
export const getUserFriends = (userId: string) => {
  return request<{
    code: number
    message: string
    data: Array<{
      friendId: string
      nickname: string
      avatar?: string
      remarkName?: string
      addTime: string
    }>
  }>({
    url: '/imUser/friends',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 获取用户的群组列表
 */
export const getUserGroups = (userId: string) => {
  return request<{
    code: number
    message: string
    data: Array<{
      groupId: string
      groupName: string
      role: 'owner' | 'admin' | 'member'
      joinTime: string
    }>
  }>({
    url: '/imUser/groups',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 强制用户下线
 */
export const forceUserOffline = (userId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUser/force-offline',
    method: 'POST',
    data: {
      userId,
    },
  })
}

/**
 * 发送系统消息给用户
 */
export const sendSystemMessage = (userId: string, message: string, messageType?: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUser/system-message',
    method: 'POST',
    data: {
      userId,
      message,
      messageType: messageType || 'text',
    },
  })
}

/**
 * 批量发送系统消息
 */
export const batchSendSystemMessage = (userIds: string[], message: string, messageType?: string) => {
  return request<{ code: number; message: string }>({
    url: '/imUser/batch-system-message',
    method: 'POST',
    data: {
      userIds,
      message,
      messageType: messageType || 'text',
    },
  })
}
