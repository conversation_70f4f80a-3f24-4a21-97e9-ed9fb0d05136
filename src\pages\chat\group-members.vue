<route lang="json">
{
  "style": {
    "navigationBarTitleText": "群成员管理",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="group-members-container">
      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-box">
          <image src="/static/images/im/icon.png" class="search-icon" mode="aspectFit" />
          <input
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索群成员"
            @input="onSearch"
          />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <view class="action-btn" @click="addMembers">
          <image src="/static/images/im/fj.png" class="action-icon" mode="aspectFit" />
          <text class="action-text">添加成员</text>
        </view>
        <view class="action-btn" @click="removeMembers">
          <image src="/static/images/im/icon.png" class="action-icon" mode="aspectFit" />
          <text class="action-text">移除成员</text>
        </view>
      </view>

      <!-- 群成员列表 -->
      <view class="members-section">
        <view class="section-header">
          <text class="section-title"
            >群成员 ({{ loading ? '加载中...' : `${filteredMembers.length}` }})</text
          >
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <text class="loading-text">正在加载群成员...</text>
        </view>

        <view v-else class="members-list">
          <view
            v-for="member in filteredMembers"
            :key="member.userId || member.userID"
            class="member-item"
            @click="showMemberActions(member)"
          >
            <view class="member-info">
              <image :src="member.avatar" class="member-avatar" mode="aspectFill" />
              <view class="member-details">
                <view class="member-name-row">
                  <text class="member-name">{{ member.nickname }}</text>
                  <view
                    v-if="member.role === 'Owner' || member.role === 'owner'"
                    class="role-tag owner"
                    >群主</view
                  >
                  <view
                    v-else-if="member.role === 'Admin' || member.role === 'admin'"
                    class="role-tag admin"
                    >管理员</view
                  >
                </view>
                <text class="member-id">ID: {{ member.userId || member.userID }}</text>
              </view>
            </view>
            <view class="member-actions">
              <image src="/static/images/im/icon.png" class="more-icon" mode="aspectFit" />
            </view>
          </view>
        </view>
      </view>

      <!-- 成员操作弹窗 -->
      <view v-if="showActionSheet" class="action-sheet-overlay" @click="hideActionSheet">
        <view class="action-sheet" @click.stop>
          <view class="action-sheet-header">
            <text class="action-sheet-title">{{ selectedMember?.nickname }}</text>
          </view>
          <view class="action-sheet-content">
            <view class="action-sheet-item" @click="viewMemberProfile">
              <text class="action-sheet-text">查看资料</text>
            </view>
            <view class="action-sheet-item" @click="startPrivateChat">
              <text class="action-sheet-text">发送消息</text>
            </view>
            <view v-if="canManageMember" class="action-sheet-item" @click="setAsAdmin">
              <text class="action-sheet-text">
                {{ selectedMember?.role === 'Admin' ? '取消管理员' : '设为管理员' }}
              </text>
            </view>
            <view v-if="canRemoveMember" class="action-sheet-item danger" @click="removeMember">
              <text class="action-sheet-text">移出群聊</text>
            </view>
          </view>
          <view class="action-sheet-cancel" @click="hideActionSheet">
            <text class="cancel-text">取消</text>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import imManager from '@/utils/im'
import { message } from '@/utils/util'
import {
  getGroupMembersPage,
  addGroupMember,
  removeGroupMember,
  setGroupAdmin,
  muteGroupMember,
  unmuteGroupMember,
  transferGroupOwner,
  type GroupMember,
} from '@/api/groupMembers'

// 兼容原有接口
interface LegacyGroupMember {
  userID: string
  nickname: string
  avatar: string
  role: 'Owner' | 'Admin' | 'Member'
  joinTime: number
}

// 响应式数据
const groupId = ref('')
const groupName = ref('')
const searchKeyword = ref('')
const memberList = ref<GroupMember[]>([])
const showActionSheet = ref(false)
const selectedMember = ref<GroupMember | null>(null)
const currentUserRole = ref<'Owner' | 'Admin' | 'Member'>('Member')

// API相关数据
const loading = ref(false)
const total = ref(0)
const pageParams = reactive({
  current: 1,
  size: 20,
})

// 计算属性
const filteredMembers = computed(() => {
  if (!searchKeyword.value) return memberList.value

  return memberList.value.filter(
    (member) =>
      member.nickname.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (member.userId || member.userID || '').includes(searchKeyword.value)
  )
})

const canManageMember = computed(() => {
  return (
    currentUserRole.value === 'Owner' ||
    (currentUserRole.value === 'Admin' && selectedMember.value?.role === 'Member')
  )
})

const canRemoveMember = computed(() => {
  return (
    selectedMember.value?.role !== 'Owner' &&
    (currentUserRole.value === 'Owner' ||
      (currentUserRole.value === 'Admin' && selectedMember.value?.role === 'Member'))
  )
})

// 生命周期
onLoad((options) => {
  groupId.value = options.groupId || ''
  groupName.value = decodeURIComponent(options.groupName || '群聊')

  uni.setNavigationBarTitle({
    title: `${groupName.value} - 成员管理`,
  })
})

onMounted(() => {
  loadGroupMembers()
})

// 加载群成员列表
const loadGroupMembers = async () => {
  try {
    loading.value = true

    // 优先使用API获取群成员
    try {
      const response = await getGroupMembersPage({
        groupId: groupId.value,
        current: pageParams.current,
        size: pageParams.size,
        keyword: searchKeyword.value,
      })

      if (response.code === 200) {
        memberList.value = response.data || []
        total.value = response.total || 0

        // 获取当前用户角色
        const currentUser = memberList.value.find(
          (member) => member.userId === imManager.state.value.userID
        )
        if (currentUser) {
          currentUserRole.value =
            currentUser.role === 'owner'
              ? 'Owner'
              : currentUser.role === 'admin'
              ? 'Admin'
              : 'Member'
        }
        return
      }
    } catch (apiError) {
      console.warn('API获取群成员失败，使用IM SDK:', apiError)
    }

    // API失败时使用IM SDK作为备用
    const members = await imManager.getGroupMemberList(groupId.value)
    memberList.value = members.map((member: any) => ({
      id: member.userID,
      userId: member.userID,
      groupId: groupId.value,
      nickname: member.nick || member.userID,
      avatar: member.avatar || '/static/images/user/tx.png',
      role: member.role === 'Owner' ? 'owner' : member.role === 'Admin' ? 'admin' : 'member',
      joinTime: new Date(member.joinTime || Date.now()).toISOString(),
      isOnline: false,
    }))

    // 获取当前用户角色
    const currentUser = members.find(
      (member: any) => member.userID === imManager.state.value.userID
    )
    if (currentUser) {
      currentUserRole.value = currentUser.role || 'Member'
    }
  } catch (error) {
    console.error('加载群成员失败:', error)
    message.error('加载群成员失败')
  } finally {
    loading.value = false
  }
}

// 搜索成员
const onSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 添加成员
const addMembers = () => {
  uni.navigateTo({
    url: `/pages/chat/add-group-members?groupId=${groupId.value}&groupName=${encodeURIComponent(
      groupName.value
    )}`,
  })
}

// 移除成员
const removeMembers = () => {
  uni.navigateTo({
    url: `/pages/chat/remove-group-members?groupId=${groupId.value}&groupName=${encodeURIComponent(
      groupName.value
    )}`,
  })
}

// 显示成员操作菜单
const showMemberActions = (member: GroupMember) => {
  selectedMember.value = member
  showActionSheet.value = true
}

// 隐藏操作菜单
const hideActionSheet = () => {
  showActionSheet.value = false
  selectedMember.value = null
}

// 查看成员资料
const viewMemberProfile = () => {
  hideActionSheet()
  uni.navigateTo({
    url: `/pages/chat/settings?userId=${selectedMember.value?.userID}&username=${encodeURIComponent(
      selectedMember.value?.nickname || ''
    )}`,
  })
}

// 开始私聊
const startPrivateChat = () => {
  hideActionSheet()
  uni.navigateTo({
    url: `/pages/chat/chat?userID=${selectedMember.value?.userID}&username=${encodeURIComponent(
      selectedMember.value?.nickname || ''
    )}`,
  })
}

// 设置/取消管理员
const setAsAdmin = async () => {
  if (!selectedMember.value) return

  try {
    const isAdmin = selectedMember.value.role === 'Admin'
    // TODO: 调用设置管理员的API

    selectedMember.value.role = isAdmin ? 'Member' : 'Admin'
    message.success(isAdmin ? '已取消管理员' : '已设为管理员')
    hideActionSheet()
  } catch (error) {
    console.error('设置管理员失败:', error)
    message.error('操作失败')
  }
}

// 移出群聊
const removeMember = async () => {
  if (!selectedMember.value) return

  uni.showModal({
    title: '确认移出',
    content: `确定要将 ${selectedMember.value.nickname} 移出群聊吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // TODO: 调用移出群成员的API

          const index = memberList.value.findIndex((m) => m.userID === selectedMember.value?.userID)
          if (index > -1) {
            memberList.value.splice(index, 1)
          }

          message.success('已移出群聊')
          hideActionSheet()
        } catch (error) {
          console.error('移出群成员失败:', error)
          message.error('移出失败')
        }
      }
    },
  })
}
</script>

<style scoped lang="scss">
.group-members-container {
  padding: 20rpx;
}

.search-section {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 25rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
}

.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  transition: background-color 0.2s;
}

.action-btn:active {
  background-color: #e8e8e8;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333;
}

.members-section {
  flex: 1;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
  background-color: white;
  border-radius: 12rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.members-list {
  background-color: white;
  border-radius: 12rpx;
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.member-item:last-child {
  border-bottom: none;
}

.member-item:active {
  background-color: #f8f8f8;
}

.member-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.member-details {
  flex: 1;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-name {
  font-size: 28rpx;
  color: #333;
  margin-right: 12rpx;
}

.role-tag {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
}

.role-tag.owner {
  background-color: #ff6b35;
}

.role-tag.admin {
  background-color: #1989fa;
}

.member-id {
  font-size: 24rpx;
  color: #999;
}

.member-actions {
  padding: 12rpx;
}

.more-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 操作弹窗样式 */
.action-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.action-sheet {
  width: 100%;
  background-color: white;
  border-radius: 24rpx 24rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.action-sheet-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
}

.action-sheet-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.action-sheet-content {
  padding: 20rpx 0;
}

.action-sheet-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
  transition: background-color 0.2s;
}

.action-sheet-item:active {
  background-color: #f8f8f8;
}

.action-sheet-item.danger .action-sheet-text {
  color: #ff4757;
}

.action-sheet-text {
  font-size: 28rpx;
  color: #333;
}

.action-sheet-cancel {
  padding: 32rpx;
  text-align: center;
  background-color: #f8f8f8;
  margin-top: 20rpx;
}

.cancel-text {
  font-size: 28rpx;
  color: #666;
}
</style>
