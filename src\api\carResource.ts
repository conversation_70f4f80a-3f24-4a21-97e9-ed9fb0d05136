import request from '@/utils/request'
export interface ICarResource {
  carSourceId?: string
  type: 'default' | 'security'
  /**
   * 发布类型
   */
  pushType: 1 | 2
  /**
   * 发布用户类型 1c端用户 2b端用户
   */
  pushUserType: 1 | 2
  /**
   * 品牌id
   */
  brandId: string
  /**
   * 系列id
   */
  seriesId: string
  /**
   * 车辆款型id
   */
  modelId: number
  /**
   * 车辆款型
   */
  vehicleModel: string
  /**
   * 上牌日期
   */
  registrationDate: string
  /**
   * 出厂日期
   */
  productionDate: string
  /**
   * 一口价
   */
  fixedPrice?: number
  /**
   * 批发价
   */
  wholesalePrice?: number
  /**
   * 指导价
   */
  guidePrice: number
  /**
   * 排量
   */
  displacement: number
  /**
   * 排放标准
   */
  emissionStandard: number
  /**
   * 真实公里
   */
  mileage: number
  /**
   * 车辆颜色
   */
  vehicleColor: number
  /**
  /**
   * 车辆配置
   */
  vehicleConfiguration: number
  /**
   * 过户次数
   */
  transferCount: number
  /**
   * 省
   */
  provinceCode: string
  /**
   * 省
   */
  provinceName: string
  /**
   * 市
   */
  cityCode: string
  /**
   * 市
   */
  cityName: string
  /**
   * 区
   */
  districtCode: string
  /**
   * 区
   */
  districtName: string
  /**
   *归属地区
   */
  // location: string
  /**
   *详细车况
   */
  vehicleCondition: string
  /**
   *可见范围
   */
  visibleRange: 1 | 2 | 9
  /**
   *状态 1： 正常 0：下架
   */
  pushStatus: 1 | 0
  /**
   * 车源图片信息
   */
  appCarSourceImageList: {
    imageId?: string
    vehicleImage: string
  }[]
}

export interface ICarResourceItem extends ICarResource {
  carSourceId?: string
  userAvatar?: string
  brandName: string
  seriesName: string
  modelName: string
  districtName: string
  userName: string
  mobile: string
  isOpen?: boolean
  createTime: string
  createBy: string
  isSubscribe: boolean
  /**
   * 收藏状态 0 未收藏 1已收藏
   */
  collectStatus: 0 | 1
  appCarSourceImageVoList: {
    imageId?: string
    vehicleImage: string
    vehicleImageUrl: string
  }[]
}

export interface ISeries {
  seriesId: number
  brandId: number
  seriesName: string
  levelId: number
  levelName: string
  maxPrice: number
  minPrice: number
  newEnergy: boolean
  seriesImg: string
  seriesState: number
  seriesRank: number
  uvRank: number
  hotRank: number
  newEnergySeriesId: number
  foreignCar: boolean
  fuelTypeName: string
  fctId: number
  fctName: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  delFlag: string
  tenantId: number
}

export interface ICarType {
  brandId: number
  brandName: string
  seriesId: number
  seriesName: string
  name: string
  price: string
  downPrice: string
  dealerPrice: string
  imgUrl: string
  description: string
  paramIsShow: string
  priceTip: string
  year: string
  state: number
  tagTitle: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  delFlag: string
  tenantId: number
  specId: number
}

//发布
export function create(data: ICarResource) {
  return request.post({ url: '/app/appCarSource/save', data })
}

export function carBrandList() {
  return request.get({ url: '/app/appCarBrand/list' })
}
export function carSeriesList(brandId: string) {
  return request.get({ url: '/app/appCarBrandSeries/getCarBrandSeriesList', data: { brandId } })
}
export function carTypeList(seriesId: string) {
  return request.get({ url: '/app/appCarModel/getMapBySeriesId', data: { seriesId } })
}

export interface ICarlistQuery {
  current: number
  size: number
  cityCode?: string
  /**
   * 发布用户类型 1c端用户 2b端用户
   */
  pushUserType?: 1 | 2
  /**
   * 发布类型 1车源 2批发
   */
  pushType?: 1 | 2
  /**
   * 发布状态 1正常 0下架
   */
  pushStatus?: 1 | 0
  kw?: string
}
export function carList(query: ICarlistQuery) {
  return request.post<ICarResourceItem[]>({ url: '/app/appCarSource/page', data: query })
}
export function carDetail(id: string) {
  return request.get<ICarResourceItem>({
    url: '/app/appCarSource/findById',
    data: { carSourceId: id },
  })
}
