<template>
  <view
    class="page-container flex flex-col bg-page"
    :class="[scollY ? 'overflow-y-auto' : '']"
    :style="{
      minHeight: scrollHeight,
      height: fixedHeight ? scrollHeight : '',
    }"
  >
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { useClientStore } from '@/stores/client'
const props = defineProps({
  fullScreen: {
    type: Boolean,
    default: false,
  },
  hasBottomBar: {
    type: Boolean,
    default: false,
  },
  fixedHeight: {
    type: Boolean,
    default: false,
  },
  scollY: {
    type: Boolean,
    default: false,
  },
})
const { clientInfo } = useClientStore()
let tabbarHeight = 0
if (props.hasBottomBar) {
  // #ifdef H5 || APP
  tabbarHeight = 50
  // #endif
}
const scrollHeight = computed(() => {
  if (props.fullScreen) {
    return clientInfo.windowHeight - (props.hasBottomBar ? tabbarHeight : 0) + 'px'
  }
  return clientInfo.windowHeight - (clientInfo.statusBarHeight || 0) - 44 - tabbarHeight + 'px'
})
</script>
