<route lang="json">
{
  "style": {
    "navigationBarTitleText": "消息",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>
<template>
  <c-page fullScreen hasBottomBar>
    <!-- 头部背景 -->
    <view class="bg w-full absolute h-[360rpx]">
      <c-img src="/statics/images/header_bg2.png" class="w-full" mode="aspectFill"></c-img>
    </view>

    <!-- 头部内容 -->
    <view class="header sticky top-0 z-10 h-[280rpx]">
      <view class="w-full h-[360rpx] absolute">
        <c-img src="/statics/images/header_bg2.png" class="w-full h-full" mode="aspectFill"></c-img>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col justify-end">
        <!-- 标签切换 -->
        <view>
          <c-tabs
            :current="state.currentTab"
            :list="state.tabList"
            type="justify-start"
            :showBottomLine="false"
            size="large"
            @change="switchTab"
          ></c-tabs>
        </view>
        <!-- 搜索框 -->
        <view class="px-[26rpx] py-[20rpx]">
          <c-search :showArea="false" placeholder="备注/昵称"></c-search>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view v-if="state.currentTab === 'message'">
      <messageList ref="messageRef"></messageList>
    </view>

    <!-- 通讯录列表 -->
    <contactsList v-if="state.currentTab === 'contacts'" ref="contactsRef"></contactsList>

    <!-- 底部标签栏 -->
    <c-tabbar :tabIndex="3"></c-tabbar>
  </c-page>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import messageList from './components/messageList.vue'
import contactsList from './components/contactsList.vue'

const state = reactive({
  currentTab: 'message',
  tabList: [
    { name: '消息', type: 'message' },
    { name: '通讯录', type: 'contacts' },
  ],
})

const messageRef = ref()
const contactsRef = ref()

const switchTab = (tab: any) => {
  state.currentTab = tab

  // 切换时重新加载数据
  if (tab === 'message' && messageRef.value) {
    messageRef.value.initData()
  } else if (tab === 'contacts' && contactsRef.value) {
    contactsRef.value.initData()
  }
}
</script>

<style scoped lang="scss"></style>
