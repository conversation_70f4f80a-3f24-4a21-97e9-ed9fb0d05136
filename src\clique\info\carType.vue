<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择款型",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fixedHeight>
    <c-list-index :dataList="state.dataList" ref="listIndexRef" :showLatter="false">
      <template #defalut="{ data }">
        <view
          class="flex-row items-center py-[24rpx]"
          v-for="(item, index) in data"
          :key="index"
          @click="onSelect(item)"
        >
          <image :src="item.seriesImg" mode="widthFix" class="w-[60rpx] h-[60rpx]"></image>
          <text :class="['text-xl', state.current === item.brandId && 'text-primary']">{{
            item.name
          }}</text>
        </view>
      </template>
    </c-list-index>
  </c-page>
</template>

<script setup lang="ts">
import { carTypeList, type ICarType } from '@/api/carResource'
import { onLoad } from '@dcloudio/uni-app'
const state = reactive({
  dataList: [] as any[],
  current: '',
})

const listIndexRef = ref()

let query = {}
const getList = function (seriesId: string) {
  carTypeList(seriesId).then((res) => {
    if (res.data) {
      for (let key in res.data) {
        state.dataList.push({
          letter: key,
          data: res.data[key],
        })
        // if (key === 'C') {
        //   break
        // }
      }
      nextTick(() => {
        listIndexRef.value.computeNodeInfo()
      })
    }
  })
}

onLoad((options) => {
  console.log(options)
  console.log(options?.seriesId)
  if (options?.seriesId) {
    getList(options.seriesId)
  }
  if (options) {
    query = options
    // console.log(query)
    // console.log(query.provinceName)
    // console.log(query.cityName)
  }
})
const onSelect = function (e) {
  state.current = e.specId
  // console.log({ ...query, ...e })
  uni.$emit('selectType', { ...query, typeId: e.specId, typeName: e.name })
  uni.navigateBack({
    delta: 3,
  })
}
</script>

<style scoped lang="scss"></style>
