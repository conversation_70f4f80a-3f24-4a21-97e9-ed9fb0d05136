import request from '@/utils/request'

export interface IRegisterVipInfo {
  packageId: number
  packageName: string
  packageStatus: number
  packageType: number
  packageDuration: number
  orgPrice: number
  prePrice: number
  effType: number
  startDate: string
  expDate: string
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  tenantId: number
}

/**
 * 获取套餐列表
 * @param header
 * @param config
 * @returns
 */
export function getVipPackages(data = {}) {
  return request.post<IRegisterVipInfo[]>({ url: '/app/appMemberPackage/list', data })
}

/**
 * 创建订单
 * @param data
 * @returns
 */
export function createOrder(data = {}) {
  return request.post<{ params: string }>({ url: '/app/appMemberPackage/pay', data })
}
