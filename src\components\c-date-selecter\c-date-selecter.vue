<template>
  <picker
    class="date-picker w-full"
    @columnchange="onColumnChange"
    @change="onChange"
    :value="temp_date"
    mode="multiSelector"
    :range="range"
    :class="{ border }"
  >
    <view class="select w-full line-height-[65rpx] h-[60rpx] flex-row items-center justify-between">
      <view class="flex-1">
        <text v-if="modelValue" class="hasvalue">{{ getDateText(modelValue) }}</text>
        <text v-else class="novalue">{{ placeholder }}</text>
      </view>
      <view class="flex-row items-center">
        <view
          v-if="clearable && getDateText(modelValue)"
          class="clear_btn"
          @click.stop="handleClear"
        >
          <c-icon type="clear" style="color: #c0c4cc"></c-icon>
        </view>
        <c-icon v-if="showArrow" type="jinru" size="32" class="arrow" color="#c3c3c3"></c-icon>
      </view>
    </view>
  </picker>
</template>

<script>
export default defineComponent({
  name: 'CDateSelecter',
})
</script>
<script setup>
import { defineComponent, ref, defineProps, defineEmits, watch } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择时间',
  },
  type: {
    type: String,
    default: 'date',
    validator(prop) {
      let allows = ['date', 'datetime', 'month']
      return allows.includes(prop)
    },
  },
  showSecond: {
    type: Boolean,
    default: true,
  },
  minYear: {
    type: Number,
    default: () => 5,
  },
  maxYear: {
    type: Number,
    default: () => 5,
  },
  format: {
    //显示时的格式
    type: String,
    default: 'YYYY-MM-DD',
  },
  valueFormat: {
    //选择后值的格式
    type: String,
    default: 'YYYY-MM-DD',
  },
  atEnd: {
    //是否取当天的最晚一刻 23:59:59
    type: Boolean,
    default: false,
  },
  border: {
    //是否显示边框
    type: Boolean,
    default: false,
  },
  // 是否显示清除按钮
  clearable: {
    type: Boolean,
    default: false,
  },
  // 是否显示箭头
  showArrow: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits(['update:modelValue', 'change'])
const range = ref([])
const temp_date = ref([])

const init = function () {
  if (props.type === 'datetime') {
    if (props.modelValue) {
      temp_date.value = [
        dayjs().year() - dayjs(props.modelValue).year() + props.minYear,
        dayjs(props.modelValue).month(),
        dayjs(props.modelValue).date() - 1,
        dayjs(props.modelValue).hour(),
        dayjs(props.modelValue).minute(),
        dayjs(props.modelValue).second(),
      ]
    } else {
      temp_date.value = [
        props.minYear,
        dayjs().month(),
        dayjs().date() - 1,
        dayjs().hour(),
        dayjs().minute(),
        dayjs().second(),
      ]
    }
  }
  if (props.type === 'date') {
    if (props.modelValue) {
      temp_date.value = [
        dayjs().year() - dayjs(props.modelValue).year() + props.minYear,
        dayjs(props.modelValue).month(),
        dayjs(props.modelValue).date() - 1,
      ]
    } else {
      temp_date.value = [props.minYear, dayjs().month(), dayjs().date() - 1]
    }
  }
  if (props.type === 'month') {
    if (props.modelValue) {
      temp_date.value = [
        dayjs().year() - dayjs(props.modelValue).year() + props.minYear,
        dayjs(props.modelValue).month(),
      ]
    } else {
      temp_date.value = [props.minYear, dayjs().month()]
    }
  }
  range.value = initRange()
}

watch(
  () => props.modelValue,
  () => {
    init()
  }
)
watch(
  () => temp_date.value,
  (val) => {
    range.value = initRange(columnValuesToDate(val))
  }
)
const initRange = function (day = '') {
  let year_list = createYearList(props.minYear, props.maxYear)
  let month_list = createMonthList()
  let day_list = createDayList(day)
  if (props.type === 'month') {
    return [year_list, month_list]
  } else if (props.type === 'date') {
    return [year_list, month_list, day_list]
  } else if (props.type === 'datetime') {
    let hour_list = [...new Array(24).keys()].map((_item, index) => {
      if (index <= 9) {
        return '0' + index + '时'
      } else {
        return '' + index + '时'
      }
    })
    let minute_list = [...new Array(60).keys()].map((_item, index) => {
      if (index <= 9) {
        return '0' + index + '分'
      } else {
        return '' + index + '分'
      }
    })
    let second_list = [...new Array(60).keys()].map((_item, index) => {
      if (index <= 9) {
        return '0' + index + '秒'
      } else {
        return '' + index + '秒'
      }
    })
    if (props.showSecond) {
      return [year_list, month_list, day_list, hour_list, minute_list, second_list]
    } else {
      return [year_list, month_list, day_list, hour_list, minute_list]
    }
  }
}
const createYearList = function (left_offset, right_offset) {
  const current_year = dayjs().year()
  let list = []
  let start = current_year - left_offset
  const end = current_year + right_offset
  while (start <= end) {
    list.push(start + '年')
    start++
  }
  return list
}
const createMonthList = function () {
  let list = []
  let start = 1
  while (start <= 12) {
    list.push(start < 10 ? '0' + start + '月' : start + '月')
    start++
  }
  return list
}
const createDayList = function (day) {
  let list = []
  let start = 1
  const getMaxDay = () => {
    let current_year = dayjs().year()
    if (day) {
      current_year = dayjs(day).year()
    }
    const current_month = temp_date.value[1] + 1
    const max_day_month = [1, 3, 5, 7, 8, 10, 12]
    if (max_day_month.includes(current_month)) {
      return 31
    } else if (current_month === 2) {
      return current_year % 4 == 0 ? 29 : 28
    } else {
      return 30
    }
  }
  while (start <= getMaxDay()) {
    list.push(start < 10 ? '0' + start + '日' : start + '日')
    start++
  }
  return list
}
const getDateText = function (val) {
  if (!val) {
    return ''
  }
  if (Number(val)) {
    if (val.length === 10) {
      return dayjs(val * 1000).format(props.format)
    } else if (val.length === 13) {
      return dayjs(Number(val)).format(props.format)
    } else {
      return ''
    }
  } else {
    return dayjs(val).format(props.format)
  }
}
const onColumnChange = function (e) {
  temp_date.value[e.detail.column] = e.detail.value
  temp_date.value = [...temp_date.value]
}
const columnValuesToDate = function (columnValues) {
  const values = columnValues.map((item, index) => {
    if (!props.showSecond && !range.value[index]) {
      return '00'
    }
    return range.value[index][item].replace(/[^0-9]/gi, '')
  })
  const date_text = values.join('')
  if (props.atEnd) {
    return dayjs(date_text).add(1, 'day').subtract(1, 'second').format(props.valueFormat)
  } else {
    return dayjs(date_text).format(props.valueFormat)
  }
}
const onChange = function (e) {
  const value = columnValuesToDate(e.detail.value)
  emits('update:modelValue', value)
  emits('change', value)
}
const handleClear = function () {
  this.$emit('update:modelValue', '')
  this.$emit('change', '')
}

init()
</script>
<style scss scoped>
/* .date-picker {
  height: 60rpx;
} */
.items-center {
  align-items: center;
}
.border {
  padding: 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #f3f3f3;
}
.hasvalue {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}
.novalue {
  flex: 1;
  font-size: 30rpx;
  color: #8a929f;
}
</style>
