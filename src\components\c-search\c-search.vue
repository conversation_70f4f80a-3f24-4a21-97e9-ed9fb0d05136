<template>
  <view
    class="search-cell h-[70rpx] bg-white rounded-[20rpx] flex-row items-center justify-between p-[10rpx]"
  >
    <view class="flex-row items-center p-[24rpx]" v-if="props.showArea" @click="onSelectRegion">
      <view class="mr-[6rpx]">
        <text class="text-[26rpx]">{{ props.area || '全国' }}</text>
      </view>
      <c-icon size="20" type="zhankai"></c-icon>
    </view>
    <view
      class="input-container h-full flex-1 flex-row items-center px"
      :style="{ paddingLeft: !props.showArea ? '12rpx' : '' }"
    >
      <view class="mr-[12rpx]">
        <c-icon type="sousuo" size="30" color="#999999"></c-icon>
      </view>
      <view class="flex-1">
        <c-input
          size="small"
          :placeholder="props.placeholder"
          :value="props.modelValue"
          @input="onInput"
          @confirm="onConfirm"
        ></c-input>
      </view>
    </view>
    <!-- <view class="search-btn flex items-center justify-center">
      <text>搜索</text>
    </view> -->
  </view>
</template>

<script setup lang="ts">
import { navigateTo } from '@/utils/util'

// import { ref } from 'vue'
const props = defineProps({
  area: {
    type: String,
    default: '全国',
  },
  placeholder: {
    type: String,
    default: '品牌/车型/发车人',
  },
  showArea: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['update:modelValue', 'input', 'confirm', 'selectRegion'])
const onInput = function (e) {
  emits('input', e)
  emits('update:modelValue', e)
}
const onConfirm = function () {
  emits('confirm', props.modelValue)
}

const onSelectRegion = function () {
  uni.$once('selectRegion', (e) => {
    emits('selectRegion', e)
  })
  navigateTo('/user/provinceList?level=2')
}
</script>

<style scoped lang="scss">
// .search-btn {
//   height: 50rpx;
//   width: 90rpx;
//   border-radius: 25rpx;
//   background-color: $color-primary;
//   text {
//     font-size: 26rpx;
//     color: #ffffff;
//   }
// }
</style>
