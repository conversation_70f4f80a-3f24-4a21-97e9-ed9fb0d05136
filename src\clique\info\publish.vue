<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "车源发布",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": true
}
</route>
<template>
  <c-page fixedHeight scollY>
    <view class="card">
      <!-- 发布类型 -->
      <view class="cate relative h-[80rpx] overflow-hidden">
        <view class="flex-row justify-between z-10 h-full">
          <view
            class="cate-item h-full flex-1 flex-row items-center justify-center"
            @click="formData.type = 'default'"
          >
            <text class="font-medium">普通车源</text>
          </view>
          <view
            class="cate-item h-full flex-1 flex-row items-center justify-center"
            @click="formData.type = 'security'"
          >
            <text class="font-medium">保障金车源</text>
          </view>
        </view>
        <image
          :src="
            formData.type === 'default'
              ? $imgUrl('/statics/images/cate_bg1.png')
              : $imgUrl('/statics/images/cate_bg2.png')
          "
          class="absolute top-[-24rpx] left-0 h-[130rpx] w-full"
        ></image>
      </view>
      <!-- 上传图片 -->
      <view class="up p-base">
        <view class="flex-row items-center justify-between mb-base">
          <view class="flex-row items-center">
            <text class="font-medium">车辆图片</text>
            <text>*</text>
            <text>(长按图片排序)</text>
          </view>
          <view
            class="flex-row items-center justify-center h-[52rpx] w-[174rpx] rounded-[26rpx] bg-primary/20"
          >
            <c-icon type="dingyue" size="26" color="#0767FF"></c-icon>
            <text class="ml-mn text-primary text-[26rpx]">发车规范</text>
          </view>
        </view>

        <c-upload @change="onUpload" :maxCount="15"> </c-upload>

        <view class="mt-sm">
          <c-tip content="第一张照片作为封面必须左前45度，最少5张照片以上  "></c-tip>
        </view>
      </view>
    </view>
    <view class="card">
      <!-- 内容识别 -->
      <view class="p-base">
        <c-input
          type="textarea"
          height="100rpx"
          v-model="clipboardContent"
          placeholder="「粘贴识别」或输入文本，智能拆分车辆信息"
        ></c-input>
        <view class="flex-row justify-end">
          <c-button size="mini" round type="primary" @click="pasteContent">粘贴并识别</c-button>
        </view>
      </view>
    </view>
    <view class="card">
      <!-- 表单 -->
      <view class="p-base">
        <c-form label-width="180rpx">
          <c-form-item label="车辆款型">
            <!-- <c-select :range="[]" :customer="false" v-model="formData.modelId"> </c-select> -->
            <selectCarType @confirm="onSelectType"></selectCarType>
          </c-form-item>
          <c-form-item label="上牌日期">
            <c-date-selecter v-model="formData.registrationDate"> </c-date-selecter>
          </c-form-item>
          <c-form-item label="出厂日期">
            <c-date-selecter v-model="formData.productionDate"> </c-date-selecter>
          </c-form-item>
          <c-form-item label="一口价">
            <c-input v-model="formData.fixedPrice" type="digit" unit="万元"> </c-input>
          </c-form-item>
          <c-form-item label="指导价">
            <c-input v-model="formData.guidePrice" type="digit" unit="万元"> </c-input>
          </c-form-item>
          <c-form-item label="排量">
            <c-input v-model="formData.displacement" placeholder="**L/**T"></c-input>
            <!-- <select-data :customer="false" v-model="formData.displacement"> </select-data> -->
          </c-form-item>
          <c-form-item label="排放标准">
            <select-data
              :customer="false"
              dictType="emission_standard"
              isDict
              :fieldNames="{ label: 'label', value: 'label' }"
              v-model="formData.emissionStandard"
            >
            </select-data>
          </c-form-item>
          <c-form-item label="真实公里">
            <c-input v-model="formData.mileage" type="digit" unit="公里"> </c-input>
          </c-form-item>
          <c-form-item label="车辆颜色">
            <select-data
              :customer="false"
              dictType="car_color"
              isDict
              :fieldNames="{ label: 'label', value: 'label' }"
              v-model="formData.vehicleColor"
            >
            </select-data>
          </c-form-item>
          <c-form-item label="车辆配置">
            <c-input v-model="formData.vehicleConfiguration"></c-input>
            <!-- <select-data :customer="false" v-model="formData.vehicleConfiguration"> </select-data> -->
          </c-form-item>
          <c-form-item label="过户次数">
            <c-input v-model="formData.transferCount" type="number" unit="次"></c-input>
            <!-- <select-data :customer="false" v-model="formData.guidePrice"> </select-data> -->
          </c-form-item>
          <c-form-item label="所在地">
            <c-selectarea @confirm="onSelectArea"></c-selectarea>
          </c-form-item>
          <c-form-item label="详细车况" labelPosition="top">
            <c-input type="textarea" height="80rpx" v-model="formData.vehicleCondition"></c-input>
          </c-form-item>
        </c-form>
      </view>
    </view>

    <view class="card">
      <view class="p-base">
        <c-form-item label-width="180rpx" label="可见范围">
          <c-select
            :range="[
              { name: '车源圈', value: 1 },
              { name: '朋友圈', value: 2 },
              { name: '全部', value: 9 },
            ]"
            v-model="formData.visibleRange"
          ></c-select>
        </c-form-item>
      </view>
    </view>
    <view class="mt-[24rpx] bottom-btn">
      <c-button type="primary" block size="large" @click="handleSubmit()" :loading="submiting">
        提交
      </c-button>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { create, type ICarResource } from '@/api/carResource'
import { ref } from 'vue'
import selectCarType from '../components/selectCarType.vue'
import { message } from '@/utils/util'
// const data = ref<string>('data')
const clipboardContent = ref('')
const formData = ref<ICarResource>({
  type: 'default',
  pushType: 1,
  pushUserType: 1,
  visibleRange: 1,
  appCarSourceImageList: [] as any,
} as ICarResource)
const onUpload = function (e) {
  console.log(e)
  if (e.type === 'upload') {
    formData.value.appCarSourceImageList.push({
      vehicleImage: e.data,
    })
  }
}

const pasteContent = function () {
  uni.getClipboardData({
    success: (e) => {
      // console.log(e.data)
      clipboardContent.value = e.data
    },
  })
}

const onSelectType = function (e) {
  // console.log(e)
  formData.value.brandId = e.brandId
  formData.value.seriesId = e.seriesId
  formData.value.modelId = e.typeId
  formData.value.vehicleModel = `${e.brandName}/${e.seriesName}/${e.typeName}`
}

const onSelectArea = function (e) {
  // console.log(e)
  formData.value.provinceCode = e.provinceCode
  formData.value.cityCode = e.cityCode
  formData.value.districtCode = e.areaCode
  formData.value.provinceName = e.provinceName
  formData.value.cityName = e.cityName
  formData.value.districtName = e.areaName
  // formData.value.location = e.areaName
}

const submiting = ref(false)
const handleSubmit = function () {
  console.log(formData.value)
  if (formData.value.appCarSourceImageList.length < 5) {
    message.warning('请至少上传5张照片')
    return
  }
  message.loading('发布中...')
  submiting.value = true
  create(formData.value)
    .then((res) => {
      console.log(res)
      if (res.code !== 0) {
        message.warning(res.msg)
      } else {
        message.success('发布成功！')
      }
    })
    .finally(() => {
      message.hideLoading()
      submiting.value = false
    })
}
</script>

<style scoped lang="scss">
.card {
  margin: 20rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
}
</style>
