<template>
  <view>
    <c-select
      :customer="props.customer"
      :remote="remoteSearch || canRemoteSearch"
      :remoteMethod="searchMethod"
      :range="dataList"
      :labelName="props.fieldNames.label"
      :valueName="props.fieldNames.value"
      @change="onChange"
      v-bind="{ ...$attrs }"
    ></c-select>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SelectData',
})
</script>
<script setup lang="ts">
import { getDictData } from '@/api/app'
import { message } from '@/utils/util'
import type { PropType } from 'vue'
const emit = defineEmits(['update:modelValue', 'change', 'change2', 'updateDataList'])
const props = defineProps({
  api: {
    type: Function as PropType<(query?: any) => Promise<any>>,
    default: null,
    required: false,
  },
  isDict: {
    type: Boolean,
    default: false,
  },
  dictType: {
    type: String,
    default: '',
  },
  filter: {
    type: Function as PropType<(e?: any) => any>,
    default: null,
    required: false,
  },
  fieldNames: {
    type: Object as PropType<{ label: string; value: string }>,
    default: () => {
      return {
        label: 'label',
        value: 'value',
      }
    },
  },
  customer: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  immediateGet: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  query: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  searchField: {
    type: String as PropType<string>,
    default: '',
  },
  remoteSearch: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  nameFormatter: {
    type: Function as PropType<(data: AnyObject) => any>,
    default: undefined,
  },
})
// watch(
//   () => props.modelValue,
//   (val) => {
//     value.value = val || ''
//   },
//   {
//     immediate: true,
//   }
// )
const searchQuery = ref({})
const allQuery = computed(() => {
  return { ...props.query, ...searchQuery.value }
})

const canRemoteSearch = ref(false)
const dataList = ref([])
const getData = function () {
  // if (!props.api) {
  //   return []
  // }
  let request: any = props.api
  if (props.dictType) {
    request = getDictData
  }
  return request(props.isDict ? props.dictType : { ...allQuery.value })
    .then((res) => {
      if (res.code === 0) {
        let list = []
        if (props.nameFormatter) {
          list = res.datas.map((item) => {
            item[props.fieldNames.label] = props.nameFormatter(item)
            return item
          })
        } else {
          list = res.data
        }
        if (props.filter) {
          dataList.value = list.filter(props.filter)
        } else {
          dataList.value = list
        }
        emit('updateDataList', res.Datas)
        if (res.TotalCount > 1000) {
          canRemoteSearch.value = true
        }
        return res.Datas
      } else {
        message.warning(res.Msg || '获取数据失败')
        return []
      }
    })
    .catch((err) => {
      throw new Error(err.message)
    })
}

if (props.immediateGet) {
  getData()
}

const searchMethod = function (keyword: string) {
  if (!props.remoteSearch && !canRemoteSearch.value) {
    return
  }
  const searchField = props.searchField || props.fieldNames.label
  if (searchField) {
    searchQuery.value[searchField] = keyword
  }
  getData()
}

const onChange = function (e) {
  emit('update:modelValue', e)
  emit('change', e)
  const current = dataList.value.find((item) => item[props.fieldNames.value] === e)
  emit('change2', current)
}

const clearDataList = function () {
  dataList.value = []
}

defineExpose({
  getData,
  clearDataList,
  dataList: dataList.value,
})
</script>
