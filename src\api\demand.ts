import request from '@/utils/request'

export interface CarSourceImage {
  imageId?: number
  vehicleImage: string
}

export interface IDemand {
  /**
   * 	购买类型：1求购 2收购3询价
   */
  buyType: 1 | 2 | 3
  /**
   * 详细内容
   */
  content: string
  /**
   * 年份要求开始
   */
  yearRequestStart: string
  /**
   * 年份要求截至
   */
  yearRequestEnd: string
  /**
   * 里程要求
   */
  mileageRequest: string
  /**
   * 车辆颜色要求
   */
  colorRequest: number
  /**
   * 	审核状态：0 待审核 1通过 2驳回
   */
  auditStatus: number
  /**
   * seriesIds
   */
  seriesIds: string[]
  seriesNames: string[]
  carSourceImageDTOList: CarSourceImage[]
}
export interface IDemandItem extends IDemand {
  id: string
  pushUserId: string
  mobile: string
  userName: string
  userAvatar: string
  createTime: string
  cityName: string
  collectStatus: 1 | 0
  carSourceImageVoList: {
    imageId: string
    vehicleImageUrl: string
    vehicleImage: string
  }[]
  seriesList: {
    brandId: string
    seriesId: string
    brandName: string
    seriesName: string
  }[]
}
export interface IDemandListQuery {
  current: number
  size: number
  cityCode?: string
  /**
   * 购买类型：1求购 2收购 3询价
   */
  buyType?: 1 | 2 | 3

  kw?: string
}

//发布
export function create(data: IDemand) {
  return request.post({ url: '/app/appCarSourceBuy/mod', data })
}

//list
export function demandList(query: IDemandListQuery) {
  return request.post({ url: '/app/appCarSourceBuy/page', data: query })
}

export function demandDetail(id: string) {
  return request.get<IDemandItem>({
    url: '/app/appCarSourceBuy/findById',
    data: { buyId: id },
  })
}
