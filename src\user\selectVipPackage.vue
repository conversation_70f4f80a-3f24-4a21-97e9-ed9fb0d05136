<route lang="json">
{
  "style": {
    "navigationBarTitleText": "选择会员套餐",
    "navigationStyle": "custom"
  },
  "auth": true
}
</route>
<template>
  <c-page fullScreen>
    <view class="bg w-full absolute h-[420rpx]">
      <image :src="$imgUrl('/statics/vip/header_bg.png')" class="w-full" mode="top"></image>
    </view>
    <view class="header sticky top-0 z-20 h-[300rpx]">
      <view class="w-full h-full absolute">
        <image
          :src="$imgUrl('/statics/vip/header_bg2.png')"
          class="w-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col justify-start">
        <c-titlebar title="会员" showBack></c-titlebar>
        <!-- 用户信息 -->
        <view class="flex-row items-center py-[24rpx] px-[36rpx]">
          <view class="w-[76rpx] mr-[30rpx] rounded-[50%] overflow-hidden">
            <image
              :src="$imgUrl(userInfo.avatar || '/statics/user/default_avatar.png')"
              class="w-[100%]"
              mode="widthFix"
            ></image>
          </view>
          <view class="flex-1 self-start">
            <view class="mb-[12rpx]" v-if="userInfo.userId">
              <text class="text-[32rpx] text-[#965B1E] font-medium">{{ userInfo.username }}</text>
            </view>
            <view class="flex-row items-center">
              <view>
                <text class="text-[24rpx] text-secondary">有效期：2025-12-31</text>
              </view>
            </view>
          </view>
          <!-- <view class="p-[12rpx]">
            <image src="/statics/icons/setting.png" class="w-[30rpx] h-[30rpx]"></image>
          </view> -->
        </view>
      </view>
    </view>
    <!-- https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/header_bg/vip.png -->
    <view class="content-container flex-1 bg-white relative z-1 rounded-t-xl overflow-scroll">
      <view class="flex-row gap-4xl overflow-y-hidden pb-[24rpx] pl-xl pt-xl">
        <view
          class="package w-[238rpx] h-[260rpx] p-[40rpx] items-center"
          v-for="item in packageList"
          :key="item.packageId"
          :class="[currentPackage.packageId === item.packageId ? 'selected' : '']"
          @click="handleSeletPack(item)"
        >
          <view class="mb-sm">
            <text
              class="text-xl font-medium"
              :class="[currentPackage.packageId === item.packageId ? 'text-[#965B1E]' : '']"
              >{{ item.packageName }}</text
            >
          </view>
          <!-- <view>
            <text>原价</text>
            <text>￥{{ item.prePrice }}</text>
          </view> -->
          <view class="flex-row items-end mb-2xl">
            <text
              class="mb-[4rpx] text-xl font-medium"
              :class="[currentPackage.packageId === item.packageId ? 'text-[#795021]' : '']"
              >￥</text
            >
            <text
              class="text-[64rpx] font-medium"
              :class="[currentPackage.packageId === item.packageId ? 'text-[#795021]' : '']"
              >{{ item.prePrice }}</text
            >
          </view>
          <view>
            <text
              class="text-base font-medium"
              :class="[currentPackage.packageId === item.packageId ? 'text-[#965B1E]' : '']"
              >{{ computeAvgPrice(item) }}</text
            >
          </view>
        </view>
      </view>
      <!-- 会员权益 -->
      <view class="mt-base p-xl">
        <text class="mb-base text-[32rpx] font-medium">会员权益</text>
        <view class="flex-row gap-[30rpx] flex-wrap">
          <view
            class="item relative w-[330rpx] h-[154rpx] rounde-[30rpx]"
            v-for="(item, index) in equityList"
            :key="index"
          >
            <image
              class="absolute w-full h-full z-0"
              :src="$imgUrl('/statics/vip/equity_card.png')"
            ></image>
            <view class="h-full w-full p-sm flex-row items-center relative z-10">
              <view class="mr-sm">
                <image :src="$imgUrl(item.icon)" class="w-[64rpx] h-[64rpx]"></image>
              </view>
              <view>
                <text class="mb-mn text-base text-[#644017] font-medium">{{ item.name }}</text>
                <text class="text-[24rpx] text-[#AF9B90]">{{ item.desc }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 支付类型 -->
      <view>
        <view
          class="flex-row items-center justify-between px-xl py-base"
          @click="payType = 'wxpay'"
        >
          <view class="flex-row items-center">
            <image
              class="w-[44rpx] h-[44rpx] mr-sm"
              :src="$imgUrl('/statics/vip/wxpay.png')"
            ></image>
            <text>微信支付</text>
          </view>
          <view>
            <radio
              class="radio"
              color="#F8CA8D"
              @click="payType = 'wxpay'"
              :checked="payType === 'wxpay'"
            />
          </view>
        </view>
        <view
          class="flex-row items-center justify-between px-xl py-base"
          @click="payType = 'alipay'"
        >
          <view class="flex-row items-center">
            <image
              class="w-[44rpx] h-[44rpx] mr-sm"
              :src="$imgUrl('/statics/vip/alipay.png')"
            ></image>
            <text>支付宝</text>
          </view>
          <view>
            <radio
              class="radio"
              color="#F8CA8D"
              @click="payType = 'alipay'"
              :checked="payType === 'alipay'"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="btn-cell sticky bottom-0 z-20 p-[32rpx] bg-white top-line">
      <view class="flex-row justify-center items-center p-sm mb-base">
        <view class="w-[60rpx] h-60rpx">
          <radio
            class="radio"
            color="#F8CA8D"
            @click="isCheckAgreement = !isCheckAgreement"
            :checked="isCheckAgreement"
          />
        </view>
        <view @click="isCheckAgreement = !isCheckAgreement">
          <text class="text-secondary">阅读并确认</text>
        </view>
        <view>
          <text class="text-secondary">《会员服务协议》</text>
        </view>
      </view>
      <view class="btn h-[94rpx] justify-center items-center rounded-[20rpx]" @click="toPay">
        <text class="text-xl text-[#795021] font-medium">立即开通</text>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { getVipPackages, type IRegisterVipInfo } from '@/api/vip.api'
import usePayment from '@/hooks/payment'
import { useUserStore } from '@/stores/user'
import { message } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { round } from 'lodash-es'
import { ref } from 'vue'

const { userInfo } = storeToRefs(useUserStore())
const packageList = ref<IRegisterVipInfo[]>([])
const equityList = ref([
  {
    icon: 'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/vip/equity_icon.png',
    name: '免费发车',
    desc: '快速匹配买家',
  },
  {
    icon: 'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/vip/equity_icon.png',
    name: '免费发车',
    desc: '快速匹配买家',
  },
  {
    icon: 'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/vip/equity_icon.png',
    name: '免费发车',
    desc: '快速匹配买家',
  },
  {
    icon: 'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/vip/equity_icon.png',
    name: '免费发车',
    desc: '快速匹配买家',
  },
])
const currentPackage = ref<IRegisterVipInfo>({} as IRegisterVipInfo)
const getPackageList = function () {
  getVipPackages({}).then((res) => {
    if (res.code === 0) {
      packageList.value = [...res.data, ...res.data, ...res.data]
      handleSeletPack(packageList.value[0])
    } else {
      message.warning(res.msg)
    }
  })
}
onLoad(() => {
  getPackageList()
})

const computeAvgPrice = function (item) {
  if (item.packageType === 1) {
    return `${round(item.prePrice / (item.packageDuration * 30), 1)}元/天`
  }
  if (item.packageType === 2) {
    return `${round(item.prePrice / (item.packageDuration * 3), 1)}元/月`
  }
  if (item.packageType === 1) {
    return `${round(item.prePrice / (item.packageDuration * 12), 1)}元/月`
  }
}

const handleSeletPack = function (item) {
  currentPackage.value = item
}

const payType = ref<'wxpay' | 'alipay'>('wxpay')
const isCheckAgreement = ref(false)

const { handlePay } = usePayment()

const toPay = async function () {
  if (!isCheckAgreement.value) {
    message.warning('请阅读并同意会员服务协议')
    return
  }
  try {
    const res = await handlePay(payType.value, { goodsId: currentPackage.value.packageId })
  } catch (error: any) {
    console.log(error)
    message.error(error.message || error.errMsg)
  }
}
</script>

<style scoped lang="scss">
.package {
  box-sizing: border-box;
  border: 1rpx solid #fbebcb;
  border-radius: 30rpx;
  background: #ffffff;
  transition: 0.26s;
  &.selected {
    background: linear-gradient(180deg, #fbe9b5 0%, #fdf5df 44%);
  }
}

.btn-cell {
  .btn {
    background: linear-gradient(270deg, #fcb7b3 0%, #fed5a3 47%, #feeabf 100%);
  }
}
.radio {
  transform: scale(0.8);
}
</style>
