<route lang="json">
{
  "style": {
    "navigationBarTitleText": "群聊信息",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="group-info-container">
      <!-- 群聊头像和基本信息 -->
      <view class="group-header">
        <view class="avatar-section">
          <image :src="groupInfo.avatar" class="group-avatar" mode="aspectFill" />
        </view>
        <view class="group-info">
          <text class="group-name">{{ groupInfo.groupName }}</text>
          <text class="group-id">群ID: {{ groupInfo.groupId }}</text>
          <text class="member-count">{{ groupInfo.memberCount }}人</text>
        </view>
      </view>

      <!-- 群成员列表 -->
      <view class="member-section">
        <view class="section-title">群成员</view>
        <view class="member-grid">
          <view
            v-for="member in groupMembers"
            :key="member.userID"
            class="member-item"
            @click="viewMemberProfile(member)"
          >
            <image :src="member.avatar" class="member-avatar" mode="aspectFill" />
            <text class="member-name">{{ member.nickname }}</text>
          </view>
          <!-- 添加成员按钮 -->
          <view class="member-item add-member" @click="addMember">
            <view class="add-icon">+</view>
            <text class="member-name">添加</text>
          </view>
        </view>
      </view>

      <!-- 群设置 -->
      <view class="setting-section">
        <view class="section-title">群设置</view>

        <view class="setting-item" @click="editGroupName">
          <text class="setting-label">群聊名称</text>
          <view class="setting-value">
            <text class="setting-text">{{ groupInfo.groupName }}</text>
            <text class="setting-arrow">></text>
          </view>
        </view>

        <view class="setting-item" @click="editGroupNotice">
          <text class="setting-label">群公告</text>
          <view class="setting-value">
            <text class="setting-text">{{ groupInfo.notice || '暂无公告' }}</text>
            <text class="setting-arrow">></text>
          </view>
        </view>

        <view class="setting-item">
          <text class="setting-label">消息免打扰</text>
          <switch
            :checked="groupInfo.muteNotifications"
            @change="toggleMuteNotifications"
            color="#1989fa"
          />
        </view>

        <view class="setting-item">
          <text class="setting-label">置顶聊天</text>
          <switch :checked="groupInfo.isTop" @change="toggleTopChat" color="#1989fa" />
        </view>
      </view>

      <!-- 危险操作 -->
      <view class="danger-section">
        <view class="danger-item" @click="clearGroupHistory">
          <text class="danger-text">清空聊天记录</text>
        </view>

        <view class="danger-item" @click="leaveGroup">
          <text class="danger-text">退出群聊</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAllTestUsers } from '@/config/im'

// 群聊信息
const groupInfo = reactive({
  groupId: '',
  groupName: '',
  avatar: '/static/images/group/default_group.png',
  memberCount: 0,
  notice: '',
  muteNotifications: false,
  isTop: false,
})

// 群成员列表 - 使用真实用户数据
const groupMembers = ref(
  getAllTestUsers().map((user) => ({
    userID: user.userID,
    nickname: user.nickname,
    avatar: user.avatar
      ? `/static/images/user/${user.avatar}`
      : '/static/images/user/default_avatar.png',
  }))
)

// 页面加载
onLoad((options) => {
  console.log('群聊信息页面参数:', options)

  if (options) {
    groupInfo.groupId = options.groupId || ''
    groupInfo.groupName = decodeURIComponent(options.groupName || '群聊')
    groupInfo.memberCount = groupMembers.value.length
  }

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: '群聊信息',
  })
})

// 查看成员资料
const viewMemberProfile = (member: any) => {
  uni.navigateTo({
    url: `/pages/chat/settings?userId=${member.userID}&username=${encodeURIComponent(
      member.nickname
    )}`,
  })
}

// 添加成员
const addMember = () => {
  uni.showToast({
    title: '添加成员功能开发中',
    icon: 'none',
  })
}

// 编辑群名称
const editGroupName = () => {
  uni.showModal({
    title: '修改群名称',
    editable: true,
    placeholderText: '请输入新的群名称',
    success: (res) => {
      if (res.confirm && res.content) {
        groupInfo.groupName = res.content
        uni.showToast({
          title: '群名称已修改',
          icon: 'success',
        })
      }
    },
  })
}

// 编辑群公告
const editGroupNotice = () => {
  uni.showModal({
    title: '修改群公告',
    editable: true,
    placeholderText: '请输入群公告内容',
    success: (res) => {
      if (res.confirm) {
        groupInfo.notice = res.content || ''
        uni.showToast({
          title: '群公告已修改',
          icon: 'success',
        })
      }
    },
  })
}

// 切换消息免打扰
const toggleMuteNotifications = (e: any) => {
  groupInfo.muteNotifications = e.detail.value
  uni.showToast({
    title: groupInfo.muteNotifications ? '已开启免打扰' : '已关闭免打扰',
    icon: 'success',
  })
}

// 切换置顶聊天
const toggleTopChat = (e: any) => {
  groupInfo.isTop = e.detail.value
  uni.showToast({
    title: groupInfo.isTop ? '已置顶聊天' : '已取消置顶',
    icon: 'success',
  })
}

// 清空群聊记录
const clearGroupHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空群聊记录吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 实现清空群聊记录功能
        uni.showToast({
          title: '群聊记录已清空',
          icon: 'success',
        })
      }
    },
  })
}

// 退出群聊
const leaveGroup = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出该群聊吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 实现退出群聊功能
        uni.showToast({
          title: '已退出群聊',
          icon: 'success',
        })
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
  })
}
</script>

<style scoped lang="scss">
.group-info-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.group-header {
  background-color: white;
  padding: 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar-section {
  margin-right: 30rpx;
}

.group-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 2rpx solid #e0e0e0;
}

.group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.group-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.group-id {
  font-size: 28rpx;
  color: #666;
}

.member-count {
  font-size: 28rpx;
  color: #1989fa;
}

.member-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx 40rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;
}

.member-grid {
  padding: 20rpx 40rpx 40rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30rpx;
}

.member-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  border: 2rpx solid #e0e0e0;
}

.add-member {
  .add-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx dashed #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #ccc;
  }
}

.member-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  max-width: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.setting-item {
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 32rpx;
  color: #333;
}

.setting-value {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.setting-text {
  font-size: 32rpx;
  color: #666;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-arrow {
  font-size: 32rpx;
  color: #ccc;
}

.danger-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.danger-item {
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  text-align: center;
}

.danger-item:last-child {
  border-bottom: none;
}

.danger-text {
  font-size: 32rpx;
  color: #ff4d4f;
}
</style>
