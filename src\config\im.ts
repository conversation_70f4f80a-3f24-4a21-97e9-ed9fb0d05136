// IM配置文件
export const IM_CONFIG = {
  // 腾讯云IM应用ID（需要在.env中配置VITE_TIM_APP_ID）
  SDKAppID: Number(import.meta.env.VITE_TIM_APP_ID) || 1600101880,

  // 模拟好友申请数据（使用测试用户）
  MOCK_FRIEND_REQUESTS: [
    {
      userID: 'administrator',
      nickname: '管理员',
      avatar: '/static/images/user/tx.png',
      requestMessage: '你好，我是系统管理员，想加你为好友',
      requestTime: '2小时前',
      timestamp: Date.now() - 2 * 60 * 60 * 1000,
    },
    {
      userID: '19573461517477745',
      nickname: '牛牛1',
      avatar: '/static/images/user/tx.png',
      requestMessage: '看到你的车源信息，想了解一下',
      requestTime: '1天前',
      timestamp: Date.now() - 24 * 60 * 60 * 1000,
    },
    {
      userID: '19573462910530810',
      nickname: '贝贝1',
      avatar: '/static/images/user/tx.png',
      requestMessage: '朋友推荐的，想加个好友',
      requestTime: '3天前',
      timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000,
    },
  ],
}

// 格式化时间
export const formatTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else {
    return Math.floor(diff / day) + '天前'
  }
}

// 获取随机测试用户（用于测试）
export const getRandomTestUser = () => {
  const testUsers = [
    {
      userID: 'administrator',
      nickname: '管理员',
      avatar: '/static/images/user/tx.png',
      userSig: 'test_user_sig_admin', // 这里应该是真实的UserSig，但现在用于测试
    },
    {
      userID: 'test_user_001',
      nickname: '测试用户1',
      avatar: '/static/images/user/tx.png',
      userSig: 'test_user_sig_001',
    },
  ]

  // 返回第一个测试用户
  return testUsers[0]
}
