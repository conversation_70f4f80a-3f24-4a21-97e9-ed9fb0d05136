// IM配置文件
export const IM_CONFIG = {
  // 腾讯云IM应用ID（需要在.env中配置VITE_TIM_APP_ID）
  SDKAppID: Number(import.meta.env.VITE_TIM_APP_ID) || 1600101880,

  // 模拟好友申请数据（使用真实用户）
  MOCK_FRIEND_REQUESTS: [
    {
      userID: '1960535231902826498',
      nickname: '测试账号一',
      avatar: null,
      requestMessage: '你好，我是测试账号一，想加你为好友',
      requestTime: '2小时前',
      timestamp: Date.now() - 2 * 60 * 60 * 1000,
    },
    {
      userID: '1960545000583643138',
      nickname: '测试333',
      avatar: '83a9e58a30e846fd8bcdd980b1a88e5c.png',
      requestMessage: '看到你的车源信息，想了解一下',
      requestTime: '1天前',
      timestamp: Date.now() - 24 * 60 * 60 * 1000,
    },
    {
      userID: '1960535362584756226',
      nickname: '测试账号二',
      avatar: null,
      requestMessage: '朋友推荐的，想加个好友',
      requestTime: '3天前',
      timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000,
    },
  ],
}

// 格式化时间
export const formatTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else {
    return Math.floor(diff / day) + '天前'
  }
}

// 真实测试用户数据
export const TEST_USERS = [
  {
    userID: '1960535362584756226',
    username: 'user2',
    phone: '15263215022',
    nickname: '测试账号二',
    name: '测试账号二',
    avatar: null,
    userSig:
      'eJw1js0KgkAUhd9ltobcOzrXSWgjtbI2ZYHuhJniGsYwikjRu2dmy-PzHc5LFPtTaEfH3opUoY4BYDWbg-UiFTIE8dOdudfOsREpEgACar002dhHz1eeAVwTqEhFJJWOE0VS0n*Ab1NeXgafj90xoL5xZZFn9pz5pmrNrtoSFm3tsq7BwDz9YbOAPbfTOfyOaRkl8P4AMDgzrw__',
  },
  {
    userID: '1960535231902826498',
    username: 'user1',
    phone: '15263215021',
    nickname: '测试账号一',
    name: '测试账号一',
    avatar: null,
    userSig:
      'eJw1jssOgjAURP*lWw25LbS2JO5cqCFGKSx0h6U2N-hoEJFo-HcJ4nIeZzJvkiU6sJ3H2pKYUxkBwHQwW1uTmLAAyE-fy6rwHksSUwFAgUo5NrG01wZPOABUCeAhZyFVwCQTkZL-AXR9bsxeQ5Ke9XaV5lnq1vZ2KCo98cdl9zKLTSueOTr92Hk3H8EGL-05OuOCyTBS-PMFLaoz-g__',
  },
  {
    userID: '1960545000583643138',
    username: 'user3',
    phone: '13333333333',
    nickname: '测试333',
    name: '测试',
    avatar: '83a9e58a30e846fd8bcdd980b1a88e5c.png',
    userSig:
      'eJw1jssKgkAUht9ltoWd41ychBZR4EJdlRnRRnOUaUoGEwuid28wW-63j-9N9snOUy*rO0VCjpIBwHw0B9WRkPgekJ9*VKawVlckRAGAgFJOTV2ptte1Hge4FMAZdxguqWAUqfwDdOPyIFqn54XIs7IuzTU*9MmsaDbDNjVIY9Pe8HiyLIueWV5fVtOy13f3DgMufHeQi88XLCYzKQ__',
  },
]

// 获取随机测试用户（用于测试）
export const getRandomTestUser = () => {
  // 返回第一个测试用户
  return TEST_USERS[0]
}

// 获取所有测试用户
export const getAllTestUsers = () => {
  return TEST_USERS
}

// 根据用户ID获取测试用户
export const getTestUserById = (userID: string) => {
  return TEST_USERS.find((user) => user.userID === userID)
}
