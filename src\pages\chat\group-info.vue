<route lang="json">
{
  "style": {
    "navigationBarTitleText": "群聊信息",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="group-info-page">
      <!-- 群成员区域 -->
      <view class="group-members-section">
        <view class="members-container">
          <!-- 成员头像网格 -->
          <view class="members-grid">
            <view
              v-for="(member, index) in displayMembers"
              :key="member.id || index"
              class="member-item"
              @click="handleMemberClick(member, index)"
            >
              <view class="member-avatar">
                <image
                  v-if="member.type === 'member'"
                  :src="member.avatar"
                  class="avatar-img"
                  mode="aspectFill"
                />
                <view v-else-if="member.type === 'add'" class="action-avatar">
                  <text class="action-icon">+</text>
                </view>
                <view v-else-if="member.type === 'remove'" class="action-avatar">
                  <text class="action-icon">-</text>
                </view>
              </view>
              <text v-if="member.type === 'member'" class="member-name">
                {{ member.nickname }}
              </text>
            </view>
          </view>

          <!-- 更多成员 -->
          <view v-if="groupInfo.memberCount > 9" class="more-members">
            <text class="more-text" @click="showAllMembers"> 更多群成员 </text>
          </view>
        </view>
      </view>

      <!-- 群设置区域 -->
      <view class="group-settings-section">
        <!-- 群聊名称 -->
        <view class="setting-item" @click="editGroupName">
          <text class="setting-title">群聊名称</text>
          <view class="setting-right">
            <text class="setting-value">{{ groupInfo.name }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 备注 -->
        <view class="setting-item" @click="editGroupRemark">
          <text class="setting-title">备注</text>
          <view class="setting-right">
            <text class="setting-value">{{ groupInfo.remark || '无' }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 查找聊天内容 -->
        <view class="setting-item" @click="searchChatContent">
          <text class="setting-title">查找聊天内容</text>
          <view class="setting-right">
            <text class="arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 群功能设置 -->
      <view class="group-functions-section">
        <!-- 消息免打扰 -->
        <view class="setting-item">
          <text class="setting-title">消息免打扰</text>
          <view class="setting-right">
            <switch
              :checked="groupSettings.doNotDisturb"
              @change="toggleDoNotDisturb"
              color="#07C160"
            />
          </view>
        </view>

        <!-- 置顶聊天 -->
        <view class="setting-item">
          <text class="setting-title">置顶聊天</text>
          <view class="setting-right">
            <switch :checked="groupSettings.isTop" @change="toggleTopChat" color="#07C160" />
          </view>
        </view>

        <!-- 我在本群的昵称 -->
        <view class="setting-item" @click="editMyNickname">
          <text class="setting-title">我在本群的昵称</text>
          <view class="setting-right">
            <text class="setting-value">{{ groupSettings.myNickname || '密道瓦的猎头牌' }}</text>
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 显示群成员昵称 -->
        <view class="setting-item">
          <text class="setting-title">显示群成员昵称</text>
          <view class="setting-right">
            <switch
              :checked="groupSettings.showMemberNickname"
              @change="toggleShowMemberNickname"
              color="#07C160"
            />
          </view>
        </view>
      </view>

      <!-- 群管理 -->
      <view class="group-management-section">
        <!-- 清空聊天记录 -->
        <view class="setting-item" @click="clearChatHistory">
          <text class="setting-title">清空聊天记录</text>
          <view class="setting-right">
            <text class="arrow">›</text>
          </view>
        </view>

        <!-- 投诉 -->
        <view class="setting-item" @click="reportGroup">
          <text class="setting-title">投诉</text>
          <view class="setting-right">
            <text class="arrow">›</text>
          </view>
        </view>
      </view>

      <!-- 退出群聊 -->
      <view class="exit-group-section">
        <view class="exit-btn" @click="exitGroup">
          <text class="exit-text">退出群聊</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { getAllTestUsers } from '@/config/im'

interface GroupMember {
  id: string
  nickname: string
  avatar: string
  type: 'member' | 'add' | 'remove'
}

interface GroupInfo {
  id: string
  name: string
  remark: string
  memberCount: number
  members: GroupMember[]
}

interface GroupSettings {
  doNotDisturb: boolean
  isTop: boolean
  myNickname: string
  showMemberNickname: boolean
}

// 生成真实群成员数据
const generateGroupMembers = (): GroupMember[] => {
  const realUsers = getAllTestUsers()
  return realUsers.map((user) => ({
    id: user.userID,
    nickname: user.nickname,
    avatar: user.avatar
      ? `/static/images/user/${user.avatar}`
      : '/static/images/user/default_avatar.png',
    type: 'member' as const,
  }))
}

const groupInfo = reactive<GroupInfo>({
  id: '',
  name: '车友交流群(3)',
  remark: '',
  memberCount: 3,
  members: generateGroupMembers(),
})

const groupSettings = reactive<GroupSettings>({
  doNotDisturb: true,
  isTop: true,
  myNickname: '密道瓦的猎头牌',
  showMemberNickname: true,
})

// 显示的成员列表（前9个成员 + 添加/删除按钮）
const displayMembers = computed(() => {
  const members = groupInfo.members.slice(0, 9)
  members.push(
    { id: 'add', nickname: '', avatar: '', type: 'add' },
    { id: 'remove', nickname: '', avatar: '', type: 'remove' }
  )
  return members
})

onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.groupId) {
    groupInfo.id = options.groupId
    if (options.groupName) {
      groupInfo.name = decodeURIComponent(options.groupName)
    }
  }
})

const handleMemberClick = (member: GroupMember, index: number) => {
  if (member.type === 'add') {
    addGroupMember()
  } else if (member.type === 'remove') {
    removeGroupMember()
  } else {
    viewMemberProfile(member.id)
  }
}

const addGroupMember = () => {
  uni.showToast({
    title: '添加群成员',
    icon: 'none',
  })
}

const removeGroupMember = () => {
  uni.showToast({
    title: '删除群成员',
    icon: 'none',
  })
}

const viewMemberProfile = (memberId: string) => {
  uni.navigateTo({
    url: '/pages/user/profile?userId=' + memberId,
  })
}

const showAllMembers = () => {
  uni.showToast({
    title: '查看所有成员',
    icon: 'none',
  })
}

const editGroupName = () => {
  uni.showToast({
    title: '编辑群名称',
    icon: 'none',
  })
}

const editGroupRemark = () => {
  uni.showToast({
    title: '编辑群备注',
    icon: 'none',
  })
}

const editMyNickname = () => {
  uni.showToast({
    title: '编辑我的昵称',
    icon: 'none',
  })
}

const searchChatContent = () => {
  uni.showToast({
    title: '搜索聊天内容',
    icon: 'none',
  })
}

const toggleDoNotDisturb = (e: any) => {
  groupSettings.doNotDisturb = e.detail.value
  uni.showToast({
    title: groupSettings.doNotDisturb ? '已开启免打扰' : '已关闭免打扰',
    icon: 'success',
  })
}

const toggleTopChat = (e: any) => {
  groupSettings.isTop = e.detail.value
  uni.showToast({
    title: groupSettings.isTop ? '已置顶聊天' : '已取消置顶',
    icon: 'success',
  })
}

const toggleShowMemberNickname = (e: any) => {
  groupSettings.showMemberNickname = e.detail.value
  uni.showToast({
    title: groupSettings.showMemberNickname ? '已显示群成员昵称' : '已隐藏群成员昵称',
    icon: 'success',
  })
}

const clearChatHistory = () => {
  uni.showModal({
    title: '清空聊天记录',
    content: '确定要清空该群的所有聊天记录吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '聊天记录已清空',
          icon: 'success',
        })
      }
    },
  })
}

const reportGroup = () => {
  uni.navigateTo({
    url: '/pages/report/report?groupId=' + groupInfo.id,
  })
}

const exitGroup = () => {
  uni.showModal({
    title: '退出群聊',
    content: '确定要退出该群聊吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已退出群聊',
          icon: 'success',
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
  })
}
</script>

<style scoped lang="scss">
.group-info-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.group-members-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.members-container {
  padding: 30rpx;
}
.setting-right,
.setting-item {
  flex-direction: row;
}

.members-grid {
  display: flex !important;
  flex-wrap: wrap;
  justify-content: flex-start;
  flex-direction: row;
}

.member-item {
  width: 120rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;

  &:nth-child(5n) {
    margin-right: 0;
  }
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  margin: 0 auto 10rpx;
  border-radius: 100rpx;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.action-avatar {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.action-icon {
  font-size: 60rpx;
  color: #999;
}

.member-name {
  font-size: 24rpx;
  color: #333;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-members {
  text-align: center;
  margin-top: 20rpx;
}

.more-text {
  font-size: 28rpx;
  color: #1989fa;
}

.group-settings-section,
.group-functions-section,
.group-management-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }
}

.setting-title {
  font-size: 30rpx;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.arrow {
  font-size: 32rpx;
  color: #c7c7cc;
  font-weight: 300;
  transform: scaleX(0.8);
}

.exit-group-section {
  background-color: white;
}

.exit-btn {
  padding: 30rpx;
  text-align: center;

  &:active {
    background-color: #fef2f2;
  }
}

.exit-text {
  font-size: 30rpx;
  color: #ff4757;
}
</style>
