<route lang="json">
{
  "style": {
    "navigationBarTitleText": "互动通知"
  }
}
</route>

<template>
  <c-page>
    <view class="interaction-notifications">
      <!-- 头部筛选下拉 -->
      <view class="filter-header">
        <view class="filter-dropdown" @click="toggleDropdown">
          <text class="filter-title">{{ currentFilterLabel }}</text>
          <text class="dropdown-arrow" :class="{ active: showDropdown }">▼</text>
        </view>

        <!-- 下拉菜单 -->
        <view v-if="showDropdown" class="dropdown-menu">
          <view
            v-for="tab in filterTabs"
            :key="tab.value"
            :class="['dropdown-item', { active: currentFilter === tab.value }]"
            @click="selectFilter(tab.value, tab.label)"
          >
            {{ tab.label }}
          </view>
        </view>
      </view>

      <!-- 通知列表 -->
      <view class="notification-list">
        <view
          v-for="notification in state.notifications"
          :key="notification.id"
          class="notification-item"
          @click="handleNotificationClick(notification)"
        >
          <!-- 用户头像 -->
          <view class="user-avatar">
            <image
              :src="notification.userAvatar || '/static/images/user/default_avatar.png'"
              class="avatar-image"
            />
            <!-- 互动类型图标 -->
            <view :class="['interaction-badge', getInteractionBadgeClass(notification.type)]">
              <text class="badge-icon">{{ getInteractionIcon(notification.type) }}</text>
            </view>
          </view>

          <!-- 通知内容 -->
          <view class="notification-content">
            <view class="user-name">{{ notification.userName }}</view>
            <view class="notification-message">{{ notification.content }}</view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-button" @click.stop="handleAction(notification)">
            <text class="action-text">{{ getActionText(notification) }}</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="filteredNotifications.length === 0" class="empty-state">
          <text class="empty-text">暂无{{ getCurrentFilterLabel() }}通知</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <c-loadmore :status="loadStatus" @reload="loadMore"></c-loadmore>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, onUnmounted } from 'vue'
import { notificationManager, type InteractionNotification } from '@/utils/notification'

// 筛选标签
const filterTabs = [
  { label: '全部', value: 'all' },
  { label: '关注', value: 'follow' },
  { label: '点赞', value: 'like' },
  { label: '评论', value: 'comment' },
  { label: '分享', value: 'share' },
]

const state = reactive({
  notifications: [] as InteractionNotification[],
  currentFilter: 'all',
  loadStatus: 'loadend' as 'loading' | 'loadend' | 'nomore',
  showDropdown: false,
  currentFilterLabel: '新关注我的',
})

// 计算属性
const filteredNotifications = computed(() => {
  if (state.currentFilter === 'all') {
    return state.notifications
  }
  return state.notifications.filter((n) => n.type === state.currentFilter)
})

const unreadCount = computed(() => {
  return filteredNotifications.value.filter((n) => !n.isRead).length
})

// 获取当前筛选标签名称
const getCurrentFilterLabel = () => {
  const tab = filterTabs.find((t) => t.value === state.currentFilter)
  return tab?.label || '全部'
}

// 获取互动图标
const getInteractionIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    follow: '👤',
    like: '❤️',
    comment: '💬',
    share: '📤',
  }
  return iconMap[type] || '👤'
}

// 获取互动徽章样式类
const getInteractionBadgeClass = (type: string) => {
  const classMap: Record<string, string> = {
    follow: 'follow-badge',
    like: 'like-badge',
    comment: 'comment-badge',
    share: 'share-badge',
  }
  return classMap[type] || 'follow-badge'
}

// 切换筛选
const switchFilter = (filter: string) => {
  state.currentFilter = filter
}

// 切换下拉菜单
const toggleDropdown = () => {
  state.showDropdown = !state.showDropdown
}

// 选择筛选项
const selectFilter = (value: string, label: string) => {
  state.currentFilter = value
  state.currentFilterLabel = label
  state.showDropdown = false
}

// 计算属性
const currentFilterLabel = computed(() => state.currentFilterLabel)
const showDropdown = computed(() => state.showDropdown)
const currentFilter = computed(() => state.currentFilter)
const loadStatus = computed(() => state.loadStatus)

// 处理通知点击
const handleNotificationClick = (notification: InteractionNotification) => {
  // 标记为已读
  if (!notification.isRead) {
    notificationManager.markInteractionNotificationRead(notification.id)
    notification.isRead = true
  }

  // 跳转到用户详情页
  uni.navigateTo({
    url: `/pages/user/profile?userId=${notification.userId}&username=${encodeURIComponent(
      notification.userName
    )}`,
  })
}

// 标记全部已读
const markAllRead = () => {
  filteredNotifications.value.forEach((notification) => {
    if (!notification.isRead) {
      notificationManager.markInteractionNotificationRead(notification.id)
      notification.isRead = true
    }
  })

  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
}

// 加载更多
const loadMore = () => {
  // 这里可以实现分页加载逻辑
  state.loadStatus = 'nomore'
}

// 监听通知更新
const handleNotificationUpdate = (type: 'system' | 'interaction', data: any) => {
  if (type === 'interaction') {
    loadNotifications()
  }
}

// 加载通知数据
const loadNotifications = () => {
  state.notifications = notificationManager.getInteractionNotifications()
}

// 生命周期
onMounted(() => {
  loadNotifications()
  notificationManager.addListener(handleNotificationUpdate)
})

onUnmounted(() => {
  notificationManager.removeListener(handleNotificationUpdate)
})
</script>

<style scoped lang="scss">
.interaction-notifications {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filter-header {
  background-color: white;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.filter-dropdown {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  flex-direction: row;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dropdown-arrow {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s;

  &.active {
    transform: rotate(180deg);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
  z-index: 10;
}

.dropdown-item {
  padding: 24rpx 40rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f8f8f8;

  &.active {
    background-color: #f0f8ff;
    color: #007aff;
  }

  &:active {
    background-color: #f5f5f5;
  }
}

.notification-list {
  display: flex;
  flex-direction: column;
}

.notification-item {
  background-color: white;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex-direction: row;
  &:active {
    background-color: #f5f5f5;
  }
}

.user-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.interaction-badge {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;

  &.follow-badge {
    background-color: #007aff;
  }

  &.like-badge {
    background-color: #ff4757;
  }

  &.comment-badge {
    background-color: #2ed573;
  }

  &.share-badge {
    background-color: #ffa502;
  }
}

.badge-icon {
  font-size: 18rpx;
  color: white;
}

.notification-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notification-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.action-button {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background-color: #007aff;

  &.followed {
    background-color: #f0f0f0;
  }
}

.action-text {
  font-size: 26rpx;
  color: white;

  .followed & {
    color: #666;
  }
}

.notification-time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.notification-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.unread-dot {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4757;
  border-radius: 50%;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
