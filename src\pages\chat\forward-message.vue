<route lang="json">
{
  "style": {
    "navigationBarTitleText": "转发消息",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="forward-container">
      <!-- 消息预览 -->
      <view class="message-preview">
        <view class="preview-header">
          <text class="preview-title">转发内容</text>
        </view>
        <view class="preview-content">
          <view class="message-bubble">
            <text class="message-text">{{ messageContent }}</text>
          </view>
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-box">
          <image src="/static/images/im/icon.png" class="search-icon" mode="aspectFit" />
          <input 
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索联系人或群聊"
            @input="onSearch"
          />
        </view>
      </view>

      <!-- 最近聊天 -->
      <view class="recent-section">
        <view class="section-header">
          <text class="section-title">最近聊天</text>
        </view>
        <view class="conversation-list">
          <view 
            v-for="conversation in filteredRecentChats" 
            :key="conversation.id"
            class="conversation-item"
            :class="{ 'selected': isSelected(conversation) }"
            @click="toggleSelection(conversation)"
          >
            <view class="conversation-info">
              <image :src="conversation.avatar" class="conversation-avatar" mode="aspectFill" />
              <view class="conversation-details">
                <text class="conversation-name">{{ conversation.name }}</text>
                <text class="conversation-type">{{ conversation.type === 'group' ? '群聊' : '单聊' }}</text>
              </view>
            </view>
            <view class="select-checkbox" :class="{ 'checked': isSelected(conversation) }">
              <text v-if="isSelected(conversation)" class="check-mark">✓</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 好友列表 -->
      <view class="friends-section">
        <view class="section-header">
          <text class="section-title">好友</text>
        </view>
        <view class="friends-list">
          <view 
            v-for="friend in filteredFriends" 
            :key="friend.userID"
            class="friend-item"
            :class="{ 'selected': isSelected(friend) }"
            @click="toggleSelection(friend)"
          >
            <view class="friend-info">
              <image :src="friend.avatar" class="friend-avatar" mode="aspectFill" />
              <view class="friend-details">
                <text class="friend-name">{{ friend.nickname }}</text>
                <text class="friend-id">ID: {{ friend.userID }}</text>
              </view>
            </view>
            <view class="select-checkbox" :class="{ 'checked': isSelected(friend) }">
              <text v-if="isSelected(friend)" class="check-mark">✓</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 已选择的联系人 -->
      <view v-if="selectedTargets.length > 0" class="selected-section">
        <view class="selected-header">
          <text class="selected-title">已选择 {{ selectedTargets.length }} 个联系人</text>
        </view>
        <view class="selected-list">
          <view 
            v-for="target in selectedTargets" 
            :key="target.id"
            class="selected-item"
            @click="removeSelection(target)"
          >
            <image :src="target.avatar" class="selected-avatar" mode="aspectFill" />
            <text class="selected-name">{{ target.name }}</text>
            <view class="remove-btn">×</view>
          </view>
        </view>
      </view>

      <!-- 转发按钮 -->
      <view class="forward-section">
        <view 
          class="forward-btn"
          :class="{ 'disabled': selectedTargets.length === 0 }"
          @click="forwardMessage"
        >
          <text class="forward-text">转发 ({{ selectedTargets.length }})</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import imManager from '@/utils/im'
import { message } from '@/utils/util'

interface ForwardTarget {
  id: string
  name: string
  avatar: string
  type: 'user' | 'group'
  userID?: string
  groupID?: string
}

interface Conversation {
  id: string
  name: string
  avatar: string
  type: 'user' | 'group'
  lastMessage: string
  time: number
}

interface Friend {
  userID: string
  nickname: string
  avatar: string
}

// 响应式数据
const messageContent = ref('')
const messageId = ref('')
const searchKeyword = ref('')
const recentChats = ref<Conversation[]>([])
const friendList = ref<Friend[]>([])
const selectedTargets = ref<ForwardTarget[]>([])

// 计算属性
const filteredRecentChats = computed(() => {
  if (!searchKeyword.value) return recentChats.value
  
  return recentChats.value.filter(chat => 
    chat.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredFriends = computed(() => {
  if (!searchKeyword.value) return friendList.value
  
  return friendList.value.filter(friend => 
    friend.nickname.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    friend.userID.includes(searchKeyword.value)
  )
})

// 生命周期
onLoad((options) => {
  messageContent.value = decodeURIComponent(options.content || '')
  messageId.value = options.messageId || ''
})

onMounted(() => {
  loadRecentChats()
  loadFriendList()
})

// 加载最近聊天
const loadRecentChats = async () => {
  try {
    const conversations = await imManager.getConversationList()
    recentChats.value = conversations.map((conv: any) => ({
      id: conv.conversationID,
      name: conv.userProfile?.nick || conv.groupProfile?.name || '未知',
      avatar: conv.userProfile?.avatar || conv.groupProfile?.avatar || '/static/images/user/tx.png',
      type: conv.type === 'C2C' ? 'user' : 'group',
      lastMessage: conv.lastMessage?.messageForShow || '',
      time: conv.lastMessage?.lastTime || 0
    }))
  } catch (error) {
    console.error('加载最近聊天失败:', error)
  }
}

// 加载好友列表
const loadFriendList = async () => {
  try {
    const friends = await imManager.getFriendList()
    friendList.value = friends.map((friend: any) => ({
      userID: friend.userID,
      nickname: friend.nick || friend.userID,
      avatar: friend.avatar || '/static/images/user/tx.png'
    }))
  } catch (error) {
    console.error('加载好友列表失败:', error)
  }
}

// 搜索
const onSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 检查是否已选择
const isSelected = (item: any): boolean => {
  if (item.userID) {
    // 好友
    return selectedTargets.value.some(target => target.id === item.userID)
  } else {
    // 会话
    return selectedTargets.value.some(target => target.id === item.id)
  }
}

// 切换选择状态
const toggleSelection = (item: any) => {
  if (isSelected(item)) {
    removeSelection(item)
  } else {
    addSelection(item)
  }
}

// 添加选择
const addSelection = (item: any) => {
  let target: ForwardTarget
  
  if (item.userID) {
    // 好友
    target = {
      id: item.userID,
      name: item.nickname,
      avatar: item.avatar,
      type: 'user',
      userID: item.userID
    }
  } else {
    // 会话
    target = {
      id: item.id,
      name: item.name,
      avatar: item.avatar,
      type: item.type,
      userID: item.type === 'user' ? item.id.replace('C2C', '') : undefined,
      groupID: item.type === 'group' ? item.id.replace('GROUP', '') : undefined
    }
  }
  
  selectedTargets.value.push(target)
}

// 移除选择
const removeSelection = (item: any) => {
  const id = item.userID || item.id
  const index = selectedTargets.value.findIndex(target => target.id === id)
  if (index > -1) {
    selectedTargets.value.splice(index, 1)
  }
}

// 转发消息
const forwardMessage = async () => {
  if (selectedTargets.value.length === 0) return

  try {
    const promises = selectedTargets.value.map(async (target) => {
      const conversationType = target.type === 'group' ? 'GROUP' : 'C2C'
      const targetId = target.userID || target.groupID || target.id
      
      return await imManager.sendTextMessage(targetId, messageContent.value, conversationType)
    })

    await Promise.all(promises)
    
    message.success(`已转发给 ${selectedTargets.value.length} 个联系人`)
    
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    
  } catch (error) {
    console.error('转发消息失败:', error)
    message.error('转发失败，请重试')
  }
}
</script>

<style scoped lang="scss">
.forward-container {
  padding: 20rpx;
  padding-bottom: 200rpx;
}

.message-preview {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.preview-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.preview-title {
  font-size: 28rpx;
  color: #666;
}

.preview-content {
  padding: 24rpx;
}

.message-bubble {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.search-section {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 25rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
}

.recent-section,
.friends-section {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.conversation-list,
.friends-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.conversation-item,
.friend-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.conversation-item:last-child,
.friend-item:last-child {
  border-bottom: none;
}

.conversation-item:active,
.friend-item:active {
  background-color: #f8f8f8;
}

.conversation-item.selected,
.friend-item.selected {
  background-color: #e8f4ff;
}

.conversation-info,
.friend-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.conversation-avatar,
.friend-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.conversation-details,
.friend-details {
  flex: 1;
}

.conversation-name,
.friend-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.conversation-type,
.friend-id {
  font-size: 24rpx;
  color: #999;
}

.select-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.select-checkbox.checked {
  background-color: #1989fa;
  border-color: #1989fa;
}

.check-mark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.selected-section {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.selected-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.selected-title {
  font-size: 28rpx;
  color: #1989fa;
  font-weight: bold;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 24rpx;
}

.selected-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}

.selected-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
}

.selected-name {
  font-size: 20rpx;
  color: #666;
  text-align: center;
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
}

.forward-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
}

.forward-btn {
  width: 100%;
  padding: 32rpx;
  background-color: #1989fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.forward-btn.disabled {
  background-color: #ccc;
}

.forward-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
