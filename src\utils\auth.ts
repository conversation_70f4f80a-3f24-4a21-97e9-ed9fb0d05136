import { TOKEN_KEY } from '@/enums/cacheEnums'
import cache from './cache'
import { navigateTo, showModalSync, toLogin } from './util'
import { useUserStore } from '@/stores/user'

export function getToken() {
  return cache.get(TOKEN_KEY) || ''
}

export function removeToken() {
  cache.remove(TOKEN_KEY)
}

export const isDevEnv = import.meta.env.MODE === 'development'

export const checkRegister = function () {
  const { verifyInfo } = useUserStore()
  if (isDevEnv) {
    return true
  }
  return !verifyInfo.isExpired && verifyInfo.verifyed
}
export const registerIntercept = function (
  options: {
    ok: (_params?: any) => any | undefined
    confirm?: () => any | undefined
    cancel?: () => any | undefined
  },
  params?: any
) {
  if (!getToken()) {
    toLogin()
    return
  }
  if (!checkRegister()) {
    showModalSync({
      title: '温馨提示',
      content: '注册会员查看更多车源，直接拨打车主电话。车商专用内网，私家用户请勿注册。',
    })
      .then(() => {
        navigateTo('/user/register')
        if (!!options?.confirm) options?.confirm()
      })
      .catch(() => {
        if (!!options?.cancel) options?.cancel()
      })
    return false
  }
  return options.ok(params)
}
