<route lang="json">
{
  "style": {
    "navigationBarTitleText": "个人中心",
    "navigationStyle": "custom"
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen hasBottomBar>
    <view class="header pb-[120rpx]">
      <view class="bg h-full">
        <image :src="$imgUrl('/statics/images/header_bg.png')" class="w-full h-full"></image>
      </view>
      <view class="content">
        <c-titlebar title="个人中心"></c-titlebar>
        <!-- 用户信息 -->
        <view class="flex-row items-center py-[24rpx] px-[36rpx]">
          <view class="w-[124rpx] mr-[30rpx] rounded-[50%] overflow-hidden">
            <image
              :src="$imgUrl(userInfo.avatar || '/statics/user/default_avatar.png')"
              class="w-[100%]"
              mode="widthFix"
            ></image>
          </view>
          <view class="flex-1 self-start">
            <view class="mb-[24rpx]" v-if="userInfo.userId">
              <text class="text-[36rpx] text-title">{{ userInfo.username }}</text>
            </view>
            <view class="mb-[24rpx]" v-else @click="toLogin">
              <text class="text-[36rpx] text-title">去登录</text>
            </view>
            <view class="mb-[24rpx]" @click="toVerify">
              <text class="text-[36rpx] text-title">去注册</text>
            </view>
            <view class="flex-row items-center">
              <view class="mr-[12rpx]">
                <text class="text-sm text-secondary">来自：-</text>
              </view>
              <view class="h-[20rpx] w-[2rpx] bg-secondary mt-[4rpx]"></view>
              <view class="ml-[12rpx]">
                <text class="text-sm text-secondary">有效期：-</text>
              </view>
            </view>
          </view>
          <!-- <view class="p-[12rpx]">
            <image src="/statics/icons/setting.png" class="w-[30rpx] h-[30rpx]"></image>
          </view> -->
        </view>
        <!-- 用户数据 -->
        <view class="flex-row items-center justify-between py-sm px-4xl">
          <view class="data flex-1 flex-row items-center justify-between mr-[48rpx]">
            <view>
              <text class="mb-[12rpx] text-xl text-title font-bold">2280</text>
              <text class="text-secondary text-base">关注</text>
            </view>
            <view>
              <text class="mb-[12rpx] text-xl text-title font-bold">280</text>
              <text class="text-secondary text-base">粉丝</text>
            </view>
            <view>
              <text class="mb-[12rpx] text-xl text-title font-bold">2260</text>
              <text class="text-secondary text-base">收藏</text>
            </view>
          </view>
          <view class="operation flex-row ml-[48rpx]">
            <view class="btn h-[56rpx] justify-center items-center px-[24rpx] rounded-[28rpx]">
              <text class="text-base text-primary">编辑资料</text>
            </view>
            <view
              class="btn ml-sm h-[56rpx] justify-center items-center px-[24rpx] rounded-[28rpx]"
              @click="navigateTo('/user/user_set')"
            >
              <c-icon type="setting" size="28" color="#0767FF"></c-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="relative mt-[-120rpx] z-10">
      <!-- 运营数据 -->
      <view
        class="h-[154rpx] flex-row items-center justify-around m-[16rpx] rounded-[30rpx] bg-white"
      >
        <!-- 数据 -->
        <view class="item items-center">
          <view class="mb-[20rpx]">
            <text class="text-main text-[40rpx] font-bold">12333</text>
          </view>
          <view>
            <text class="text-muted text-[28rpx]">成交(辆)</text>
          </view>
        </view>
        <view class="item items-center">
          <view class="mb-[20rpx]">
            <text class="text-main text-[40rpx] font-bold">789926</text>
          </view>
          <view>
            <text class="text-muted text-[28rpx]">担保成交(辆)</text>
          </view>
        </view>
        <view class="item flex flex-col items-center">
          <view class="mb-[20rpx]">
            <text class="text-main text-[40rpx] font-bold">1969</text>
          </view>
          <view>
            <text class="text-muted text-[28rpx]">总计(辆)</text>
          </view>
        </view>
      </view>
      <view class="m-[16rpx] rounded-[30rpx] px-[24rpx] py-[10rpx] bg-white">
        <c-list-item
          title="我的圈子"
          :icon="$imgUrl('/statics/user/clique.png')"
          :showLine="false"
        ></c-list-item>
      </view>
      <view
        class="flex flex-col gap-[24rpx] m-[16rpx] rounded-[30rpx] px-[24rpx] py-[10rpx] bg-white"
      >
        <c-list-item title="服务" :icon="$imgUrl('/statics/user/service.png')"></c-list-item>
        <c-list-item title="个人功能" :icon="$imgUrl('/statics/user/person.png')"></c-list-item>
        <c-list-item
          title="商家版"
          :icon="$imgUrl('/statics/user/shop.png')"
          :showLine="false"
        ></c-list-item>
      </view>
    </view>
    <c-tabbar :tabIndex="4"></c-tabbar>
  </c-page>
</template>

<script setup lang="ts">
import { getDecorate } from '@/api/home'
import { useUserStore } from '@/stores/user'
import { useClientStore } from '@/stores/client'
import { onShow, onTabItemTap } from '@dcloudio/uni-app'
import { storeToRefs } from 'pinia'
import { reactive } from 'vue'
import { navigateTo, toLogin } from '@/utils/util'
const state = reactive<{
  pages: any[]
}>({
  pages: [],
})

const { clientInfo } = useClientStore()
// console.log(clientInfo)
const getData = async () => {
  const { data } = await getDecorate({ id: 2 })
  state.pages = JSON.parse(data.pageData)
}
const userStore = useUserStore()
const { userInfo, isLogin } = storeToRefs(userStore)
onTabItemTap(async (e) => {
  console.log(e)
  try {
    await userStore.getUser()
  } catch (error) {
    console.log(error)
  }
})

getData()

const toVerify = function () {
  navigateTo('/user/register')
}
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  .bg {
    position: absolute;
    z-index: 0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }
  .content {
    position: relative;
    // z-index: 0;
    // top: 0;
    // bottom: 0;
    // left: 0;
    // right: 0;
    z-index: 1;
  }
}

.operation {
  .btn {
    border: 1rpx solid $color-primary;
  }
}
</style>
