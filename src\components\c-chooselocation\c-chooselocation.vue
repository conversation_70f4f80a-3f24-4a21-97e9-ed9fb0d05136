<template>
  <view class="cell flex-row items-center" @click="chooseLocation">
    <view class="flex-1 overflow-hidden">
      <text
        class="overflow-ellipsis whitespace-nowrap overflow-hidden text-[30rpx]"
        v-if="locationInfo.address || props.address"
        >{{ locationInfo.address || props.address }}</text
      >
      <text v-else class="placeholder text-[30rpx]">请选择位置</text>
    </view>
    <c-icon type="jinru" size="32" color="#c3c3c3"></c-icon>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'ChooseLocation',
})
</script>
<script setup lang="ts">
import { defineComponent, ref } from 'vue'
const props = defineProps({
  address: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['confirm'])
const locationInfo = ref<UniApp.ChooseLocationSuccess>({} as UniApp.ChooseLocationSuccess)
const chooseLocation = function () {
  uni.chooseLocation({
    success(res) {
      console.log(res)
      locationInfo.value = res
      emit('confirm', res)
    },
  })
}
</script>

<style scoped lang="scss">
.cell {
  .placeholder {
    color: #8a929f;
  }
}
</style>
