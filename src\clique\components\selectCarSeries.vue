<template>
  <view class="">
    <view class="cell flex-row items-center" @click="onSelect">
      <view class="flex-1">
        <!-- <text v-if="selectedList.length" class="text-[30rpx]">{{ desc }}</text> -->
        <view v-if="selectedList.length" class="flex-row items-center gap-mn flex-wrap">
          <view v-for="item in selectedList" :key="item.seriesId" class="flex-row tag bg-primary/5">
            <view class="mr-mn"
              ><text class="warp text-primary text-sm">{{ item.seriesName }}</text></view
            >
            <view @click="handleRemove(item)"
              ><c-icon type="plus" size="30" color="#0767FF"></c-icon
            ></view>
          </view>
        </view>
        <text v-else class="text-[30rpx] placeholder">请选择</text>
      </view>
      <c-icon type="jinru" size="32" color="#c3c3c3"></c-icon>
    </view>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SelectCarType',
})
</script>
<script setup lang="ts">
import type { ISeries } from '@/api/carResource'
import { navigateTo } from '@/utils/util'
// import { onLoad } from '@dcloudio/uni-app'
import { defineComponent, ref } from 'vue'
const emit = defineEmits(['confirm'])
const selectedList = ref<ISeries[]>([])
const desc = computed(() => {
  return selectedList.value.map((item) => item.seriesName).join('/')
})
// const multiple = ref(false)
// onLoad((options) => {
//   if (options?.multiple === '1') {
//     multiple.value = true
//   }
// })

const onSelect = function () {
  uni.$once('selectSeries', (e) => {
    if (!selectedList.value.some((item) => item.seriesId === e.seriesId)) {
      selectedList.value.push(e)
    }
    emit('confirm', selectedList.value)
  })
  navigateTo('/clique/info/carBrand?level=2')
}

const handleRemove = function (e: ISeries) {
  selectedList.value = selectedList.value.filter((item) => item.seriesId !== e.seriesId)
}
</script>

<style scoped lang="scss">
.cell {
  .placeholder {
    color: #8a929f;
  }
}
.tag {
  padding: 3rpx 10rpx;
  border-radius: 12rpx;
  border: 1rpx solid $color-primary;
}
</style>
