<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <subscriptionItem v-for="item in state.dataList" :key="item.id" :data="item"></subscriptionItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import type { IFriend } from 'types/chat.interface'
import subscriptionItem from './subscriptionItem.vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
type Status = 'all' | 'friend'

const state = reactive({
  currentTab: 'carSource',
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all' as Status,
  },
  dataList: [] as IFriend[],
})
const dataList: IFriend[] = [
  {
    id: '1',
    avatar: '',
    username: '福特车源服务交流群',
  },
  {
    id: '2',
    avatar: '',
    username: '福特车源服务交流群',
  },
  {
    id: '3',
    avatar: '',
    username: '福特车源服务交流群',
  },
  {
    id: '4',
    avatar: '',
    username: '福特车源服务交流群',
  },
  {
    id: '5',
    avatar: '',
    username: '福特车源服务交流群',
  },
]

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((reslove) => {
    setTimeout(() => {
      const _dataList = cloneDeep(dataList)
      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      reslove(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}
// getData()
const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

const switchStatus = function (status: Status) {
  state.queryForm.status = status
  initData()
}

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss">
.btn {
  border: 1rpx solid #f4f6fa;
}
</style>
