<template>
  <view class="bottom-line flex-row items-start bg-white m-[30rpx] pb-[46rpx]">
    <view class="avatar w-[60rpx] h-[60rpx] rounded-[30rpx] shrink-0 overflow-hidden">
      <image
        :src="props.data.avatar || '/static/user/default_avatar.png'"
        class="w-[100%]"
        mode="widthFix"
      ></image>
    </view>
    <view class="flex-1 ml-[20rpx] pr-[24rpx]">
      <!-- 用户信息 -->
      <view class="mb-[20rpx]">
        <view class="flex-row items-center justify-between">
          <view class="mb-[8rpx]">
            <text class="username text-[#576B95] font-medium">{{ props.data.username }}</text>
          </view>
          <view>
            <text class="price text-[36rpx] font-bold text-highlight"
              >{{ props.data.price }}万</text
            >
          </view>
          <!-- <view
            class="flex-row items-center w-[106rpx] h-[48rpx] justify-center rounded-[24rpx] bg-primary/10"
          >
            <c-icon type="plus" size="20" color="#0767ff"></c-icon>
            <text class="text-primary ml-[6rpx] text-[24rpx]">关注</text>
          </view> -->
        </view>
        <view class="flex-row">
          <text class="time text-[24rpx] text-secondary">{{ props.data.time }}</text>
          <text class="location ml-[12rpx] text-[24rpx] text-secondary">{{
            props.data.location
          }}</text>
        </view>
      </view>
      <!-- 内容 -->
      <view class="mb-[20rpx]">
        <text class="text-[28rpx] leading-[40rpx]">{{ props.data.description }}</text>
      </view>
      <!-- 图片 -->
      <view>
        <view v-if="props.data.imageList.length === 1">
          <image
            :src="props.data.imageList[0]"
            lazy-load
            fade-show
            class="max-w-[576rpx] max-h-[340rpx] rounded-[20rpx]"
            mode="aspectFill"
          ></image>
        </view>
        <view v-else class="flex-row gap-[10rpx] flex-wrap">
          <view
            v-for="(item, index) in sliceImagelist"
            :key="index"
            class="w-[186rpx] h-[160rpx] relative rounded-[20rpx] overflow-hidden"
          >
            <image :src="item" lazy-load fade-show class="w-full h-full" mode="aspectFill"> </image>
            <view
              v-if="index === props.imgMaxCount - 1"
              class="w-full h-full absolute z-10 items-center justify-center bg-black/50"
            >
              <text class="text-white">+{{ props.data.imageList.length - props.imgMaxCount }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 脚部 -->
      <view class="footer mt-[24rpx] flex-row items-center justify-between">
        <!-- <view>
          <text class="price text-[36rpx] font-bold text-highlight">{{ props.data.price }}万</text>
        </view> -->
        <!-- <view class="flex-row"> -->
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon type="chat" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">咨询</text>
        </view>
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon type="phone" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">电话</text>
        </view>
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon type="share" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">分享</text>
        </view>
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon
            v-if="props.data.isFollow"
            type="shoucangchenggong"
            size="32"
            color="#FCBD54"
          ></c-icon>
          <c-icon v-else type="shoucang4" size="32" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">收藏</text>
        </view>
        <!-- </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { ICarSource } from 'types/carSource.interface'
import { type PropType } from 'vue'
const props = defineProps({
  data: {
    type: Object as PropType<ICarSource>,
    default: () => ({}),
  },
  imgMaxCount: {
    type: Number,
    default: 3,
  },
})

const sliceImagelist = computed(() => {
  return props.data.imageList.slice(0, props.imgMaxCount)
})

const previewImage = function (url) {
  uni.previewImage({
    urls: props.data.imageList,
    indicator: 'number',
    current: url,
  })
}
</script>

<style scoped lang="scss"></style>
