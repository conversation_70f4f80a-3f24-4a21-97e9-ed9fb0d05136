<template>
  <view class="bottom-line items-start bg-white m-[30rpx] pb-[30rpx]" @tap="toDetail">
    <view class="w-full">
      <view class="flex-row">
        <view class="avatar mr-xs w-[60rpx] h-[60rpx] rounded-[30rpx] shrink-0 overflow-hidden">
          <image
            :src="$imgUrl(props.data.userAvatar || '/statics/user/default_avatar.png')"
            class="w-[100%]"
            mode="widthFix"
          ></image>
        </view>
        <!-- 用户信息 -->
        <view class="mb-[20rpx]">
          <view class="flex-row items-center justify-between">
            <view class="mb-[8rpx] flex-row items-center">
              <text class="username text-[#576B95] font-medium">{{ props.data.userName }}</text>
              <view
                v-if="props.data.buyType === 1"
                class="label ml-mn w-[80rpx] text-center p-[8rpx] rounded-[20rpx] bg-[#59C171]/10"
                ><text class="text-[24rpx] text-[#59C171]">求购</text></view
              >
              <view
                v-if="props.data.buyType === 2"
                class="label ml-mn w-[80rpx] text-center p-[8rpx] rounded-[20rpx] bg-muted/10"
                ><text class="text-[24rpx] text-muted">询价</text></view
              >
            </view>
            <!-- <view>
              <text class="price text-[36rpx] font-bold text-highlight"
                >{{ props.data.fixedPrice }}万</text
              >
            </view> -->
            <!-- <view
              class="flex-row items-center w-[106rpx] h-[48rpx] justify-center rounded-[24rpx] bg-primary/10"
            >
              <c-icon type="plus" size="20" color="#0767ff"></c-icon>
              <text class="text-primary ml-[6rpx] text-[24rpx]">关注</text>
            </view> -->
          </view>
          <view class="flex-row">
            <text class="time text-[24rpx] text-secondary">{{ props.data.createTime }}</text>
            <text class="location ml-[12rpx] text-[24rpx] text-secondary">{{
              props.data.cityName
            }}</text>
          </view>
        </view>
      </view>
      <!-- 内容 -->
      <view class="mb-[20rpx]">
        <view class="p-sm bg-page rounded-[20rpx]">
          <view class="flex-row"
            ><text
              v-for="(item, index) in props.data.seriesList"
              :key="item.seriesId"
              class="text-xl font-medium mb-mn"
              >{{ index > 0 ? '，' : '' }}{{ item.seriesName }}</text
            ></view
          >
          <view class="flex-row items-center flex-wrap">
            <text class="text-sm text-secondary">{{ props.data.yearRequestStart }}</text>
            <text class="text-sm text-secondary">~</text>
            <text class="text-sm text-secondary">{{ props.data.yearRequestEnd }}</text>
            <view class="w-[2rpx] h-[22rpx] mx-[8rpx] bg-[#9e9e9e]"></view>
            <text class="text-sm text-secondary">{{ props.data.mileageRequest }} 公里</text>
            <view class="w-[2rpx] h-[22rpx] mx-[8rpx] bg-[#9e9e9e]"></view>
            <text class="text-sm text-secondary">{{ props.data.colorRequest }}</text>
          </view>
        </view>
        <view class="overflow-hidden mb-sm" :class="['h-auto']">
          <text class="text-[28rpx] leading-[40rpx] descp">{{ props.data.content }}</text>
        </view>
        <!-- <view class="overflow-hidden" :class="[props.data.isOpen ? 'h-auto' : 'max-h-[160rpx]']">
          <text class="text-[28rpx] leading-[40rpx] descp">{{ props.data.content }}</text>
        </view>
        <view @click.stop="props.data.isOpen = !props.data.isOpen" class="w-[80rpx]">
          <text class="text-[#576B95]">{{ props.data.isOpen ? '收起' : '全文' }}</text>
        </view> -->
      </view>
      <!-- 图片 -->
      <view>
        <view v-if="props.data.carSourceImageVoList.length === 1">
          <image
            :src="props.data.carSourceImageVoList[0].vehicleImage"
            lazy-load
            fade-show
            class="max-w-[576rpx] max-h-[340rpx] rounded-[20rpx]"
            mode="aspectFill"
            @tap.stop="handlePreviewImage(props.data.carSourceImageVoList[0].vehicleImage)"
          ></image>
        </view>
        <view v-else class="flex-row gap-[10rpx] flex-wrap">
          <view
            v-for="(item, index) in sliceImagelist"
            :key="index"
            class="w-[186rpx] h-[160rpx] relative rounded-[20rpx] overflow-hidden"
          >
            <image
              :src="item.vehicleImage"
              lazy-load
              fade-show
              class="w-full h-full"
              mode="aspectFill"
              @tap.stop="handlePreviewImage(item.vehicleImage)"
            >
            </image>
            <view
              v-if="index === props.imgMaxCount - 1"
              class="w-full h-full absolute z-10 items-center justify-center bg-black/50"
            >
              <text class="text-white"
                >+{{ props.data.carSourceImageVoList.length - props.imgMaxCount }}</text
              >
            </view>
          </view>
        </view>
      </view>
      <view v-if="props.data.seriesList?.length" class="tag mt-sm flex-row flex-wrap gap-x-xs">
        <text
          class="text-sm text-[#947133]"
          v-for="item in props.data.seriesList"
          :key="item.seriesId"
          ># {{ item.seriesName }}</text
        >
      </view>
      <!-- 脚部 -->
      <view class="footer mt-[24rpx] flex-row items-center justify-between">
        <!-- <view>
          <text class="price text-[36rpx] font-bold text-highlight">{{ props.data.price }}万</text>
        </view> -->
        <!-- <view class="flex-row"> -->
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon type="chat" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">咨询</text>
        </view>
        <view
          class="flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="registerIntercept({ ok: handleMakePhoneCall }, props.data.mobile)"
        >
          <c-icon type="phone" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">电话</text>
        </view>
        <!-- #ifdef MP -->
        <button
          open-type="share"
          class="viewbutton flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="() => false"
        >
          <c-icon type="share" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">分享</text>
        </button>
        <!-- #endif -->
        <!-- #ifndef MP -->
        <view
          class="viewbotton flex flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="handleShare()"
        >
          <c-icon type="share" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">分享</text>
        </view>
        <!-- #endif -->
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon
            v-if="props.data.collectStatus"
            type="shoucangchenggong"
            size="32"
            color="#FCBD54"
          ></c-icon>
          <c-icon v-else type="shoucang4" size="32" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">收藏</text>
        </view>
        <!-- </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { execShare, getPageInfo, makePhoneCall, navigateTo, previewImage } from '@/utils/util'
import { registerIntercept } from '@/utils/auth'
import { type PropType } from 'vue'
import type { IDemandItem } from '@/api/demand'
const props = defineProps({
  data: {
    type: Object as PropType<IDemandItem>,
    default: () => ({
      carSourceImageVoList: [],
    }),
  },
  imgMaxCount: {
    type: Number,
    default: 3,
  },
})

const sliceImagelist = computed(() => {
  return props.data.carSourceImageVoList.slice(0, props.imgMaxCount)
})

const handlePreviewImage = function (url: string) {
  previewImage(
    url,
    props.data.carSourceImageVoList.map((item) => item.vehicleImage)
  )
}
const handleMakePhoneCall = function (mobile: string) {
  makePhoneCall({
    phoneNumber: mobile,
  })
}
const toDetail = function () {
  navigateTo({
    path: `/clique/info/demandDetail?id=${props.data.id}`,
  })
  uni.$once('getDemandDetailData', function (fn) {
    fn(props.data)
  })
  // registerIntercept({
  //   ok: () => {
  //     navigateTo({
  //       path: `/clique/infoDetail?id=${props.data.id}`,
  //     })
  //   },
  // })
}

// #ifdef APP
const handleShare = function (scene: 'WXSceneSession' | 'WXSceneTimeline' = 'WXSceneSession') {
  execShare({
    type: 0,
    title: '求购车型：' + props.data.seriesList.map((item) => item.seriesName).join(' '),
    scene,
    imageUrl: props.data.carSourceImageVoList[0].vehicleImage,
    miniProgram: {
      id: import.meta.env.VITE_WX_MINIPROGRAM_ID,
      path: `${getPageInfo().route}?id=${props.data.id}`,
    },
  })
}
// #endif
</script>
<!-- <style lang="scss" scoped>
.descp {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style> -->
