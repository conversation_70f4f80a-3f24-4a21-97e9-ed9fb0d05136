<template>
  <view
    class="h-[88rpx] bg-white px-[28rpx] flex-row items-center rounded-[20rpx]"
    :class="[showLine ? 'bottom-line' : '']"
    hover-class="hover"
  >
    <image v-if="icon" :src="icon" class="w-[48rpx] h-[48rpx] mr-[24rpx]" mode="widthFix"></image>
    <view class="flex-1">
      <text>{{ title }}</text>
    </view>
    <view v-if="showArrow">
      <slot name="rightIcon">
        <c-icon :type="rightIcon" size="28" color="#c1c1c1"></c-icon>
      </slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CListItem',
  components: {},
  data() {
    return {}
  },
  props: {
    title: {
      //主标题
      type: String,
      default: '',
    },
    icon: {
      //主标题
      type: String,
      default: '',
    },
    rightIcon: {
      type: String,
      default: 'jinru',
    },
    showArrow: {
      //是否显示右侧箭头
      type: Boolean,
      default: true,
    },
    showLine: {
      type: Boolean,
      default: true,
    },
  },
  created() {},
}
</script>
<style scoped>
.bottom-line {
  border-bottom: 1rpx solid #f7f7f7;
}
.hover {
  background-color: #f9f9f9;
}
</style>
