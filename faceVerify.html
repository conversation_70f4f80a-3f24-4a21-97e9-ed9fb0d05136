<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>人脸认证</title>
    <!--   引入该JS，全局注入getMetaInfo方法   -->
    <script
      type="text/javascript"
      src="https://o.alicdn.com/yd-cloudauth/cloudauth-cdn/jsvm_all.js"
    ></script>
  </head>
  <body>
    <div></div>
    <script>
      const serchToQuery = function (search) {
        const list = search.split(/\?|&/).filter((item) => item)
        const res = {}
        if (list?.length) {
          list.map((item) => {
            const [key, value] = item.split('=')
            res[key] = value
          })
        }
        return res
      }
      // 在调用实人认证服务端发起认证请求时需要传入该MetaInfo值
      let MetaInfo = window.getMetaInfo()

      // 请求认证业务接口获取CertifyUrl
      // const pageOrigin = window.location.origin
      const pageOrigin = 'http://**************:9999'
      const pageSearch = window.location.search
      const pageQuery = serchToQuery(pageSearch)
      if (pageQuery.token) {
        localStorage.setItem('TOKEN', pageQuery.token)
      }
      fetch(pageOrigin + '/app/face/verify/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          authorization: 'Bearer ' + localStorage.getItem('TOKEN'),
          'client-toc': 'Y',
        },
        body: JSON.stringify({
          cardName: pageQuery.cardName ? decodeURIComponent(pageQuery.cardName) : '',
          cardNo: pageQuery.cardNo ? decodeURIComponent(pageQuery.cardName) : '',
          metaInfo: JSON.stringify(MetaInfo),
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          console.log('data:', data)
          // 获取CertifyUrl后跳转至认证页面
          setTimeout(() => {
            window.location.href = data.data.certifyUrl
          }, 300)
        })
    </script>
  </body>
</html>
