<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>人脸认证结果</title>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    <script type="text/javascript" src="./uni.webview.1.5.6.js"></script>
  </head>
  <body>
    <script>
      // 此时的window.location.href就是ReturnUrl了。
      // 格式为：https://aliyundoc.com 或 https://aliyundoc.com/index.html
      let url = new URL(window.location.href)
      // 获取返回的认证结果数据。
      let parms = JSON.parse(url.searchParams.get('response'))
      // console.log("parms:", parms);

      // 如果需要二次验证实人认证结果可参考下面代码。
      // 获取CertifyId 为了避免盗链和篡改风险，请自行管理此ID和真实认证人的关系。
      var certifyId = parms.extInfo.certifyId
      // console.log(parms.extInfo);
      // const pageOrigin = window.location.origin
      const pageOrigin = 'http://**************:9999'
      function describe() {
        let message = ''
        let isErr = false
        switch (parms.code) {
          case 1001:
            meessage = '系统错误'
            isErr = true
            break
          case 1003:
            meessage = '验证中断'
            isErr = true
            break
          case 2002:
            meessage = '网络错误'
            isErr = true
            break
          case 2003:
            meessage = '客户端设备时间错误'
            isErr = true
            break
          case 2006:
            meessage = '认证失败'
            isErr = true
            break
          default:
            console.log(parms.code)
        }
        if (isErr) {
          uni.postMessage({
            data: { code: parms.code, message: message },
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 200)
          return
        }
        // 携带CertifyId
        fetch(pageOrigin + '/app/face/verify/describe?certifyId=' + certifyId, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            authorization: 'Bearer ' + localStorage.getItem('TOKEN'),
            'client-toc': 'Y',
          },
        })
          .then((response) => {
            return response.json()
          })
          .then((res) => {
            console.log('res', res.data)
            if (res.data) {
              uni.postMessage({
                data: {
                  ...res.data,
                  certifyId: certifyId,
                  code: parms.code,
                  message: message,
                },
              })
              setTimeout(() => {
                uni.navigateBack()
              }, 200)
            }
          })
      }
      document.addEventListener('UniAppJSBridgeReady', function () {
        describe()
      })
    </script>
  </body>
</html>
