<template>
  <view class="row flex">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'CRow',
  components: {},
  data() {
    return {}
  },
  props: {
    labelWidth: {
      //里面col子元素label的宽度
      type: String,
      default: '100rpx',
    },
  },
  created() {},
}
</script>

<style scoped lang="scss">
.row {
  align-items: flex-start;
  flex-wrap: wrap;
  // margin-bottom: 24rpx;
  // :deep(){
  //   .col {
  //     margin-bottom: 24rpx;
  //   }
  //   .col:last-child {
  //     margin-bottom: 0 !important;
  //   }
  // }
}
</style>
