<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "详情",
    "enablePullDownRefresh": false,
    "navigationStyle": "custom",
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen>
    <view class="bg w-full fixed h-[420rpx]">
      <image
        :src="$imgUrl('/statics/images/header_bg2.png')"
        class="w-full"
        mode="aspectFill"
      ></image>
    </view>
    <view class="header sticky top-0 z-20 overflow-hidden">
      <view class="w-full h-[420rpx] absolute">
        <image
          :src="$imgUrl('/statics/images/header_bg2.png')"
          class="w-full h-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="w-full justify-end relative">
        <c-titlebar showBack>
          <view class="flex-row items-center">
            <image
              :src="$imgUrl(detailData.userAvatar || '/statics/user/default_avatar.png')"
              mode="widthFix"
              class="w-[60rpx] h-[60rpx]"
            ></image>
            <view class="ml-sm">
              <view class="mb-mn">
                <text class="text-lg text-title font-medium">{{ detailData.userName }}</text>
              </view>
              <view class="flex-row">
                <text class="text-xs text-secondary">{{ detailData.createTime }}</text>
                <text class="text-xs text-secondary ml-sm">{{ detailData.cityName }}</text>
              </view>
            </view>
          </view>
          <template #rightBtn>
            <view class="flex-row items-center px-xs">
              <view
                class="flex-row items-center justify-center h-[52rpx] w-[114rpx] rounded-[26rpx] bg-primary/20"
              >
                <c-icon type="plus" size="22" color="#0767FF"></c-icon>
                <text class="text-xs text-primary">关注</text>
              </view>
              <!-- #ifdef MP -->
              <button open-type="share" class="viewbutton p-xs ml-xs">
                <c-icon type="fenxiang" color="#0767FF"></c-icon>
              </button>
              <!-- #endif -->
              <!-- #ifdef APP -->
              <view class="p-xs ml-xs" @tap="handleShare('WXSceneSession')">
                <c-icon type="fenxiang" color="#0767FF"></c-icon>
              </view>
              <!-- #endif -->
            </view>
          </template>
        </c-titlebar>
      </view>
    </view>
    <view class="container relative z-10 mt-xl px-sm">
      <view class="card mb-sm">
        <view class="title flex-row items-center gap-xs">
          <text
            v-for="item in detailData.seriesList"
            :key="item.seriesId"
            class="text-title text-xl font-medium overflow-hidden whitespace-nowrap text-ellipsis"
            >{{ item.seriesName }}</text
          >
        </view>
      </view>
      <view class="card mb-sm">
        <view class="title flex-row items-center mb-lg">
          <image
            :src="$imgUrl('/statics/icons/info.png')"
            mode="widthFix"
            class="w-[36rpx] h-[36rpx] mr-[10rpx]"
          ></image>
          <text class="text-xl font-medium">求购说明</text>
        </view>
        <view>
          <text>{{ detailData.content }}</text>
        </view>
        <view class="image-list flex-row gap-[16rpx] flex-wrap">
          <view
            v-for="(item, index) in sliceImagelist"
            :key="index"
            class="w-[206rpx] h-[206rpx] relative rounded-[20rpx] overflow-hidden"
          >
            <image
              :src="$imgUrl(item.vehicleImage)"
              lazy-load
              fade-show
              class="w-full h-full"
              mode="aspectFill"
              @tap.stop="handlePreviewImage(item.vehicleImage)"
            >
            </image>
            <view
              v-if="index === state.imgMaxCount - 1"
              class="w-full h-full absolute z-10 items-center justify-center bg-black/50"
              @tap="state.imgMaxCount = detailData.carSourceImageVoList.length + 1"
            >
              <!-- <text class="text-white">+{{ detailData.imageList.length - state.imgMaxCount }}</text> -->
              <text class="text-white">查看全部</text>
              <text class="text-white">{{ detailData.carSourceImageVoList.length }}张></text>
            </view>
          </view>
        </view>
      </view>
      <view class="bottom-bar h-[144rpx]">
        <view
          class="bg-white fixed left-0 bottom-0 w-full flex-row h-[144rpx] items-center justify-between"
        >
          <view class="left flex-row">
            <view class="p-sm items-center">
              <c-icon type="chat" size="42"></c-icon>
              <text class="mt-mn text-xs">聊价格</text>
            </view>
            <view class="p-sm items-center" @tap="registerIntercept({ ok: handleFollow })">
              <c-icon
                v-if="detailData.collectStatus"
                type="shoucangchenggong"
                size="42"
                color="#FCBD54"
              ></c-icon>
              <c-icon v-else type="shoucang4" size="42"></c-icon>
              <text class="mt-mn text-xs">收藏</text>
            </view>
          </view>
          <view class="flex-row flex-1">
            <view
              class="btn phone-btn w-full mx-base"
              @click="registerIntercept({ ok: handleMakePhoneCall })"
            >
              <text class="text-white">电话咨询</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { demandDetail, type IDemandItem } from '@/api/demand'
import { registerIntercept } from '@/utils/auth'
import { execShare, getPageInfo, makePhoneCall, message, previewImage } from '@/utils/util'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { ref } from 'vue'
const detailData = ref<IDemandItem>({
  carSourceImageVoList: [] as any,
} as IDemandItem)
const state = reactive({
  imgMaxCount: 9,
})

onLoad((options) => {
  uni.$emit('getDemandDetailData', function (data: IDemandItem) {
    detailData.value = data
  })
  if (options?.id) {
    getDetail(options.id)
  }
})

const getDetail = function (id: string) {
  demandDetail(id).then((res) => {
    if (res.code !== 0) {
      message.warning(res.msg)
      return
    }
    detailData.value = res.data
  })
}

const sliceImagelist = computed(() => {
  if (!detailData.value.carSourceImageVoList) {
    return []
  }
  return detailData.value.carSourceImageVoList.slice(0, state.imgMaxCount)
})

const handlePreviewImage = function (url) {
  previewImage(
    url,
    detailData.value.carSourceImageVoList.map((item) => item.vehicleImage)
  )
}

const handleFollow = function () {
  if (detailData.value.collectStatus === 1) {
    detailData.value.collectStatus = 0
  } else {
    detailData.value.collectStatus = 1
  }
  if (detailData.value.collectStatus === 1) {
    message.warning('收藏成功')
  } else {
    message.warning('取消收藏成功')
  }
}

const handleMakePhoneCall = function () {
  makePhoneCall({
    phoneNumber: detailData.value.mobile,
    fail(err) {
      console.log(err)
    },
    success(e) {
      console.log(1111)
      console.log(e)
    },
  })
}
// #ifdef APP
const handleShare = function (scene: 'WXSceneSession' | 'WXSceneTimeline' = 'WXSceneSession') {
  execShare({
    type: 0,
    title: '求购车型：' + detailData.value.seriesList.map((item) => item.seriesName).join(' '),
    scene,
    imageUrl: detailData.value.carSourceImageVoList[0].vehicleImage,
    miniProgram: {
      id: import.meta.env.VITE_WX_MINIPROGRAM_ID,
      path: `${getPageInfo().route}?id=${detailData.value.id}`,
    },
  })
}
// #endif
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  // console.log(getPageInfo())
  return {
    title: '求购车型：' + detailData.value.seriesList.map((item) => item.seriesName).join(' '),
    path: `${getPageInfo().route}?id=${detailData.value.id}`,
    imageUrl: detailData.value.carSourceImageVoList[0].vehicleImage,
  }
})
onShareTimeline(() => {
  // console.log(getPageInfo())
  return {
    title: '求购车型：' + detailData.value.seriesList.map((item) => item.seriesName).join(' '),
    path: `${getPageInfo().route}?id=${detailData.value.id}`,
    imageUrl: detailData.value.carSourceImageVoList[0].vehicleImage,
  }
})
// #endif
</script>

<style scoped lang="scss">
.card {
  border-radius: 20rpx;
  padding: 30rpx;
  background-color: #ffffff;
}
.info-list {
  .item {
    flex: 1;
    flex-direction: row;
    min-width: 40%;
    flex: 1;
  }
}

.bottom-bar {
  .btn {
    justify-content: center;
    align-items: center;
    // width: 205rpx;
    height: 84rpx;
    border-radius: 16rpx;
  }
  .phone-btn {
    background: linear-gradient(270deg, #fe4306 0%, #fe975b 100%);
  }
  .dingjin-btn {
    background: linear-gradient(270deg, #1262ff 0%, #0a89ff 100%);
  }
}
</style>
