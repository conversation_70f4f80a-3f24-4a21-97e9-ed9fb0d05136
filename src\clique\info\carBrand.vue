<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择品牌",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fixedHeight>
    <c-list-index :dataList="state.brandList" ref="listIndexRef">
      <template #defalut="{ data }">
        <view
          class="flex-row items-center py-[24rpx] px-xs bottom-line"
          v-for="(item, index) in data"
          :key="index"
          @click="toSeries(item)"
        >
          <!-- <image
            :src="item.brandLogo"
            mode="widthFix"
            lazy-load
            class="mr-mn rounded-[12rpx] w-[80rpx] h-[80rpx]"
          ></image> -->
          <text :class="['text-xl', state.currentBrand === item.brandId && 'text-primary']">{{
            item.brandName
          }}</text>
        </view>
      </template>
    </c-list-index>
  </c-page>
</template>

<script lang="ts">
export default defineComponent({
  name: 'ChooseLocation',
})
</script>
<script setup lang="ts">
import { carBrandList } from '@/api/carResource'
import { message, navigateTo } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { defineComponent, ref } from 'vue'

const state = reactive({
  brandList: [] as any[],
  currentBrand: '',
})
let level = ''
onLoad((options) => {
  if (options?.level) {
    level = options.level
  }
})
const listIndexRef = ref()
const getList = function () {
  message.loading('加载中...')
  carBrandList().then((res) => {
    if (res.data) {
      // state.brandList = res.data
      let index = 0
      for (let key in res.data) {
        state.brandList.push({
          letter: key,
          data: res.data[key],
        })
        index++
        // if (key === 'C') {
        //   break
        // }
      }
      nextTick(() => {
        listIndexRef.value.computeNodeInfo()
        message.hideLoading()
      })
    } else {
      message.warning(res.msg)
    }
  })
}
getList()

const toSeries = function (e) {
  state.currentBrand = e.brandId
  navigateTo(`/clique/info/carSeries?brandId=${e.brandId}&brandName=${e.brandName}&level=${level}`)
}
</script>

<style scoped lang="scss">
.cell {
  .placeholder {
    color: #999999;
  }
}
</style>
