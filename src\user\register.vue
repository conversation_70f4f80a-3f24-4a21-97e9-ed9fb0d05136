<route lang="json">
{
  "style": {
    "navigationBarTitleText": "会员注册"
  },
  "auth": false
}
</route>
<template>
  <!-- <view class="bg-page min-h-full flex flex-col items-center box-border"> -->
  <c-page>
    <view class="w-full">
      <view class="title">
        <view class="text-4xl text-title font-medium mb-[20rpx]"> 完成身份认证 </view>
        <view class="tip text-sm flex-row items-center">
          <image class="w-[42rpx] h-[42rpx]" :src="$imgUrl('/statics/icons/auth.png')"></image>
          <text>依照法律要求，的信息将被加密保护，请放心填写</text>
        </view>
      </view>
      <view class="bg-white px-[32rpx] rounded-xl mx-[16rpx]">
        <c-form label-width="160rpx">
          <c-form-item label="头像">
            <!-- #ifdef MP-WEIXIN -->
            <view class="flex-row items-center justify-between">
              <image
                v-if="formData.avatar"
                :src="$imgUrl(formData.avatar)"
                mode="widthFix"
                class="w-[60rpx] rounded-sm mr-[12rpx]"
              ></image>
              <button
                class="button second flex-1 text-left py-[24rpx] px-0"
                open-type="chooseAvatar"
                @chooseavatar="onChooseAvatar"
              >
                点击获取头像
              </button>
            </view>
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <view class="button second flex-1 text-left py-[24rpx] px-0">点击获取</view>
            <!-- #endif -->
          </c-form-item>
          <c-form-item label="昵称">
            <!-- #ifdef MP-WEIXIN -->
            <c-input
              type="nickname"
              v-model="formData.nickname"
              placeholder="点击获取昵称"
            ></c-input>
            <!-- #endif -->
            <!-- #ifndef MP-WEIXIN -->
            <c-input type="text" v-model="formData.nickname" placeholder="请输入昵称"></c-input>
            <!-- #endif -->
          </c-form-item>
          <c-form-item label="微信号">
            <c-input
              class="flex-1"
              v-model="formData.wxNumber"
              :border="false"
              placeholder="请输入"
            />
          </c-form-item>
          <c-form-item label="真实姓名">
            <c-input
              class="flex-1"
              v-model="formData.name"
              :border="false"
              placeholder="请输入"
              @blur="validatePhone"
            />
          </c-form-item>
          <c-form-item label="手机号" v-if="!userInfo.phone">
            <view class="flex-row">
              <c-input
                class="flex-1"
                v-model="formData.phone"
                :border="false"
                placeholder="请输入手机号码"
                @blur="validatePhone"
              />
              <!-- #ifdef MP-WEIXIN -->
              <view
                class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 pl-sm w-[180rpx]"
              >
                <button
                  class="mini-btn"
                  open-type="getPhoneNumber"
                  @getphonenumber="onGetWxPhoneNumber"
                >
                  获取手机号
                </button>
                <!-- <view class="mini-btn">获取手机号</view> -->
              </view>
              <!-- #endif -->
            </view>
          </c-form-item>
          <c-form-item label="验证码" v-if="!userInfo.phone">
            <view class="flex-row">
              <c-input
                class="flex-1"
                v-model="formData.mobileCode"
                placeholder="请输入验证码"
                :border="false"
              />
              <view
                class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 pl-sm w-[180rpx]"
                @click="sendSms"
              >
                <view class="mini-btn">获取验证码</view>
              </view>
            </view>
          </c-form-item>
          <c-form-item label="车行定位">
            <c-chooselocation
              @confirm="onSelectLocation"
              :address="formData.carDealershipAddress"
            ></c-chooselocation>
          </c-form-item>
          <c-form-item label="车行名称">
            <c-input v-model="formData.carDealershipName" placeholder="请输入"></c-input>
          </c-form-item>
          <c-form-item label="归属地">
            <!-- <c-input v-model="formData.region" placeholder="请输入"></c-input> -->
            <!-- <view @click="onSelectArea">请选择</view> -->
            <c-selectarea @confirm="onSelectArea"></c-selectarea>
          </c-form-item>
          <c-form-item label="详细地址">
            <c-input v-model="formData.detailAddress" placeholder="请输入"></c-input>
          </c-form-item>
          <!-- <c-form-item label="新密码">
            <c-input
              class="flex-1"
              type="password"
              v-model="formData.password"
              placeholder="6-20位数字+字母或符号组合"
              :border="false"
              @input="checkPasswordStrength"
              @focus="passwordFocused = true"
              @blur="passwordFocused = false"
            />
          </c-form-item>
          <view v-if="passwordFocused" class="mt-2 password-strength">
            <view class="strength-bar">
              <view
                :class="['strength-level', strengthClass]"
                :style="{ width: strengthWidth }"
              ></view>
            </view>
            <text class="strength-text" :class="strengthClass">{{ strengthText }}</text>
          </view>
          <c-form-item label="确认密码">
            <c-input
              class="flex-1"
              type="password"
              v-model="formData.value.password2"
              placeholder="再次输入新密码"
              :border="false"
            />
          </c-form-item> -->
        </c-form>
      </view>
      <view class="bg-white mt-[32rpx] p-[32rpx] rounded-xl mx-[16rpx]">
        <view>
          <view class="text-xl text-title font-medium mb-[30rpx]"
            >上传本人身份证正反面(支持一次上传正反面)</view
          >
          <view class="flex-row justify-between">
            <c-upload @change="onUploadIdcard1" :showList="false">
              <view class="items-center">
                <image
                  :src="$imgUrl(formData.idCardCorrect || '/statics/user/idcard1.png')"
                  class="w-[300rpx] h-[147rpx]"
                  mode="aspectFill"
                ></image>
                <view class="mt-[20rpx] text-sm text-tip">
                  <text>上传人像面</text>
                </view>
              </view>
            </c-upload>
            <c-upload @change="onUploadIdcard2" :showList="false">
              <view class="items-center">
                <image
                  :src="$imgUrl(formData.idCardImageReverse || '/statics/user/idcard2.png')"
                  class="w-[300rpx] h-[147rpx]"
                  mode="aspectFill"
                ></image>
                <view class="mt-[20rpx] text-sm text-tip">
                  <text>上传国徽面</text>
                </view>
              </view>
            </c-upload>
          </view>
        </view>
        <view class="mt-[60rpx]">
          <view class="text-xl text-title font-medium mb-[20rpx]">所在车行营业执照(选填)</view>
          <view class="flex-row justify-content">
            <c-upload @change="onUploadBusinessLicense" :showList="false">
              <view class="p-[10rpx] rounded-lg border-dashed border-muted w-[120rpx] h-[110rpx]">
                <image
                  :src="$imgUrl(formData.businessLicense || '/statics/user/camera.png')"
                  class="w-full"
                  mode="aspectFill"
                ></image>
              </view>
            </c-upload>
          </view>
        </view>
      </view>
      <view class="mt-[20rpx] bottom-btn">
        <c-button type="primary" block size="large" @click="handleConfirm(true)"> 提交 </c-button>
      </view>
    </view>
  </c-page>
  <!-- </view> -->
</template>

<script setup lang="ts">
import { smsSend } from '@/api/app'
import { cardAliOcr, cardOcr, getVerifyInfo, registerVip, type IRegisterVipForm } from '@/api/user'
// #ifdef APP
import { useFaceVerify } from '@/hooks/faceVerify'
// #endif
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
// import request from '@/utils/request'
import { message, navigateTo } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { ref, shallowRef } from 'vue'

const { userInfo } = toRefs(useUserStore())
const { getUser } = useUserStore()

const uCodeRef = shallowRef()
const formData = ref<IRegisterVipForm>({
  userId: '',
  name: '',
  wxNumber: '',
  avatar: '',
  nickname: '',
  phone: '',
  mobileCode: '',
  password: '',
  password2: '',
  carDealershipAddress: '',
  carDealershipLat: '',
  carDealershipLng: '',
  carDealershipName: '',
  region: '',
  provinceCode: '',
  cityCode: '',
  districtCode: '',
  detailAddress: '',
  idCard: '',
  idCardCorrect: '',
  idCardImageReverse: '',
  businessLicense: '',
  faceVerify: false,
  memberPay: false,
})

onLoad(async () => {
  if (!userInfo.value.userId) {
    try {
      await getUser()
    } catch (error) {
      console.log(error)
    }
  }
  getVerifyInfo().then((res) => {
    console.log(res)
    if (res.code === 0) {
      formData.value = res.data
    }
  })
  // if (userInfo.value.userId) {
  //   formData.value.userId = userInfo.value.userId
  //   getVerifyInfo(userInfo.value.userId).then((res) => {
  //     console.log(res)
  //     if (res.code === 0) {
  //       formData.value = res.data
  //     }
  //   })
  // }
})

const passwordLevel = ref(0)

const onSelectLocation = function (res: UniApp.ChooseLocationSuccess) {
  formData.value.carDealershipAddress = res.address
  formData.value.carDealershipLat = res.latitude + ''
  formData.value.carDealershipLng = res.longitude + ''
}
const checkPasswordStrength = () => {
  const password = formData.value.password
  let strength = 0

  if (password.length >= 8) strength++
  if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  passwordLevel.value = strength
}

// 添加手机号验证函数
const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

const validatePhone = () => {
  if (formData.value.phone && !isValidPhone(formData.value.phone)) {
    message.warning('请输入正确的手机号码')
  }
}

const sendSms = async () => {
  if (!formData.value.phone) return message.warning('请输入手机号码')
  if (!isValidPhone(formData.value.phone)) return message.warning('请输入正确的手机号码')
  if (uCodeRef.value?.canGetCode) {
    await smsSend(formData.value.phone)
    message.warning('发送成功')
    uCodeRef.value?.start()
  }
}
// #ifdef APP
const { checkVerifyRes } = useFaceVerify()
// #endif
const handleConfirm = async (verify: boolean = true) => {
  if (!formData.value.faceVerify) {
    // #ifdef APP
    // try {
    //   const res = await checkVerifyRes()
    //   console.log(res)
    //   message.success('实人认证成功！')
    // } catch (error: any) {
    //   console.log(error)
    //   message.error(error.message || '人脸认证失败')
    //   return
    // }
    //#endif
    // #ifdef MP || H5
    if (verify) {
      uni.$once('certifyRes', async (res) => {
        if (res.success) {
          setTimeout(() => {
            message.success('实人认证成功')
          }, 300)
          setTimeout(() => {
            handleConfirm(false)
          }, 2000)
        } else {
          setTimeout(() => {
            message.error(res.message || '人脸认证失败')
          }, 300)
        }
      })
      navigateTo(
        `/user/faceVerify?url=${encodeURIComponent(
          `${
            import.meta.env.VITE_H5FACEVERIFY_BASE_URL
          }/faceVerify.html?token=${getToken()}&cardName=${formData.value.name}&cardNo=${
            formData.value.idCard
          }`
        )}`
      )
      return
    }
    // #endif
  }
  const { code, msg } = await registerVip({ ...formData.value, userId: userInfo.value.userId })

  if (code === 1) {
    message.warning(msg)
  } else {
    if (!formData.value.memberPay) {
      message.warning('提交成功，请购买会员套餐！')
      setTimeout(() => {
        navigateTo('/user/selectVipPackage')
      }, 2000)
    } else {
      message.success('操作成功')
      setTimeout(() => {
        uni.navigateBack()
      }, 2000)
    }
  }
}

const passwordFocused = ref(false)

const onChooseAvatar = function (e) {
  // console.log(e)
  const { avatarUrl } = e.detail
  // console.log(avatarUrl)
  formData.value.avatar = avatarUrl
}

const onGetWxPhoneNumber = function (e) {
  console.log(e)
  const { code } = e.detail
  console.log(code)
}

const onUploadIdcard1 = function (e) {
  console.log(e)
  if (e.type === 'choose') {
    formData.value.idCardCorrect = e.data[0]
    // 转成ocr识别的base64
  }
  if (e.type === 'upload') {
    message.success('上传成功')
    formData.value.idCardCorrect = e.data
    message.loading('正在识别身份证')
    // cardOcr({ imageResource: e.data })
    cardAliOcr({ url: e.data })
      .then((res) => {
        // console.log(res)
        if (res.code === 0) {
          message.success('识别完成')
          formData.value.idCard = res.data.id_card
          formData.value.name = res.data.name
          formData.value.detailAddress = res.data.address
        } else {
          message.warning('识别失败')
        }
      })
      .finally(() => {
        message.hideLoading()
      })
  }
}
const onUploadIdcard2 = function (e) {
  // console.log(e)
  if (e.type === 'choose') {
    formData.value.idCardImageReverse = e.data[0]
  }
  if (e.type === 'upload') {
    formData.value.idCardImageReverse = e.data
  }
}
const onUploadBusinessLicense = function (e) {
  // console.log(e)
  if (e.type === 'choose') {
    formData.value.businessLicense = e.data[0]
  }
  if (e.type === 'upload') {
    formData.value.businessLicense = e.data
  }
}

const onSelectArea = function (e) {
  // console.log(e)
  formData.value.provinceCode = e.provinceCode
  formData.value.cityCode = e.cityCode
  formData.value.region = e.cityCode
  formData.value.districtCode = e.areaCode
}
</script>

<style lang="scss" scoped>
page {
  height: 100%;
}

.password-strength {
  margin-top: 8rpx;
  transition: opacity 0.3s ease;

  .strength-bar {
    height: 4rpx;
    background-color: #e0e0e0;
    margin-bottom: 4rpx;
  }

  .strength-level {
    height: 100%;
    transition: width 0.3s ease;
  }

  .strength-text {
    font-size: 24rpx;
  }

  .strength-weak {
    background-color: #ff4d4f;
    color: #ff4d4f;
  }

  .strength-medium {
    background-color: #faad14;
    color: #faad14;
  }

  .strength-strong {
    background-color: #52c41a;
    color: #52c41a;
  }
}

.button {
  &.second {
    // padding: 0;
    line-height: 1.2;
    font-size: 30rpx;
    color: #999999;
    border: none;
    outline: none;
    outline-style: none;
    background-color: #ffffff;
    color: #999999;
    &:after {
      border: none;
    }
  }
}

.mini-btn {
  padding: 10rpx 0;
  border: none;
  outline: none;
  line-height: 1.2;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #1a1a1a;
  margin: 0;
  &:after {
    border: none;
  }
}

.title {
  padding: 28rpx;
  .tip {
    color: #5f6678;
  }
}
.bottom-btn {
  position: sticky;
  bottom: 0;
  padding: 20rpx 24rpx;
  background-color: #ffffff;
}
</style>
