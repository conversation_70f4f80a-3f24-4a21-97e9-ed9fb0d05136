# 🔧 小程序域名配置说明

## 📋 概述

如果车城app需要发布为微信小程序，需要在微信公众平台配置以下域名白名单。

## 🌐 必需配置的域名

### 1. **socket合法域名** (WebSocket)

```
wss://wss.im.tencent.cn
wss://wssv6.im.qcloud.com
wss://${SDKAppID}w4c.my-imcloud.com
wss://wss.tim.qq.com
wss://wss.im.qcloud.com
```

**说明**:
- 用于IM实时通信连接
- 必须配置，否则无法建立IM连接
- `${SDKAppID}` 需要替换为实际的应用ID

### 2. **request合法域名** (HTTPS)

```
https://web.sdk.qcloud.com
https://boce-cdn.my-imcloud.com
https://api.im.qcloud.com
https://events.im.qcloud.com
https://webim.tim.qq.com
https://wss.im.qcloud.com
https://wssv6.im.qcloud.com
https://wss.tim.qq.com
https://web.sdk.cloud.tencent.cn
https://web.sdk.tencent.cn
```

**说明**:
- 用于API请求和资源加载
- 包含IM服务的各种接口域名

### 3. **uploadFile合法域名**

```
https://cos.ap-shanghai.myqcloud.com
https://cos.ap-beijing.myqcloud.com
https://cos.ap-guangzhou.myqcloud.com
```

**说明**:
- 用于文件上传功能
- 根据实际使用的COS地域配置

## ⚙️ 配置步骤

### 1. **登录微信公众平台**
访问：https://mp.weixin.qq.com/

### 2. **进入开发管理**
开发 > 开发管理 > 开发设置 > 服务器域名

### 3. **配置域名**
分别在对应的域名类型中添加上述域名列表

### 4. **保存配置**
配置完成后保存，等待生效（通常几分钟内生效）

## 🚨 注意事项

### ⚠️ **重要提醒**
1. **域名必须是HTTPS**: 所有域名必须支持HTTPS协议
2. **不支持IP地址**: 不能配置IP地址，只能配置域名
3. **不支持端口号**: 域名不能包含端口号
4. **通配符限制**: 不支持通配符域名

### 💡 **最佳实践**
1. **提前配置**: 在开发阶段就配置好所有域名
2. **测试验证**: 配置后在小程序开发工具中测试
3. **定期检查**: 定期检查域名是否有变更
4. **备用域名**: 配置多个备用域名以提高可用性

## 🔧 代码适配

### 小程序环境检测
```javascript
// 检测是否为小程序环境
const isMiniProgram = () => {
  // #ifdef MP-WEIXIN
  return true
  // #endif
  return false
}

// 根据环境选择不同的配置
const getIMConfig = () => {
  if (isMiniProgram()) {
    return {
      // 小程序专用配置
      SDKAppID: 1400000000,
      // 其他小程序特定配置
    }
  } else {
    return {
      // H5/APP配置
      SDKAppID: 1400000000,
      // 其他配置
    }
  }
}
```

### 条件编译
```javascript
// 小程序特定的IM初始化
// #ifdef MP-WEIXIN
tim = TIM.create({
  SDKAppID: config.SDKAppID,
  // 小程序特定选项
})
// #endif

// #ifdef H5
tim = TIM.create({
  SDKAppID: config.SDKAppID,
  // H5特定选项
})
// #endif
```

## 📱 测试验证

### 1. **开发工具测试**
在微信开发者工具中测试IM功能是否正常

### 2. **真机测试**
在真实设备上测试小程序的IM功能

### 3. **网络检查**
检查网络请求是否被域名限制阻止

## 🔄 更新维护

### 定期检查
- 腾讯云IM域名是否有更新
- 新增的功能是否需要新的域名
- 域名证书是否即将过期

### 版本兼容
- 不同版本的IM SDK可能使用不同的域名
- 升级SDK时需要检查域名配置

---

**📝 配置完成后，车城app就可以在小程序环境中正常使用IM功能了！**
