<route lang="json">
{
  "style": {
    "navigationBarTitleText": "会员注册"
  },
  "auth": false
}
</route>
<template>
  <view class="register bg-page min-h-full items-center box-border pt-[24rpx]">
    <view class="w-full flex-1 bg-white">
      <view class="px-[32rpx] mx-[16rpx]">
        <c-form label-width="180rpx">
          <c-form-item label="手机号">
            <view class="flex-row">
              <c-input
                class="flex-1"
                v-model="formData.phone"
                :border="false"
                placeholder="请输入手机号码"
                @blur="validatePhone"
              />
              <!-- #ifdef MP-WEIXIN -->
              <view
                class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[200rpx]"
              >
                <button
                  class="mini-btn"
                  open-type="getPhoneNumber"
                  @getphonenumber="onGetWxPhoneNumber"
                >
                  获取手机号
                </button>
                <!-- <view class="mini-btn">获取手机号</view> -->
              </view>
              <!-- #endif -->
            </view>
          </c-form-item>
          <c-form-item label="验证码">
            <view class="flex-row">
              <c-input
                class="flex-1"
                v-model="formData.mobileCode"
                placeholder="请输入验证码"
                :border="false"
              />
              <view
                class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[200rpx]"
                @click="sendSms"
              >
                <view class="mini-btn">获取验证码</view>
              </view>
            </view>
          </c-form-item>
          <c-form-item label="新密码">
            <c-input
              class="flex-1"
              type="password"
              v-model="formData.password"
              placeholder="6-20位数字+字母或符号组合"
              :border="false"
              @input="checkPasswordStrength"
              @focus="passwordFocused = true"
              @blur="passwordFocused = false"
            />
          </c-form-item>
          <c-form-item label="确认密码">
            <c-input
              class="flex-1"
              type="password"
              v-model="formData.password2"
              placeholder="再次输入新密码"
              :border="false"
            />
          </c-form-item>
        </c-form>
        <view class="flex-row text-sm items-center py-[24rpx]">
          <radio
            class="radio"
            @click="state.agreementChecked = !state.agreementChecked"
            color="#ec702d"
            :checked="state.agreementChecked"
          />
          <view
            class="flex-row text-sm items-center"
            @click="state.agreementChecked = !state.agreementChecked"
          >
            <text>已阅读并同意</text>
            <view @click.stop>
              <navigator
                class="text-primary"
                hover-class="none"
                url="/pages/agreement/agreement?type=service"
              >
                <text>《服务协议》</text>
              </navigator>
            </view>
            <text>和</text>
            <view @click.stop>
              <navigator
                class="text-primary"
                hover-class="none"
                url="/pages/agreement/agreement?type=privacy"
              >
                <text>《隐私协议》</text>
              </navigator>
            </view>
          </view>
        </view>
      </view>
      <view class="w-full mt-[24rpx] bottom-btn">
        <c-button type="primary" block size="large" @click="handleConfirm"> 立即注册 </c-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { register } from '@/api/account'
import { smsSend } from '@/api/app'
import { message } from '@/utils/util'
import { reactive, ref, shallowRef } from 'vue'

const uCodeRef = shallowRef()
const codeTips = ref('')
const formData = reactive({
  phone: '',
  mobileCode: '',
  password: '',
  password2: '',
})

const state = reactive({
  agreementChecked: false,
  passwordLevel: 0,
})

const checkPasswordStrength = () => {
  const password = formData.password
  let strength = 0

  if (password.length >= 8) strength++
  if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  state.passwordLevel = strength
}

// 添加手机号验证函数
const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

const codeChange = (text: string) => {
  codeTips.value = text
}

const validatePhone = () => {
  if (formData.phone && !isValidPhone(formData.phone)) {
    message.warning('请输入正确的手机号码')
  }
}

const sendSms = async () => {
  if (!formData.phone) return message.warning('请输入手机号码')
  if (!isValidPhone(formData.phone)) return message.warning('请输入正确的手机号码')
  if (uCodeRef.value?.canGetCode) {
    await smsSend(formData.phone)
    message.warning('发送成功')
    uCodeRef.value?.start()
  }
}

const handleConfirm = async () => {
  if (!state.agreementChecked) {
    message.warning('请先同意隐私政策和用户服务协议')
    return
  }
  if (!formData.phone) return message.warning('请输入手机号码')
  if (!isValidPhone(formData.phone)) return message.warning('请输入正确的手机号码')
  if (!formData.mobileCode) return message.warning('请输入短信验证码')
  if (!formData.password) return message.warning('请输入密码')
  if (state.passwordLevel < 2) return message.warning('密码强度不够，请设置更复杂的密码')
  if (!formData.password2) return message.warning('请输入确认密码')
  if (formData.password != formData.password2) return message.warning('两次输入的密码不一致')
  const { code, msg } = await register(formData)

  if (code === 1) {
    message.warning(msg)
  } else {
    message.success('操作成功')
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  }
}

const passwordFocused = ref(false)

const onGetWxPhoneNumber = function (e) {
  console.log(e)
  const { code } = e.detail
  console.log(code)
}
</script>

<style lang="scss" scoped>
.button {
  &.second {
    // padding: 0;
    line-height: 1.2;
    font-size: 30rpx;
    color: #999999;
    border: none;
    outline: none;
    outline-style: none;
    background-color: #ffffff;
    color: #999999;
    &:after {
      border: none;
    }
  }
}

.radio {
  transform: scale(0.7);
}

.mini-btn {
  padding: 10rpx 0;
  border: none;
  outline: none;
  line-height: 1.2;
  border-radius: 10rpx;
  font-size: 26rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #1a1a1a;
  &:after {
    border: none;
  }
}

.bottom-btn {
  position: sticky;
  bottom: 0;
  padding: 20rpx 24rpx;
  background-color: #ffffff;
}
</style>
