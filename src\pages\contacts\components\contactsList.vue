<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <!-- 我的关注标题和筛选 -->
    <view class="flex-row items-center justify-between mb-[24rpx]">
      <view class="flex-row h-[56rpx] items-center justify-center">
        <text class="ml-[10rpx] text-[30rpx] text-title">
          我的关注({{ state.dataList.length }})
        </text>
      </view>
      <view class="flex-row h-[66rpx] items-center p-[6rpx] rounded-[24rpx] bg-[#F4F6FA]">
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'all' ? 'bg-white' : '']"
          @click="switchStatus('all')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'all' ? 'bg-white text-title' : 'text-muted']"
            >全部</text
          >
        </view>
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'friend' ? 'bg-white' : '']"
          @click="switchStatus('friend')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'friend' ? 'bg-white text-title' : 'text-muted']"
            >好友</text
          >
        </view>
      </view>
    </view>

    <!-- 联系人列表 -->
    <contactItem v-for="item in state.dataList" :key="item.id" :data="item"></contactItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from 'vue'
import contactItem from './contactItem.vue'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'

import { IM_CONFIG } from '@/config/im'

interface IContact {
  id: string
  avatar: string
  username: string
  remark?: string
  isFriend: boolean
}

type Status = 'all' | 'friend'

const state = reactive({
  loading: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all' as Status,
  },
  dataList: [] as IContact[],
})

// 使用真实IM用户数据作为联系人
const mockData: IContact[] =
  IM_CONFIG.MOCK_FRIEND_REQUESTS?.map((user, index) => ({
    id: user.userID,
    avatar: user.avatar,
    username: user.nickname,
    isFriend: index === 0 ? false : true, // 管理员设为非好友，其他设为好友
  })) || []

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((resolve) => {
    setTimeout(() => {
      let _dataList: IContact[] = cloneDeep(mockData)

      // 根据状态筛选数据
      if (state.queryForm.status === 'friend') {
        _dataList = _dataList.filter((item) => item.isFriend)
      }

      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      resolve(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}

const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

const switchStatus = function (status: Status) {
  state.queryForm.status = status
  initData()
}

// 检查IM状态
const checkIMStatus = () => {
  // 不再自动初始化IM，避免与自动登录冲突
  console.log('通讯录页面已加载，IM状态由自动登录管理')
}

// 组件挂载时检查状态
onMounted(() => {
  checkIMStatus()
  getData() // 初始化数据
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理监听器等
})

// 初始化数据
initData()

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss"></style>
