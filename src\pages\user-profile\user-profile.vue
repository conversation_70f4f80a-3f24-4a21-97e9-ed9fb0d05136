<route lang="json">
{
  "style": {
    "navigationBarTitleText": "用户资料",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="user-profile-container">
      <!-- 用户头像和基本信息 -->
      <view class="user-header">
        <view class="avatar-section">
          <image :src="userInfo.avatar" class="user-avatar" mode="aspectFill" />
        </view>
        <view class="user-info">
          <text class="username">{{ userInfo.username }}</text>
          <text class="user-id">ID: {{ userInfo.userID }}</text>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <view class="action-item" @click="sendMessage">
          <view class="action-icon">
            <text class="icon-text">💬</text>
          </view>
          <text class="action-text">发消息</text>
        </view>

        <view class="action-item" @click="makeCall">
          <view class="action-icon">
            <text class="icon-text">📞</text>
          </view>
          <text class="action-text">语音通话</text>
        </view>

        <view class="action-item" @click="videoCall">
          <view class="action-icon">
            <text class="icon-text">📹</text>
          </view>
          <text class="action-text">视频通话</text>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="detail-section">
        <view class="section-title">详细信息</view>

        <view class="detail-item">
          <text class="detail-label">昵称</text>
          <text class="detail-value">{{ userInfo.username }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">用户ID</text>
          <text class="detail-value">{{ userInfo.userID }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">在线状态</text>
          <text class="detail-value online">在线</text>
        </view>
      </view>

      <!-- 更多操作 -->
      <view class="more-section">
        <view class="section-title">更多操作</view>

        <view class="more-item" @click="clearChatHistory">
          <text class="more-text">清空聊天记录</text>
          <text class="more-arrow">›</text>
        </view>

        <view class="more-item" @click="blockUser">
          <text class="more-text danger">拉黑用户</text>
          <text class="more-arrow">›</text>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 用户信息
const userInfo = reactive({
  userID: '',
  username: '',
  avatar: '/static/images/user/default_avatar.png',
})

// 页面加载
onLoad((options) => {
  console.log('用户资料页面参数:', options)

  if (options) {
    userInfo.userID = options.userID || ''
    userInfo.username = decodeURIComponent(options.username || '用户')
    userInfo.avatar = decodeURIComponent(options.avatar || '/static/images/user/default_avatar.png')
  }

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: userInfo.username,
  })
})

// 发送消息
const sendMessage = () => {
  // 返回到聊天页面
  uni.navigateBack()
}

// 语音通话
const makeCall = () => {
  uni.showToast({
    title: '语音通话功能开发中',
    icon: 'none',
  })
}

// 视频通话
const videoCall = () => {
  uni.showToast({
    title: '视频通话功能开发中',
    icon: 'none',
  })
}

// 清空聊天记录
const clearChatHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空与该用户的聊天记录吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 实现清空聊天记录功能
        uni.showToast({
          title: '聊天记录已清空',
          icon: 'success',
        })
      }
    },
  })
}

// 拉黑用户
const blockUser = () => {
  uni.showModal({
    title: '确认拉黑',
    content: '确定要拉黑该用户吗？拉黑后将无法收到对方消息。',
    success: (res) => {
      if (res.confirm) {
        // TODO: 实现拉黑用户功能
        uni.showToast({
          title: '用户已拉黑',
          icon: 'success',
        })
      }
    },
  })
}
</script>

<style scoped lang="scss">
.user-profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-header {
  background-color: white;
  padding: 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar-section {
  margin-right: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 2rpx solid #e0e0e0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-id {
  font-size: 28rpx;
  color: #666;
}

.action-section {
  background-color: white;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #1989fa;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 40rpx;
  color: white;
}

.action-text {
  font-size: 24rpx;
  color: #333;
}

.detail-section,
.more-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx 40rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item {
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 32rpx;
  color: #333;
}

.detail-value {
  font-size: 32rpx;
  color: #666;
}

.detail-value.online {
  color: #52c41a;
}

.more-item {
  padding: 30rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.more-item:last-child {
  border-bottom: none;
}

.more-text {
  font-size: 32rpx;
  color: #333;
}

.more-text.danger {
  color: #ff4d4f;
}

.more-arrow {
  font-size: 32rpx;
  color: #c7c7cc;
  font-weight: 300;
  transform: scaleX(0.8);
}
</style>
