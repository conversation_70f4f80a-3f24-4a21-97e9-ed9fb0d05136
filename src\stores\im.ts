/**
 * IM状态管理Store
 */

import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import imManager, { type Message, type Conversation } from '@/utils/im/index'
import { IMStatus, imStatusManager } from '@/utils/im/init'

export const useIMStore = defineStore('im', () => {
  // IM状态
  const status = ref<IMStatus>(IMStatus.NOT_INITIALIZED)
  const isConnected = ref(false)
  const currentUserId = ref('')

  // 会话列表
  const conversationList = ref<Conversation[]>([])
  const totalUnreadCount = ref(0)

  // 当前聊天
  const currentConversationId = ref('')
  const currentMessages = ref<Message[]>([])

  // 用户信息缓存
  const userInfoCache = reactive<Record<string, any>>({})
  const groupInfoCache = reactive<Record<string, any>>({})

  /**
   * 设置IM状态
   */
  const setStatus = (newStatus: IMStatus) => {
    status.value = newStatus
    isConnected.value = newStatus === IMStatus.LOGGED_IN
  }

  /**
   * 设置当前用户ID
   */
  const setCurrentUserId = (userId: string) => {
    currentUserId.value = userId
  }

  /**
   * 更新会话列表
   */
  const updateConversationList = (conversations: Conversation[]) => {
    conversationList.value = conversations

    // 计算总未读数
    totalUnreadCount.value = conversations.reduce((total, conv) => total + conv.unreadCount, 0)

    // 更新tabbar角标
    updateTabBarBadge()
  }

  /**
   * 添加新会话
   */
  const addConversation = (conversation: Conversation) => {
    const existingIndex = conversationList.value.findIndex(
      (conv) => conv.conversationID === conversation.conversationID
    )

    if (existingIndex >= 0) {
      // 更新现有会话
      conversationList.value[existingIndex] = conversation
    } else {
      // 添加新会话
      conversationList.value.unshift(conversation)
    }

    // 重新计算未读数
    totalUnreadCount.value = conversationList.value.reduce(
      (total, conv) => total + conv.unreadCount,
      0
    )
    updateTabBarBadge()
  }

  /**
   * 更新会话未读数
   */
  const updateConversationUnreadCount = (conversationId: string, unreadCount: number) => {
    const conversation = conversationList.value.find(
      (conv) => conv.conversationID === conversationId
    )
    if (conversation) {
      conversation.unreadCount = unreadCount

      // 重新计算总未读数
      totalUnreadCount.value = conversationList.value.reduce(
        (total, conv) => total + conv.unreadCount,
        0
      )
      updateTabBarBadge()
    }
  }

  /**
   * 设置当前会话
   */
  const setCurrentConversation = (conversationId: string) => {
    currentConversationId.value = conversationId

    // 清空当前消息列表
    currentMessages.value = []

    // 标记会话为已读
    updateConversationUnreadCount(conversationId, 0)
  }

  /**
   * 添加消息到当前会话
   */
  const addMessageToCurrentConversation = (message: Message) => {
    if (message.conversationID === currentConversationId.value) {
      currentMessages.value.push(message)
    }
  }

  /**
   * 更新消息已读状态
   */
  const updateMessageReadStatus = (messageId: string, isRead: boolean) => {
    const message = currentMessages.value.find((msg) => msg.ID === messageId)
    if (message) {
      message.isRead = isRead
    }
  }

  /**
   * 缓存用户信息
   */
  const cacheUserInfo = (userId: string, userInfo: any) => {
    userInfoCache[userId] = userInfo
  }

  /**
   * 获取缓存的用户信息
   */
  const getCachedUserInfo = (userId: string) => {
    return userInfoCache[userId]
  }

  /**
   * 缓存群组信息
   */
  const cacheGroupInfo = (groupId: string, groupInfo: any) => {
    groupInfoCache[groupId] = groupInfo
  }

  /**
   * 获取缓存的群组信息
   */
  const getCachedGroupInfo = (groupId: string) => {
    return groupInfoCache[groupId]
  }

  /**
   * 更新tabbar角标
   */
  const updateTabBarBadge = () => {
    if (totalUnreadCount.value > 0) {
      uni.setTabBarBadge({
        index: 2, // 消息tab的索引
        text: totalUnreadCount.value > 99 ? '99+' : totalUnreadCount.value.toString(),
      })
    } else {
      uni.removeTabBarBadge({
        index: 2,
      })
    }
  }

  /**
   * 清空所有数据
   */
  const clearAll = () => {
    status.value = IMStatus.NOT_INITIALIZED
    isConnected.value = false
    currentUserId.value = ''
    conversationList.value = []
    totalUnreadCount.value = 0
    currentConversationId.value = ''
    currentMessages.value = []

    // 清空缓存
    Object.keys(userInfoCache).forEach((key) => delete userInfoCache[key])
    Object.keys(groupInfoCache).forEach((key) => delete groupInfoCache[key])

    // 清除tabbar角标
    uni.removeTabBarBadge({
      index: 2,
    })
  }

  /**
   * 发送消息
   */
  const sendMessage = async (to: string, content: string, conversationType: string) => {
    try {
      const message = await imManager.sendTextMessage(to, content, conversationType)
      if (message) {
        addMessageToCurrentConversation(message)
        return message
      }
      return null
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 加载会话列表
   */
  const loadConversationList = async () => {
    try {
      const conversations = await imManager.getConversationList()
      updateConversationList(conversations)
      return conversations
    } catch (error) {
      console.error('加载会话列表失败:', error)
      throw error
    }
  }

  /**
   * 加载消息列表
   */
  const loadMessageList = async (conversationId: string, nextReqMessageID?: string) => {
    try {
      const result = await imManager.getMessageList(conversationId, nextReqMessageID)

      if (!nextReqMessageID) {
        // 首次加载，替换消息列表
        currentMessages.value = result.messageList.reverse()
      } else {
        // 加载更多，插入到前面
        currentMessages.value = [...result.messageList.reverse(), ...currentMessages.value]
      }

      return result
    } catch (error) {
      console.error('加载消息列表失败:', error)
      throw error
    }
  }

  /**
   * 标记消息已读
   */
  const markMessageAsRead = async (conversationId: string) => {
    try {
      await imManager.markMessageAsRead(conversationId)
      updateConversationUnreadCount(conversationId, 0)
    } catch (error) {
      console.error('标记消息已读失败:', error)
      throw error
    }
  }

  // 监听IM状态变化
  imStatusManager.addStatusListener(setStatus)

  return {
    // 状态
    status,
    isConnected,
    currentUserId,
    conversationList,
    totalUnreadCount,
    currentConversationId,
    currentMessages,

    // 方法
    setStatus,
    setCurrentUserId,
    updateConversationList,
    addConversation,
    updateConversationUnreadCount,
    setCurrentConversation,
    addMessageToCurrentConversation,
    updateMessageReadStatus,
    cacheUserInfo,
    getCachedUserInfo,
    cacheGroupInfo,
    getCachedGroupInfo,
    clearAll,
    sendMessage,
    loadConversationList,
    loadMessageList,
    markMessageAsRead,
  }
})

export default useIMStore
