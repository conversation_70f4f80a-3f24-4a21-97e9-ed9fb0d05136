# 🔧 IM即时通信后端配置说明

## 📋 概述

车城app的IM功能需要后端提供以下关键服务：
1. **UserSig生成服务** - 用户身份验证签名
2. **IM配置管理** - 动态配置IM参数
3. **用户管理接口** - 用户信息同步
4. **消息推送服务** - 离线消息推送

## 🔑 核心配置项

### 1. **腾讯云IM基础配置**

```json
{
  "SDKAppID": 1400000000,           // 腾讯云IM应用ID
  "SecretKey": "your_secret_key",   // 应用密钥
  "AdminUserID": "admin",           // 管理员用户ID
  "ExpireTime": 86400               // UserSig有效期(秒)
}
```

### 2. **服务器环境配置**

```json
{
  "environment": "production",      // 环境: development/staging/production
  "serverUrl": "https://api.carcity.com",
  "imApiPrefix": "/api/v1/im",
  "enableTestMode": false,          // 是否启用测试模式
  "logLevel": "info"               // 日志级别
}
```

## 🚀 必需的后端接口

### 1. **获取UserSig接口**

**接口地址**: `POST /api/v1/im/getUserSig`

**请求参数**:
```json
{
  "userID": "user123",              // 用户ID
  "expireTime": 86400               // 可选，过期时间(秒)
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "userSig": "eJw1jdEKgj...",     // 生成的UserSig
    "expireTime": 1640995200,       // 过期时间戳
    "userID": "user123"             // 用户ID
  }
}
```

**后端实现示例** (Node.js):
```javascript
const TLSSigAPIv2 = require('tls-sig-api-v2');

app.post('/api/v1/im/getUserSig', (req, res) => {
  const { userID, expireTime = 86400 } = req.body;
  
  if (!userID) {
    return res.json({ code: -1, message: '用户ID不能为空' });
  }
  
  try {
    const api = new TLSSigAPIv2.Api(SDKAppID, SecretKey);
    const userSig = api.genUserSig(userID, expireTime);
    
    res.json({
      code: 0,
      message: 'success',
      data: {
        userSig,
        expireTime: Date.now() + expireTime * 1000,
        userID
      }
    });
  } catch (error) {
    res.json({ code: -1, message: '生成UserSig失败' });
  }
});
```

### 2. **IM配置管理接口**

**获取配置**: `GET /api/v1/admin/im-config`

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "SDKAppID": 1400000000,
    "SERVER_URL": "https://api.carcity.com/api/v1/im",
    "DEFAULT_AVATAR": "/static/images/user/default_avatar.png",
    "environment": "production",
    "testMode": false
  }
}
```

**保存配置**: `POST /api/v1/admin/im-config`

**请求参数**:
```json
{
  "SDKAppID": 1400000000,
  "SERVER_URL": "https://api.carcity.com/api/v1/im",
  "DEFAULT_AVATAR": "/static/images/user/default_avatar.png",
  "environment": "production",
  "testMode": false
}
```

### 3. **用户信息同步接口**

**接口地址**: `POST /api/v1/im/syncUser`

**请求参数**:
```json
{
  "userID": "user123",
  "nickname": "张三",
  "avatar": "https://example.com/avatar.jpg",
  "phone": "13800138000"
}
```

## 🔐 安全配置

### 1. **UserSig生成安全要求**

- ✅ **服务器端生成**: UserSig必须在服务器端生成，不能在客户端生成
- ✅ **密钥保护**: SecretKey必须保存在服务器端，不能泄露给客户端
- ✅ **有效期控制**: 设置合理的UserSig有效期，建议24小时
- ✅ **用户验证**: 生成UserSig前验证用户身份和权限

### 2. **接口安全**

```javascript
// 示例：接口权限验证中间件
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization;
  
  if (!token) {
    return res.json({ code: 401, message: '未授权访问' });
  }
  
  // 验证token有效性
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.json({ code: 401, message: 'Token无效' });
  }
};

// 应用到IM接口
app.use('/api/v1/im', authMiddleware);
```

## 📊 数据库设计

### 1. **IM配置表** (im_config)

```sql
CREATE TABLE im_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  sdk_app_id BIGINT NOT NULL COMMENT 'IM应用ID',
  secret_key VARCHAR(255) NOT NULL COMMENT '应用密钥',
  server_url VARCHAR(255) NOT NULL COMMENT '服务器地址',
  environment ENUM('development','staging','production') DEFAULT 'production',
  test_mode BOOLEAN DEFAULT FALSE COMMENT '是否测试模式',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. **用户IM信息表** (user_im_info)

```sql
CREATE TABLE user_im_info (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  im_user_id VARCHAR(50) NOT NULL COMMENT 'IM用户ID',
  nickname VARCHAR(100) COMMENT '昵称',
  avatar VARCHAR(255) COMMENT '头像URL',
  last_login_time TIMESTAMP COMMENT '最后登录时间',
  status ENUM('online','offline') DEFAULT 'offline',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_id (user_id),
  UNIQUE KEY uk_im_user_id (im_user_id)
);
```

## 🔧 部署配置

### 1. **环境变量配置**

```bash
# .env 文件
IM_SDK_APP_ID=1400000000
IM_SECRET_KEY=your_secret_key_here
IM_ADMIN_USER_ID=admin
IM_EXPIRE_TIME=86400
IM_SERVER_URL=https://api.carcity.com
IM_LOG_LEVEL=info
```

### 2. **Docker配置示例**

```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  im-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - IM_SDK_APP_ID=${IM_SDK_APP_ID}
      - IM_SECRET_KEY=${IM_SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - mysql
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: carcity_im
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## 📝 配置步骤

### 1. **腾讯云IM控制台配置**

1. 登录 [腾讯云IM控制台](https://console.cloud.tencent.com/im)
2. 创建新应用或选择现有应用
3. 获取 `SDKAppID` 和 `SecretKey`
4. 配置回调地址（可选）
5. 设置群组和好友关系链配置

### 2. **后端服务部署**

1. 安装依赖包：`npm install tls-sig-api-v2`
2. 配置环境变量
3. 实现UserSig生成接口
4. 部署到服务器
5. 配置HTTPS证书

### 3. **前端配置**

1. 访问IM配置管理页面：`/pages/admin/im-config`
2. 填写SDKAppID和服务器地址
3. 测试连接是否正常
4. 保存配置

## 🚨 注意事项

### ⚠️ **安全警告**

- 🔴 **禁止在客户端生成UserSig**
- 🔴 **SecretKey绝对不能泄露给客户端**
- 🔴 **生产环境必须关闭测试模式**
- 🔴 **定期更换SecretKey**

### 💡 **最佳实践**

- ✅ 使用HTTPS协议
- ✅ 实现接口限流和防刷
- ✅ 记录详细的操作日志
- ✅ 定期备份配置数据
- ✅ 监控IM服务状态

## 📞 技术支持

如有问题，请参考：
1. [腾讯云IM官方文档](https://cloud.tencent.com/document/product/269)
2. [UserSig生成指南](https://cloud.tencent.com/document/product/269/32688)
3. 项目内的代码注释和示例

---

**🎯 配置完成后，车城app的IM功能将完全可用！**
