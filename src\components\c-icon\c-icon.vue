<template>
  <view style="font-size: 0">
    <text :class="['iconfont', 'icon-' + type]" :style="getStyle">{{ name }}</text>
  </view>
</template>

<script setup lang="ts">
import { defineComponent, computed } from 'vue'
import iconData from './iconfont.json'
defineComponent({
  name: 'CIcon',
})
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: '',
  },
  _style: {
    type: Object,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    default: () => {
      return {
        lineHeight: 1,
      }
    },
  },
  size: {
    type: [String, Number],
    default: '38',
  },
})

const name = computed(() => {
  const curr = iconData.glyphs.find((item) => item.font_class === props.type)
  let text = ''
  // Android平台特殊处理
  // #ifdef APP-ANDROID
  // @ts-ignore
  text = new String(Character.toChars(parseInt(curr?.unicode || '', 16).toInt()))
  // #endif
  // 其他平台处理
  // #ifndef APP-ANDROID
  text = String.fromCharCode(parseInt(curr?.unicode || '', 16))
  // #endif
  return text
})

const getStyle = computed(() => {
  var style = Object.assign({}, props._style)
  if (props.size) {
    let size = props.size as number
    style.fontSize = uni.upx2px(size) + 'px'
  }

  if (props.color) {
    style.color = props.color
  }
  return style
})
</script>

<style lang="scss" scoped>
@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
.icon-loading {
  animation: rotating 2s linear infinite;
}

@font-face {
  font-family: 'iconfont'; /* Project id 4983354 */
  src: url('iconfont.woff2?t=17534243904166') format('woff2'),
    url('iconfont.woff?t=17534243904166') format('woff'),
    url('iconfont.ttf?t=17534243904166') format('truetype'),
    url('iconfont.svg?t=17534243904166#iconfont') format('svg');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
