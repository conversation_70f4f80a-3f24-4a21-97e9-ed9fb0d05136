<template>
  <button
    :class="[
      type,
      size,
      circle ? 'circle' : '',
      loading ? 'loading' : '',
      block && 'block',
      plain && 'plain',
    ]"
    hover-class="button-hover"
    :disabled="disabled"
    @click.stop="triggerHandler"
  >
    <view :class="['text flex-row items-center justify-center']">
      <c-icon
        :class="[!iconOnly && 'mr-[12rpx]']"
        color="#ffffff"
        v-if="loading || icon"
        :type="loading ? 'loading' : icon"
      ></c-icon>
      <text :class="[loading ? 'loading' : '']">
        <slot></slot>
      </text>
    </view>
  </button>
</template>

<script>
export default {
  name: 'CButton',
  props: {
    type: {
      type: String,
      default: 'default',
    },
    icon: {
      type: String,
      default: '',
    },
    iconOnly: {
      type: Boolean,
      default: false,
    },
    plain: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'normal',
    },
    block: {
      type: Boolean,
      default: false,
    },
    circle: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    loading: {
      type: [Boolean, String],
      default: false,
    },
  },
  methods: {
    triggerHandler(e) {
      if (this.loading) return
      // #ifdef MP-WEIXIN
      this.$emit('click')
      // #endif
      // #ifndef MP-WEIXIN
      this.$emit('click2')
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
button {
  outline: none;
  display: flex;
  flex-direction: row;
  -webkit-align-items: center;
  -webkit-box-align: center;
  align-items: center;
  vertical-align: middle;
  height: 60rpx;
  line-height: 60rpx;
  margin: 0;
  justify-content: center;
  transition: opacity 0.2s;
  .text {
    font-size: 32rpx !important;
    text {
      color: #fff;
    }
  }
  &[disabled='true'] {
    opacity: 0.5;
    color: #dedede;
    pointer-events: none;
  }
  &:after {
    border: none;
  }
  &.block {
    // display: block;
    width: 100%;
  }
  &.primary {
    background: $color-primary;
    &.plain {
      border: 1rpx solid $color-primary;
      .text {
        color: $color-primary;
      }
      background: none;
      box-shadow: none;
      :deep(.iconfont) {
        color: $color-primary;
      }
    }
  }
  &.success {
    background: $color-success;
  }
  &.warning {
    background: $color-warning;
  }
  &.info {
    background: #909399;
  }
  &.danger {
    background: $color-danger;
  }
  &.default {
    background: #fff;
    border: 1rpx solid #dcdfe6;
    .text {
      color: #606266;
    }
    &.circle {
      border-radius: 30rpx;
    }
  }
  &.mini {
    height: 38rpx;
    line-height: 32rpx;
    padding: 0 12rpx;
    border-radius: 6rpx;
    .text {
      font-size: 22rpx !important;
    }
    &.circle {
      width: 32rpx;
      border-radius: 18rpx;
    }
  }
  &.small {
    height: 48rpx;
    line-height: 48rpx;
    padding: 0 22rpx;
    border-radius: 8rpx;
    .text {
      font-size: 28rpx !important;
    }
    &.circle {
      width: 48rpx;
      border-radius: 24rpx;
    }
  }
  &.large {
    height: 82rpx;
    padding: 0 32rpx;
    border-radius: 20rpx;
    line-height: 82rpx;
    .text {
      font-size: 32rpx !important;
    }
    &.circle {
      width: 82rpx;
      border-radius: 41rpx;
    }
  }
  &.flex {
    width: 100%;
    height: 80rpx;
    padding: 0 38rpx;
    border-radius: 8rpx;
    line-height: 80rpx;
    .text {
      font-size: 36rpx;
    }
    &.circle {
      border-radius: 40rpx;
    }
  }
  .text.loading {
    margin-left: 10rpx;
    opacity: 0.6;
  }
  &.loading {
    opacity: 0.8;
    pointer-events: none;
  }
  &.button-hover {
    transform: translate(1rpx, 1rpx);
    opacity: 0.8;
  }
}
</style>
