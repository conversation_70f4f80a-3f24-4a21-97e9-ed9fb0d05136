/**
 * 会话管理API
 */
import { request } from '@/utils/request'

export interface Conversation {
  id: string
  userId: string
  targetId: string
  targetType: 'user' | 'group'
  targetName: string
  targetAvatar?: string
  lastMessage?: string
  lastMessageTime?: string
  lastMessageType?: 'text' | 'image' | 'voice' | 'video' | 'file'
  unreadCount: number
  isTop: boolean
  doNotDisturb: boolean
  isDeleted: boolean
  createTime: string
  updateTime: string
  remarkName?: string
  draftMessage?: string
}

export interface ConversationResponse {
  code: number
  message: string
  data: Conversation
}

export interface ConversationListResponse {
  code: number
  message: string
  data: Conversation[]
  total?: number
}

export interface ConversationPageParams {
  current?: number
  size?: number
  userId?: string
  targetType?: 'user' | 'group'
  keyword?: string
  isTop?: boolean
  doNotDisturb?: boolean
}

/**
 * 修改会话
 */
export const updateConversation = (conversationId: string, data: Partial<Conversation>) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation',
    method: 'PUT',
    data: {
      id: conversationId,
      ...data,
    },
  })
}

/**
 * 新增会话
 */
export const createConversation = (data: {
  userId: string
  targetId: string
  targetType: 'user' | 'group'
  targetName: string
  targetAvatar?: string
  remarkName?: string
}) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation',
    method: 'POST',
    data,
  })
}

/**
 * 通过id删除会话
 */
export const deleteConversation = (conversationId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation',
    method: 'DELETE',
    data: {
      id: conversationId,
    },
  })
}

/**
 * 设置置顶用户(单聊)
 */
export const setUserConversationTop = (userId: string, targetUserId: string, isTop: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/topUserConfig',
    method: 'POST',
    data: {
      userId,
      targetUserId,
      isTop,
    },
  })
}

/**
 * 设置置顶群聊(群聊)
 */
export const setGroupConversationTop = (userId: string, groupId: string, isTop: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/topGroupConfig',
    method: 'POST',
    data: {
      userId,
      groupId,
      isTop,
    },
  })
}

/**
 * 初始化聊天
 */
export const initializeConversation = (userId: string, targetId: string, targetType: 'user' | 'group') => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/initialize',
    method: 'POST',
    data: {
      userId,
      targetId,
      targetType,
    },
  })
}

/**
 * 批量导入会话
 */
export const importConversations = (conversations: Array<Partial<Conversation>>) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/import',
    method: 'POST',
    data: {
      conversations,
    },
  })
}

/**
 * 设置置顶用户关闭(单聊)
 */
export const cancelUserConversationTop = (userId: string, targetUserId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/cancelTopUserConfig',
    method: 'POST',
    data: {
      userId,
      targetUserId,
    },
  })
}

/**
 * 设置置顶群聊关闭(群聊)
 */
export const cancelGroupConversationTop = (userId: string, groupId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/cancelTopGroupConfig',
    method: 'POST',
    data: {
      userId,
      groupId,
    },
  })
}

/**
 * 分页查询会话
 */
export const getConversationsPage = (params: ConversationPageParams = {}) => {
  return request<ConversationListResponse>({
    url: '/imConversation/page',
    method: 'GET',
    params: {
      current: params.current || 1,
      size: params.size || 20,
      userId: params.userId,
      targetType: params.targetType,
      keyword: params.keyword,
      isTop: params.isTop,
      doNotDisturb: params.doNotDisturb,
    },
  })
}

/**
 * 导出会话列表
 */
export const exportConversations = (params?: ConversationPageParams) => {
  return request<Blob>({
    url: '/imConversation/export',
    method: 'GET',
    params,
    responseType: 'blob',
  })
}

/**
 * 通过条件查询会话详情
 */
export const getConversationDetails = (conversationId: string) => {
  return request<ConversationResponse>({
    url: '/imConversation/details',
    method: 'GET',
    params: {
      id: conversationId,
    },
  })
}

/**
 * 获取用户的所有会话
 */
export const getUserConversations = (userId: string) => {
  return request<ConversationListResponse>({
    url: '/imConversation/user-conversations',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 清空会话历史消息
 */
export const clearConversationHistory = (conversationId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/clear-history',
    method: 'POST',
    data: {
      conversationId,
    },
  })
}

/**
 * 标记会话已读
 */
export const markConversationRead = (conversationId: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/mark-read',
    method: 'POST',
    data: {
      conversationId,
    },
  })
}

/**
 * 设置会话免打扰
 */
export const setConversationDoNotDisturb = (conversationId: string, doNotDisturb: boolean) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/do-not-disturb',
    method: 'POST',
    data: {
      conversationId,
      doNotDisturb,
    },
  })
}

/**
 * 保存会话草稿
 */
export const saveConversationDraft = (conversationId: string, draftMessage: string) => {
  return request<{ code: number; message: string }>({
    url: '/imConversation/save-draft',
    method: 'POST',
    data: {
      conversationId,
      draftMessage,
    },
  })
}

/**
 * 获取会话未读数量统计
 */
export const getConversationUnreadCount = (userId: string) => {
  return request<{
    code: number
    message: string
    data: {
      totalUnread: number
      userConversations: number
      groupConversations: number
      details: Array<{ conversationId: string; unreadCount: number }>
    }
  }>({
    url: '/imConversation/unread-count',
    method: 'GET',
    params: {
      userId,
    },
  })
}
