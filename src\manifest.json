	{
    "name" : "车城",
    "appid" : "__UNI__09F31AF",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "OAuth" : {},
            "Payment" : {},
            "Maps" : {},
            "Geolocation" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Record" : {},
            "Camera" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "minSdkVersion" : 21
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [
                            "applinks:static-mp-b2b16b76-9f7e-4706-b0c3-d832215ae654.next.bspapp.com"
                        ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx04a1a3b055566d4d",
                        "appsecret" : "d914d44c1f2f875c8c9228ef013d085e",
                        "UniversalLinks" : "https://static-mp-b2b16b76-9f7e-4706-b0c3-d832215ae654.next.bspapp.com/uni-universallinks/__UNI__09F31AF/"
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "tohung",
                        "appkey_ios" : "de914fa353bd31c4b8873bf1f541aa95",
                        "appkey_android" : "d20464c805382e69e33a9b67e5005550"
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx04a1a3b055566d4d",
                        "UniversalLinks" : "https://static-mp-b2b16b76-9f7e-4706-b0c3-d832215ae654.next.bspapp.com/uni-universallinks/__UNI__09F31AF/"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx04a1a3b055566d4d",
                        "UniversalLinks" : "https://static-mp-b2b16b76-9f7e-4706-b0c3-d832215ae654.next.bspapp.com/uni-universallinks/__UNI__09F31AF/"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default"
            }
        },
        "nativePlugins" : {
            "AP-FaceDetectModule" : {
                "__plugin_info__" : {
                    "name" : "APFaceDetectPlugin",
                    "description" : "阿里云金融级实人认证SDK",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx91461ee5c1551bd1",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "h5" : {
        "router" : {
            "mode" : "history",
            "base" : "/"
        },
        "title" : "加载中",
        "devServer" : {
            "https" : false
        }
    },
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans",
    "_spaceID" : "mp-b2b16b76-9f7e-4706-b0c3-d832215ae654"
}
