import { defineStore } from 'pinia'
export const useClientStore = defineStore('clientStore', {
  state: () => ({
    runtime: {} as PlusRuntime,
    clientInfo: {} as UniNamespace.GetSystemInfoResult,
    widgetInfo: {} as PlusRuntimeWidgetInfo,
  }),
  actions: {
    getClientInfo() {
      this.clientInfo = uni.getSystemInfoSync()
      // #ifdef APP
      this.runtime = plus.runtime
      plus.runtime.getProperty(plus.runtime.appid as string, (widgetInfo) => {
        this.widgetInfo = widgetInfo
        // console.log(this.widgetInfo)
      })
      // #endif
    },
  },
})
