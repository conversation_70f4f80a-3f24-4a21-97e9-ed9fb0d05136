import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";
import pluginVue from "eslint-plugin-vue";

export default [
  {
    files: ["**/*.{js,mjs,cjs,ts,vue}"],
    languageOptions: {
      globals: {
        Atomics: "readonly",
        SharedArrayBuffer: "readonly",
        plus: "readonly",
        uni: "readonly",
        require: "readonly",
        __dirname: "readonly",
        export: "readonly",
        module: "readonly",
        process: "readonly",
        getCurrentPages: "readonly",
        getApp: "readonly",
        defineComponent: "readonly",
        defineProps: "readonly",
        defineEmits: "readonly",
        defineExpose: "readonly",
      },
      // ignores: ["Home", "Login"],
      // rules: {
      //   "@typescript-eslint/no-explicit-any": "off",
      // },
    },
  },
  { languageOptions: { globals: globals.browser } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  ...pluginVue.configs["flat/essential"],
  {
    files: ["**/*.vue"],
    languageOptions: { parserOptions: { parser: tseslint.parser } },
  },
  // {
  //   rules: {
  //     '@typescript-eslint/no-explicit-any': 'off',
  //   },
  // },
];
