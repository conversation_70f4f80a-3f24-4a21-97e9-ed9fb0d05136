<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "详情",
    "enablePullDownRefresh": false,
    "navigationStyle": "custom",
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen>
    <view class="bg w-full fixed h-[420rpx]">
      <image
        :src="$imgUrl('/statics/images/header_bg2.png')"
        class="w-full"
        mode="aspectFill"
      ></image>
    </view>
    <view class="header sticky top-0 z-20 overflow-hidden">
      <view class="w-full h-[420rpx] absolute">
        <image
          :src="$imgUrl('/statics/images/header_bg2.png')"
          class="w-full h-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="w-full justify-end relative">
        <c-titlebar showBack>
          <view class="flex-row items-center">
            <image
              :src="$imgUrl(detailData.userAvatar || '/statics/user/default_avatar.png')"
              mode="widthFix"
              class="w-[60rpx] h-[60rpx]"
            ></image>
            <view class="ml-sm">
              <view class="mb-mn">
                <text class="text-lg text-title font-medium">{{ detailData.userName }}</text>
              </view>
              <view class="flex-row">
                <text class="text-xs text-secondary">{{ detailData.createTime }}</text>
                <text class="text-xs text-secondary ml-sm">{{ detailData.cityName }}</text>
              </view>
            </view>
          </view>
          <template #rightBtn>
            <view class="flex-row items-center px-xs">
              <view
                class="flex-row items-center justify-center h-[52rpx] w-[114rpx] rounded-[26rpx] bg-primary/20"
              >
                <c-icon type="plus" size="22" color="#0767FF"></c-icon>
                <text class="text-xs text-primary">关注</text>
              </view>
              <!-- #ifdef MP -->
              <button open-type="share" class="viewbutton p-xs ml-xs">
                <c-icon type="fenxiang" color="#0767FF"></c-icon>
              </button>
              <!-- #endif -->
              <!-- #ifdef APP -->
              <view class="p-xs ml-xs" @tap="handleShare('WXSceneSession')">
                <c-icon type="fenxiang" color="#0767FF"></c-icon>
              </view>
              <!-- #endif -->
            </view>
          </template>
        </c-titlebar>
      </view>
    </view>
    <view class="container relative z-10 mt-xl px-sm">
      <view class="card mb-sm">
        <view class="title flex-row items-center justify-between">
          <text
            class="text-title text-xl font-medium overflow-hidden whitespace-nowrap text-ellipsis"
            >{{ detailData.vehicleModel }}</text
          >
          <text class="price ml-mn shrink-0 text-highlight text-3xl font-bold"
            >{{ detailData.fixedPrice }}万</text
          >
        </view>
      </view>
      <view class="card mb-sm">
        <view class="title flex-row items-center mb-lg">
          <image
            :src="$imgUrl('/statics/icons/info.png')"
            mode="widthFix"
            class="w-[36rpx] h-[36rpx] mr-[10rpx]"
          ></image>
          <text class="text-xl font-medium">车辆信息</text>
        </view>
        <view class="info-list flex-row flex-wrap gap-sm">
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">车辆款型</text>
            </view>
            <text class="text-sm">{{ detailData.vehicleModel }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">上牌日期</text>
            </view>
            <text class="text-sm">{{ detailData.registrationDate }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">出厂日期</text>
            </view>
            <text class="text-sm">{{ detailData.productionDate }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">指导价</text>
            </view>
            <text class="text-sm">{{ detailData.guidePrice }}w</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">排量</text>
            </view>
            <text class="text-sm">{{ detailData.displacement }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">排放标准</text>
            </view>
            <text class="text-sm">{{ detailData.emissionStandard }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">真实公里</text>
            </view>
            <text class="text-sm">{{ detailData.mileage }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">车辆颜色</text>
            </view>
            <text class="text-sm">{{ detailData.vehicleColor }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">车辆配置</text>
            </view>
            <text class="text-sm">{{ detailData.vehicleConfiguration }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">详细车况</text>
            </view>
            <text class="text-sm">{{ detailData.vehicleConfiguration }}</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">过户次数</text>
            </view>
            <text class="text-sm">{{ detailData.transferCount }}次</text>
          </view>
          <view class="item">
            <view class="w-[140rpx] mr-mn">
              <text class="text-sm font-medium">所在地</text>
            </view>
            <text class="text-sm">{{ detailData.cityName }}</text>
          </view>
        </view>
      </view>
      <view class="card mb-sm">
        <view class="title flex-row items-center mb-lg">
          <image
            :src="$imgUrl('/statics/icons/img.png')"
            mode="widthFix"
            class="w-[36rpx] h-[36rpx] mr-[10rpx]"
          ></image>
          <text class="text-xl font-medium">车辆实拍</text>
        </view>
        <view class="image-list flex-row gap-[16rpx] flex-wrap">
          <view
            v-for="(item, index) in sliceImagelist"
            :key="index"
            class="w-[206rpx] h-[206rpx] relative rounded-[20rpx] overflow-hidden"
          >
            <image
              :src="$imgUrl(item.vehicleImageUrl)"
              lazy-load
              fade-show
              class="w-full h-full"
              mode="aspectFill"
              @tap.stop="handlePreviewImage(item.vehicleImageUrl)"
            >
            </image>
            <view
              v-if="index === state.imgMaxCount - 1"
              class="w-full h-full absolute z-10 items-center justify-center bg-black/50"
              @tap="state.imgMaxCount = detailData.appCarSourceImageVoList.length + 1"
            >
              <!-- <text class="text-white">+{{ detailData.imageList.length - state.imgMaxCount }}</text> -->
              <text class="text-white">查看全部</text>
              <text class="text-white">{{ detailData.appCarSourceImageVoList.length }}张></text>
            </view>
          </view>
        </view>
      </view>
      <view class="card mb-sm">
        <view class="title flex-row items-center mb-lg">
          <image
            :src="$imgUrl('/statics/icons/process.png')"
            mode="widthFix"
            class="w-[36rpx] h-[36rpx] mr-[10rpx]"
          ></image>
          <text class="text-xl font-medium">担保流程</text>
        </view>
        <view>
          <image
            :src="$imgUrl('/statics/images/process.png')"
            class="w-full"
            mode="widthFix"
          ></image>
        </view>
        <!-- <view class="process-list">
          <view
            class="process-row flex-row items-center mb-base"
            :class="[index % 2 === 0 ? 'justify-around' : 'justify-around']"
            v-for="(items, index) in processListGroup"
            :key="index"
          >
            <view
              v-for="(item, cindex) in items"
              :key="item.name"
              :class="['flex-row', 'items-center']"
            >
              <view class="items-center">
                <image :src="item.icon" class="w-[56rpx] h-[56rpx] mb-mn" mode="widthFix"></image>
                <text class="text-mn">{{ item.name }}</text>
              </view>
              <image
                v-if="cindex < items.length - 1"
                :src="
                  index % 2 === 0
                    ? '/static/icons/process/arrow_right.png'
                    : '/static/icons/process/arrow_left.png'
                "
                class="w-[70rpx]"
                mode="widthFix"
              ></image>
            </view>
          </view>
        </view> -->
      </view>
      <view class="bottom-bar h-[144rpx]">
        <view
          class="bg-white fixed left-0 bottom-0 w-full flex-row h-[144rpx] items-center justify-between"
        >
          <view class="left flex-row">
            <view class="p-sm items-center">
              <c-icon type="chat" size="42"></c-icon>
              <text class="mt-mn text-xs">聊价格</text>
            </view>
            <view class="p-sm items-center" @tap="registerIntercept({ ok: handleFollow })">
              <c-icon
                v-if="detailData.collectStatus"
                type="shoucangchenggong"
                size="42"
                color="#FCBD54"
              ></c-icon>
              <c-icon v-else type="shoucang4" size="42"></c-icon>
              <text class="mt-mn text-xs">收藏</text>
            </view>
            <view class="p-sm items-center" @tap="registerIntercept({ ok: handleSubscribe })">
              <c-icon
                v-if="detailData.isSubscribe"
                type="dingyue"
                size="42"
                color="#FCBD54"
              ></c-icon>
              <c-icon v-else type="dingyue" size="42"></c-icon>
              <text class="mt-mn text-xs">订阅</text>
            </view>
          </view>
          <view class="flex-row">
            <view class="btn phone-btn" @click="registerIntercept({ ok: handleMakePhoneCall })">
              <text class="text-white">电话咨询</text>
            </view>
            <view class="btn dingjin-btn ml-sm mr-base">
              <text class="text-white">定金保障</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { carDetail, type ICarResourceItem } from '@/api/carResource'
import { registerIntercept } from '@/utils/auth'
import { execShare, getPageInfo, makePhoneCall, message, previewImage } from '@/utils/util'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { ref } from 'vue'
const detailData = ref<ICarResourceItem>({
  appCarSourceImageVoList: [] as any,
} as ICarResourceItem)
const state = reactive({
  imgMaxCount: 9,
})

onLoad((options) => {
  uni.$emit('getInfoDetailData', function (data: ICarResourceItem) {
    detailData.value = data
  })
  if (options?.id) {
    getDetail(options.id)
  }
})

const getDetail = function (id: string) {
  carDetail(id).then((res) => {
    if (res.code !== 0) {
      message.warning(res.msg)
      return
    }
    detailData.value = res.data
  })
}

const sliceImagelist = computed(() => {
  if (!detailData.value.appCarSourceImageVoList) {
    return []
  }
  return detailData.value.appCarSourceImageVoList.slice(0, state.imgMaxCount)
})

// type Process = {
//   name: string
//   icon: string
// }
// const processList = ref<Process[]>([
//   {
//     name: '联系卖方确认信息',
//     icon: '/static/icons/process/1.png',
//   },
//   {
//     name: '在线视频验车',
//     icon: '/static/icons/process/2.png',
//   },
//   {
//     name: '洽谈成交价格',
//     icon: '/static/icons/process/3.png',
//   },
//   {
//     name: '签约定金保障合同',
//     icon: '/static/icons/process/6.png',
//   },
//   {
//     name: '支付定金至平台',
//     icon: '/static/icons/process/5.png',
//   },
//   {
//     name: '交易完成退定金',
//     icon: '/static/icons/process/4.png',
//   },
// ])

// const processListGroup = computed(() => {
//   const res: Process[][] = []
//   let num = 0
//   while (processList.value.length) {
//     const children = processList.value.splice(0, 3)
//     if (num % 2 === 0) {
//       res.push(children)
//     } else {
//       res.push(children.reverse())
//     }
//     num++
//   }
//   return res
// })

const handlePreviewImage = function (url) {
  previewImage(
    url,
    detailData.value.appCarSourceImageVoList.map((item) => item.vehicleImageUrl)
  )
}

const handleFollow = function () {
  if (detailData.value.collectStatus === 1) {
    detailData.value.collectStatus = 0
  } else {
    detailData.value.collectStatus = 1
  }
  if (detailData.value.collectStatus === 1) {
    message.warning('收藏成功')
  } else {
    message.warning('取消收藏成功')
  }
}

const handleSubscribe = function () {
  detailData.value.isSubscribe = !detailData.value.isSubscribe
  if (detailData.value.isSubscribe) {
    message.warning('订阅成功')
  } else {
    message.warning('取消订阅成功')
  }
}
const handleMakePhoneCall = function () {
  makePhoneCall({
    phoneNumber: detailData.value.mobile,
    fail(err) {
      console.log(err)
    },
    success(e) {
      console.log(1111)
      console.log(e)
    },
  })
}
// #ifdef APP
const handleShare = function (scene: 'WXSceneSession' | 'WXSceneTimeline' = 'WXSceneSession') {
  execShare({
    type: 0,
    title: detailData.value.vehicleModel,
    scene,
    imageUrl: detailData.value.appCarSourceImageVoList[0].vehicleImageUrl,
    miniProgram: {
      id: import.meta.env.VITE_WX_MINIPROGRAM_ID,
      path: `${getPageInfo().route}?id=${detailData.value.carSourceId}`,
    },
  })
}
// #endif
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  // console.log(getPageInfo())
  return {
    title: detailData.value.vehicleModel,
    path: `${getPageInfo().route}?id=${detailData.value.carSourceId}`,
    imageUrl: detailData.value.appCarSourceImageVoList[0].vehicleImageUrl,
  }
})
onShareTimeline(() => {
  // console.log(getPageInfo())
  return {
    title: detailData.value.vehicleModel,
    path: `${getPageInfo().route}?id=${detailData.value.carSourceId}`,
    imageUrl: detailData.value.appCarSourceImageVoList[0].vehicleImageUrl,
  }
})
// #endif
</script>

<style scoped lang="scss">
.card {
  border-radius: 20rpx;
  padding: 30rpx;
  background-color: #ffffff;
}
.info-list {
  .item {
    flex: 1;
    flex-direction: row;
    min-width: 40%;
    flex: 1;
  }
}

.bottom-bar {
  .btn {
    justify-content: center;
    align-items: center;
    width: 205rpx;
    height: 84rpx;
    border-radius: 16rpx;
  }
  .phone-btn {
    background: linear-gradient(270deg, #fe4306 0%, #fe975b 100%);
  }
  .dingjin-btn {
    background: linear-gradient(270deg, #1262ff 0%, #0a89ff 100%);
  }
}
</style>
