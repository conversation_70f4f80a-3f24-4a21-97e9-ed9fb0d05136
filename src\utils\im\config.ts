/**
 * IM配置文件
 * 腾讯云即时通信IM配置
 */

export const IM_CONFIG = {
  // 腾讯云IM应用的SDKAppID
  SDKAppID: 1600101880, // 请替换为你的SDKAppID

  // IM服务器地址
  SERVER_URL: 'http://192.168.110.14/api/app',

  // 默认头像
  DEFAULT_AVATAR: '/static/images/user/tx.png',

  // 日志级别配置
  LOG_LEVEL: {
    VERBOSE: 0, // 普通级别，日志量较多，接入时建议使用
    RELEASE: 1, // release级别，SDK 输出关键信息，生产环境时建议使用
    WARNING: 2, // 告警级别，SDK 只输出告警和错误级别的日志
    ERROR: 3, // 错误级别，SDK 只输出错误级别的日志
    SILENT: 4, // 无日志级别，SDK 将不打印任何日志
  },

  // 消息类型
  MESSAGE_TYPES: {
    TEXT: 'TIMTextElem',
    IMAGE: 'TIMImageElem',
    SOUND: 'TIMSoundElem',
    VIDEO: 'TIMVideoFileElem',
    FILE: 'TIMFileElem',
    LOCATION: 'TIMLocationElem',
    FACE: 'TIMFaceElem',
    CUSTOM: 'TIMCustomElem',
  },

  // 会话类型
  CONVERSATION_TYPES: {
    C2C: 'C2C', // 单聊
    GROUP: 'GROUP', // 群聊
  },

  // 群组类型
  GROUP_TYPES: {
    WORK: 'Work', // 工作群
    PUBLIC: 'Public', // 公开群
    MEETING: 'Meeting', // 会议群
    AVCHATROOM: 'AVChatRoom', // 直播群
  },

  // 消息优先级
  MESSAGE_PRIORITY: {
    HIGH: 'High',
    NORMAL: 'Normal',
    LOW: 'Low',
  },

  // 群成员角色
  GROUP_MEMBER_ROLE: {
    OWNER: 'Owner', // 群主
    ADMIN: 'Admin', // 管理员
    MEMBER: 'Member', // 普通成员
  },

  // 消息状态
  MESSAGE_STATUS: {
    UNSEND: 'unsend', // 未发送
    SUCCESS: 'success', // 发送成功
    FAIL: 'fail', // 发送失败
  },

  // 已读回执状态
  READ_RECEIPT_STATUS: {
    UNREAD: 'unread', // 未读
    READ: 'read', // 已读
    READ_BY_PEER: 'readByPeer', // 对方已读
  },
}

/**
 * 获取用户签名
 * 注意：应该从用户信息中直接获取imSign字段
 */
export async function getUserSig(userID: string): Promise<string> {
  console.error('getUserSig被调用，但应该直接使用用户信息中的imSign字段')
  throw new Error('无法获取UserSig，请确保用户信息中包含imSign字段')
}

/**
 * IM错误码映射
 */
export const IM_ERROR_CODES = {
  // 通用错误
  70001: '参数错误',
  70002: '签名错误',
  70003: '用户不存在',
  70004: '请求超时',
  70005: '网络错误',

  // 消息相关错误
  80001: '消息发送失败',
  80002: '消息格式错误',
  80003: '消息内容过长',
  80004: '消息频率限制',

  // 群组相关错误
  90001: '群组不存在',
  90002: '群组已满',
  90003: '无权限操作',
  90004: '群组类型错误',

  // 用户相关错误
  60001: '用户未登录',
  60002: '用户被踢下线',
  60003: '用户状态异常',
}

/**
 * 获取错误信息
 */
export function getErrorMessage(errorCode: number): string {
  return IM_ERROR_CODES[errorCode] || `未知错误(${errorCode})`
}

/**
 * 消息时间格式化
 */
export function formatMessageTime(timestamp: number): string {
  const now = new Date()
  const messageTime = new Date(timestamp)
  const diff = now.getTime() - messageTime.getTime()

  // 一分钟内
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 一小时内
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 今天
  if (now.toDateString() === messageTime.toDateString()) {
    return messageTime.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (yesterday.toDateString() === messageTime.toDateString()) {
    return `昨天 ${messageTime.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })}`
  }

  // 一周内
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return `${weekdays[messageTime.getDay()]} ${messageTime.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })}`
  }

  // 超过一周
  return messageTime.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

/**
 * 获取会话显示名称
 */
export function getConversationDisplayName(conversation: any): string {
  if (conversation.type === IM_CONFIG.CONVERSATION_TYPES.C2C) {
    return conversation.userProfile?.nick || conversation.userProfile?.userID || '未知用户'
  } else if (conversation.type === IM_CONFIG.CONVERSATION_TYPES.GROUP) {
    return conversation.groupProfile?.name || conversation.groupProfile?.groupID || '未知群组'
  }
  return '未知会话'
}

/**
 * 获取会话头像
 */
export function getConversationAvatar(conversation: any): string {
  if (conversation.type === IM_CONFIG.CONVERSATION_TYPES.C2C) {
    return conversation.userProfile?.avatar || IM_CONFIG.DEFAULT_AVATAR
  } else if (conversation.type === IM_CONFIG.CONVERSATION_TYPES.GROUP) {
    return conversation.groupProfile?.avatar || IM_CONFIG.DEFAULT_AVATAR
  }
  return IM_CONFIG.DEFAULT_AVATAR
}

/**
 * 获取消息显示内容
 */
export function getMessageDisplayContent(message: any): string {
  switch (message.type) {
    case IM_CONFIG.MESSAGE_TYPES.TEXT:
      return message.payload.text
    case IM_CONFIG.MESSAGE_TYPES.IMAGE:
      return '[图片]'
    case IM_CONFIG.MESSAGE_TYPES.SOUND:
      return '[语音]'
    case IM_CONFIG.MESSAGE_TYPES.VIDEO:
      return '[视频]'
    case IM_CONFIG.MESSAGE_TYPES.FILE:
      return '[文件]'
    case IM_CONFIG.MESSAGE_TYPES.LOCATION:
      return '[位置]'
    case IM_CONFIG.MESSAGE_TYPES.FACE:
      return '[表情]'
    case IM_CONFIG.MESSAGE_TYPES.CUSTOM:
      return '[自定义消息]'
    default:
      return '[未知消息]'
  }
}

/**
 * 检查是否需要显示时间
 */
export function shouldShowTime(currentMessage: any, previousMessage: any): boolean {
  if (!previousMessage) return true

  const timeDiff = currentMessage.time - previousMessage.time
  // 超过5分钟显示时间
  return timeDiff > 5 * 60 * 1000
}

export default IM_CONFIG
