<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "求购",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": true
}
</route>
<template>
  <c-page fixedHeight scollY>
    <view class="card">
      <!-- 发布类型 -->
      <view class="cate relative h-[80rpx] overflow-hidden">
        <view class="flex-row justify-between z-10 h-full">
          <view
            class="cate-item h-full flex-1 flex-row items-center justify-center"
            @click="formData.buyType = 1"
          >
            <text class="font-medium">求购</text>
          </view>
          <view
            class="cate-item h-full flex-1 flex-row items-center justify-center"
            @click="formData.buyType = 2"
          >
            <text class="font-medium">批发</text>
          </view>
        </view>
        <image
          :src="
            formData.buyType === 1
              ? $imgUrl('/statics/images/cate_bg1.png')
              : $imgUrl('/statics/images/cate_bg2.png')
          "
          class="absolute top-[-24rpx] left-0 h-[130rpx] w-full"
        ></image>
      </view>
    </view>
    <view class="card">
      <!-- 表单 -->
      <view class="p-base">
        <c-form label-width="180rpx">
          <c-form-item label="求购系列">
            <!-- <c-select :range="[]" :customer="false" v-model="formData.modelId"> </c-select> -->
            <selectCarSeries @confirm="onSelectSeries"></selectCarSeries>
          </c-form-item>
          <c-form-item label="年份要求">
            <view class="flex-row items-center">
              <c-date-selecter
                v-model="formData.yearRequestStart"
                type="month"
                :maxYear="0"
                :minYear="20"
                format="YYYY-MM"
              >
              </c-date-selecter>
              <view class="mx-mn"><text class="text-secondary">至</text></view>
              <c-date-selecter
                v-model="formData.yearRequestEnd"
                type="month"
                :maxYear="0"
                :minYear="20"
                format="YYYY-MM"
              >
              </c-date-selecter>
            </view>
          </c-form-item>
          <c-form-item label="里程要求">
            <c-input v-model="formData.mileageRequest" type="digit" unit="公里"> </c-input>
          </c-form-item>
          <c-form-item label="车身颜色">
            <select-data
              :customer="false"
              dictType="car_color"
              isDict
              :fieldNames="{ label: 'label', value: 'label' }"
              v-model="formData.colorRequest"
            >
            </select-data>
          </c-form-item>
        </c-form>
      </view>
    </view>

    <view class="card">
      <!-- 上传图片 -->
      <view class="up p-base">
        <view class="flex-row items-center justify-between mb-base">
          <view class="flex-row items-center">
            <text class="font-medium">上传图片</text>
            <!-- <text>*</text>
            <text>(长按图片排序)</text> -->
          </view>
          <view
            class="flex-row items-center justify-center h-[52rpx] w-[174rpx] rounded-[26rpx] bg-primary/20"
          >
            <c-icon type="dingyue" size="26" color="#0767FF"></c-icon>
            <text class="ml-mn text-primary text-[26rpx]">求购说明</text>
          </view>
        </view>

        <c-upload @change="onUpload" :maxCount="3"> </c-upload>
      </view>
      <!-- 详细需求 -->
      <view class="p-base">
        <textarea
          class="h-[180rpx] rounded-[12rpx] w-full p-xs bg-[#f5f5f5]"
          :maxlength="180"
          v-model="formData.content"
          placeholder="填写详细的求购需求，请勿填写联系方式等个人信息;如您需要长期发布收购信息，请点击上方收购"
        ></textarea>
      </view>
    </view>
    <view class="mt-[24rpx] bottom-btn">
      <c-button type="primary" block size="large" @click="handleSubmit()" :loading="submiting">
        提交
      </c-button>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import selectCarSeries from '../components/selectCarSeries.vue'
import { message } from '@/utils/util'
import { create, type IDemand } from '@/api/demand'
// const data = ref<string>('data')
const formData = ref<IDemand>({
  buyType: 1,
  carSourceImageDTOList: [] as any,
  seriesIds: [] as string[],
  seriesNames: [] as string[],
} as IDemand)
const onUpload = function (e) {
  if (e.type === 'upload') {
    formData.value.carSourceImageDTOList.push({
      vehicleImage: e.data,
    })
  }
}

const onSelectSeries = function (e) {
  formData.value.seriesIds = e.map((item) => item.seriesId)
}

const submiting = ref(false)
const handleSubmit = function () {
  console.log(formData.value)
  // if (formData.value.carSourceImageDTOList.length < 5) {
  //   message.warning('请至少上传5张照片')
  //   return
  // }
  if (!formData.value.seriesIds?.length) {
    message.warning('请至少选择一个求购系列！')
    return
  }
  if (!formData.value.content) {
    message.warning('请填写需求内容！')
    return
  }
  message.loading('发布中...')
  submiting.value = true
  create(formData.value)
    .then((res) => {
      console.log(res)
      if (res.code !== 0) {
        message.warning(res.msg)
      } else {
        message.success('发布成功！')
      }
    })
    .finally(() => {
      message.hideLoading()
      submiting.value = false
    })
}
</script>

<style scoped lang="scss">
.card {
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
}
</style>
