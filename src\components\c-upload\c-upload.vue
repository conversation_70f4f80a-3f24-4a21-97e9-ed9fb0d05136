<template>
  <view class="flex-row">
    <view class="flex-row justify-content gap-mn flex-wrap" v-if="props.showList">
      <view
        v-for="(item, index) in fileList"
        :key="index"
        class="img-container w-[122rpx] h-[122rpx] p[12rpx] relative"
      >
        <image
          :src="item.url ? remoteImg(item.url) : item.filePath"
          class="w-full h-full rounded-lg"
          mode="aspectFill"
          @click="handlePreviewImage(item)"
        ></image>
        <view
          class="w-2xl h-2xl p-xs rounded-[50%] absolute z-10 top-0 right-0 justify-center items-center bg-highlight"
          @click="handleRemove(index)"
        >
          <text class="text-sm text-white">-</text>
        </view>
      </view>
      <view v-if="fileList.length < props.maxCount || !props.showList" @click="handleChoose">
        <slot>
          <view
            class="w-[122rpx] h-[122rpx] justify-center items-center rounded-lg border-dashed border-border"
          >
            <image
              :src="$imgUrl('/statics/user/camera.png')"
              class="w-[64rpx]"
              mode="widthFix"
            ></image>
          </view>
        </slot>
      </view>
    </view>
    <view v-else>
      <view @click="handleChoose">
        <slot>
          <view
            class="w-[122rpx] h-[122rpx] justify-center items-center rounded-lg border-dashed border-border"
          >
            <image
              :src="$imgUrl('/statics/user/camera.png')"
              class="w-[64rpx]"
              mode="widthFix"
            ></image>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getToken } from '@/utils/auth'
import { message, previewImage } from '@/utils/util'
import { ref } from 'vue'

const props = defineProps({
  maxCount: {
    type: Number,
    default: 1,
  },
  showList: {
    type: Boolean,
    default: true,
  },
  autoUpload: {
    type: Boolean,
    default: true,
  },
})
type FileInfo = {
  url: string
  filePath: string
  state: 'choosed' | 'success' | 'uploading' | 'fail'
}
const fileList = ref<FileInfo[]>([])

const emit = defineEmits(['change'])
const chooseImage = function (): Promise<UniApp.ChooseImageSuccessCallbackResult> {
  return new Promise((resolve) => {
    uni.chooseImage({
      count: props.maxCount - fileList.value.length,
      sizeType: ['original'],
      sourceType: ['album ', 'camera'],
      success: (res) => {
        resolve(res)
      },
    })
  })
}

const uploadFile = function (filePath): Promise<UniApp.UploadFileSuccessCallbackResult> {
  const token = getToken()
  return new Promise((resolve) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_APP_BASE_URL}/app/appFile/upload`,
      header: {
        Authorization: token,
      },
      timeout: 1000 * 30,
      // formData: {
      //   type: 'idCard',
      // },
      filePath,
      name: 'file',
      success: (res) => {
        console.log(res)
        resolve(res)
      },
      fail: (err: any) => {
        throw new Error(err.message || '上传失败')
      },
    })
  })
}

const handleUpload = async function (imgInfo) {
  const uploadedList: string[] = []
  for (let i = 0; i < imgInfo.tempFilePaths.length; i++) {
    const curr = fileList.value.find((item) => item.filePath === imgInfo.tempFilePaths[i])
    if (curr?.filePath) {
      curr.state = 'uploading'
    }
    const res = await uploadFile(imgInfo.tempFilePaths[i])
    if (res.data) {
      // console.log(res.data)
      const resData = JSON.parse(res.data)
      // console.log(resData.data.url)
      if (curr?.filePath) {
        curr.url = resData.data.url
        curr.state = 'success'
      }
      console.log(fileList.value)
      uploadedList.push(resData.data.url)
      emit('change', {
        type: 'upload',
        data: resData.data.url,
      })
    } else {
      if (curr) {
        curr.state = 'fail'
      }
    }
  }
  emit('change', {
    type: 'end',
    data: uploadedList,
  })
}

const handleChoose = async function () {
  try {
    const imgInfo = await chooseImage()
    if (!imgInfo.tempFilePaths?.length) {
      message.warning('没有选择到文件')
      return
    }
    emit('change', {
      type: 'choose',
      data: imgInfo.tempFilePaths,
    })
    if (Array.isArray(imgInfo.tempFilePaths)) {
      const newFileList: FileInfo[] = imgInfo.tempFilePaths.map((item) => {
        return {
          url: '',
          filePath: item,
          state: 'choosed',
        }
      })
      fileList.value.push(...newFileList)
    } else {
      fileList.value.push({
        url: '',
        filePath: imgInfo.tempFilePaths,
        state: 'choosed',
      })
    }
    if (props.autoUpload) {
      handleUpload(imgInfo)
    }
  } catch (error) {}
}

const isRemoteImg = function (url: string) {
  return url.startsWith('http://') || url.startsWith('https://')
}

const remoteImg = function (path: string) {
  return `${import.meta.env.VITE_APP_BASE_URL}${path}`
}

const handleRemove = function (index: number) {
  fileList.value.splice(index, 1)
}

const handlePreviewImage = function (e) {
  // const curr = isRemoteImg(e.url) ? e.url : e.filePath
  const curr = e.url ? remoteImg(e.url) : e.filePath
  const urls = fileList.value.map((item) => {
    // return isRemoteImg(item.url) ? item.url : item.filePath
    return item.url ? remoteImg(item.url) : item.filePath
  })
  console.log(curr)
  console.log(urls)
  previewImage(curr, urls)
}
</script>

<style scoped lang="scss"></style>
