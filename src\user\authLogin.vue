<route lang="json">
{
  "style": {
    "navigationBarTitleText": "微信登录",
    "navigationStyle": "custom"
  },
  "auth": false
}
</route>
<template>
  <view
    class="m-auto items-center justify-center pb-[80px] px-[48rpx]"
    :style="{ height: clientInfo.windowHeight - (clientInfo.statusBarHeight || 0) - 44 + 'px' }"
  >
    <image
      :src="$imgUrl('/statics/images/logo.png')"
      class="w-[180rpx] h-[180rpx] mb-[32rpx]"
      mode="widthFix"
    ></image>
    <c-button
      type="success"
      :loading="loging"
      block
      size="large"
      icon="wechat"
      @click="handleLogin"
    >
      使用微信登录
    </c-button>
    <view class="flex-row text-sm items-center mt-[48rpx]">
      <radio
        class="radio"
        @click="agreementChecked = !agreementChecked"
        color="#0767FF"
        :checked="agreementChecked"
      />
      <view class="flex-row text-sm items-center" @click="agreementChecked = !agreementChecked">
        <text>已阅读并同意</text>
        <view @click.stop>
          <navigator
            class="text-primary"
            hover-class="none"
            url="/pages/agreement/agreement?type=service"
          >
            <text>《服务协议》</text>
          </navigator>
        </view>
        <text>和</text>
        <view @click.stop>
          <navigator
            class="text-primary"
            hover-class="none"
            url="/pages/agreement/agreement?type=privacy"
          >
            <text>《隐私协议》</text>
          </navigator>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useClientStore } from '@/stores/client'
import { useUserStore } from '@/stores/user'
import { mnpLogin } from '@/api/account'
import { getProvider, message, oauthLogin } from '@/utils/util'
import { ref } from 'vue'
const agreementChecked = ref(false)
const loging = ref(false)
const { clientInfo } = useClientStore()
const userStore = useUserStore()
const handleLogin = async function () {
  if (!agreementChecked.value) {
    message.warning('请先同意隐私政策和用户服务协议')
    return
  }
  try {
    loging.value = true
    const provider = await getProvider('oauth')
    if (provider?.some((item) => item === 'weixin')) {
      const loginRes = await oauthLogin('weixin')
      const data = await mnpLogin(loginRes.code)
      // loginData.value = data
      onLoginSuccess(data)
      // const userInfo = await getUserInfo('weixin')
      // console.log(userInfo)
    } else {
      message.warning('未检测到相关配置')
    }
  } catch (error: any) {
    if (error.code === -8) {
      message.warning('客户端没有安装微信')
    } else {
      message.warning(error?.message || '授权登录失败')
    }
  }
  setTimeout(() => {
    loging.value = false
  }, 300)
}

const onLoginSuccess = async function (data) {
  const { access_token } = data
  console.log(access_token)
  userStore.login(access_token)
  await userStore.getUser()

  // 自动登录IM
  try {
    const { autoLoginIM } = await import('@/utils/im')
    await autoLoginIM()
  } catch (error) {
    console.error('IM自动登录失败:', error)
  }

  message.success('登录成功')
  uni.hideLoading()
  uni.reLaunch({
    url: '/pages/index/index',
  })
}
</script>

<style scoped lang="scss">
.radio {
  transform: scale(0.7);
}
</style>
