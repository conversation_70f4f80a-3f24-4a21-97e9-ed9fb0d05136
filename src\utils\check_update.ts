import request from '@/utils/request'
import { message, showModalSync } from './util'

const checkUpdate = async function (manual = false) {
  // #ifdef APP
  /**
   * 安装app更新，版本管理功能可以参考https://uniapp.dcloud.net.cn/uniCloud/upgrade-center.html
   * @param resource 资源包
   */
  const installUpdate = function (resource: string): Promise<PlusRuntimeWidgetInfo> {
    return new Promise((resolve, reject) => {
      plus.runtime.install(
        resource,
        { force: true },
        function onSuccess(widgetInfo) {
          resolve(widgetInfo)
        },
        function onError(error) {
          reject(error)
          console.log('安装失败：', error)
        }
      )
    })
  }

  /**
   * 获取本地应用资源信息（版本号等）
   */
  const getWidgetInfo = function (): Promise<PlusRuntimeWidgetInfo> {
    return new Promise((resolve) => {
      plus.runtime.getProperty(plus.runtime.appid as string, function (widgetInfo) {
        resolve(widgetInfo)
      })
    })
  }

  const widgetinfo = await getWidgetInfo()
  try {
    const res = await request.post({
      url: `/App/CompareAppVersion?VersionNumber=${widgetinfo.version}`,
    })
    // console.log(res)
    if (!res.data?.versionNumber) {
      if (manual) {
        message.warning(res.message || '没有新版本')
      }
      return
    }
    try {
      // 如果必须要更新
      if (res.data.Necessary) {
        await showModalSync({
          title: '有新版本',
          content: res.data.updateContent,
          confirmText: '立即更新',
          showCancel: false,
        })
      } else {
        await showModalSync({
          title: '有新版本',
          content: res.data.UpdateContent,
          confirmText: '立即更新',
          showCancel: false,
          // cancelText: '下次再说',
        })
      }
    } catch (err) {
      console.log(err)
      return
    }
    const downloadTask = uni.downloadFile({
      url: `${res.data.versionLink}`,
      success: async (downloadRes) => {
        uni.hideLoading()
        try {
          console.log(downloadRes.tempFilePath)
          await installUpdate(downloadRes.tempFilePath)
          if (res.data.versionLink.endsWith('.wgt')) {
            // plus.runtime.restart()
            await showModalSync({
              content: '已安装新版本，是否重启应用？',
            }).then(() => {
              plus.runtime.restart()
            })
          }
        } catch (err: any) {
          if (res.data.versionLink.endsWith('.apk')) {
            uni.showToast({
              title: err.message || err.msg || '安装失败',
              icon: 'none',
            })
          }
          console.log(err)
        }
      },
      fail: (err) => {
        uni.hideLoading()
        console.log('下载失败：', err)
      },
    })
    const showLoading = plus.nativeUI.showWaiting(`正在下载新版本`, { back: 'none' })
    downloadTask.onProgressUpdate((res) => {
      showLoading.setTitle(`正在下载新版本\n进度：${res.progress}%`)
      if (res.progress >= 100) {
        // setTimeout(() => {
        showLoading.close()
        // }, 300)
      }
    })
  } catch (error) {
    console.log(error)
  }
  // #endif
}

export default checkUpdate
