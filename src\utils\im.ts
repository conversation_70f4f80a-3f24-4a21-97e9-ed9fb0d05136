import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'
import { ref } from 'vue'
import { message } from './util'
import { IM_CONFIG } from './im/config'
import { useUserStore } from '@/stores/user'

// IM实例
let tim: any = null
let isInitialized = false

// 用户状态
export const imState = ref({
  isLogin: false,
  isConnecting: false,
  userID: '',
  userSig: '',
  sdkReady: false,
})

// 消息监听器
const messageListeners = new Set<Function>()
// 好友监听器
const friendListeners = new Set<Function>()
// 群组监听器
const groupListeners = new Set<Function>()

/**
 * 初始化TIM实例
 */
export const initTIM = async (): Promise<boolean> => {
  try {
    // 如果已经初始化，直接返回
    if (tim && isInitialized) {
      return true
    }

    // 创建TIM实例
    tim = TIM.create({
      SDKAppID: Number(import.meta.env.VITE_TIM_APP_ID) || 1600101880,
    })

    // 注册文件上传插件
    tim.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin })

    // 设置日志级别 (根据环境动态设置)
    const logLevel =
      import.meta.env.MODE === 'development'
        ? IM_CONFIG.LOG_LEVEL.VERBOSE
        : IM_CONFIG.LOG_LEVEL.RELEASE
    tim.setLogLevel(logLevel)

    // 监听事件
    bindTIMEvent()

    isInitialized = true
    return true
  } catch (error) {
    console.error('TIM初始化失败:', error)
    message.error('即时通讯初始化失败')
    isInitialized = false
    return false
  }
}

/**
 * 绑定TIM事件监听
 */
const bindTIMEvent = () => {
  if (!tim) return

  // SDK准备就绪
  tim.on(TIM.EVENT.SDK_READY, () => {
    imState.value.sdkReady = true
  })

  // SDK未准备好
  tim.on(TIM.EVENT.SDK_NOT_READY, () => {
    imState.value.sdkReady = false
  })

  // 连接状态变化
  tim.on(TIM.EVENT.KICKED_OUT, (event: any) => {
    console.log('被踢下线:', event)
    imState.value.isLogin = false
    message.warning('账号在其他设备登录')
  })

  // 收到新消息
  tim.on(TIM.EVENT.MESSAGE_RECEIVED, (event: any) => {
    console.log('收到新消息:', event)
    // 通知所有监听器
    messageListeners.forEach((listener) => listener(event.data))
  })

  // 网络状态变化
  tim.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
    console.log('网络状态变化:', event)
  })

  // 好友列表更新
  tim.on(TIM.EVENT.FRIEND_LIST_UPDATED, (event: any) => {
    console.log('好友列表更新:', event)
    friendListeners.forEach((listener) => listener(event.data))
  })

  // 群组列表更新
  tim.on(TIM.EVENT.GROUP_LIST_UPDATED, (event: any) => {
    console.log('群组列表更新:', event)
    groupListeners.forEach((listener) => listener(event.data))
  })

  // 好友申请
  tim.on(TIM.EVENT.FRIEND_APPLICATION_LIST_UPDATED, (event: any) => {
    console.log('好友申请更新:', event)
  })

  // 群组系统消息 (该事件在当前SDK版本中可能不支持，暂时注释)
  // tim.on(TIM.EVENT.GROUP_SYSTEM_NOTICE_RECEIVED, (event: any) => {
  //   console.log('群组系统消息:', event)
  // })

  // 消息撤回
  tim.on(TIM.EVENT.MESSAGE_REVOKED, (event: any) => {
    console.log('消息被撤回:', event)
  })

  // 消息已读回执
  tim.on(TIM.EVENT.MESSAGE_READ_RECEIPT_RECEIVED, (event: any) => {
    console.log('消息已读回执:', event)
  })
}

/**
 * 登录TIM
 */
export const loginTIM = async (userID: string, userSig: string): Promise<boolean> => {
  // 确保TIM已初始化
  if (!tim || !isInitialized) {
    const initSuccess = await initTIM()
    if (!initSuccess) {
      return false
    }
  }

  // 如果已经登录相同用户，直接返回成功
  if (imState.value.isLogin && imState.value.userID === userID) {
    return true
  }

  try {
    imState.value.isConnecting = true

    const { code } = await tim.login({ userID, userSig })

    if (code === 0) {
      imState.value.isLogin = true
      imState.value.userID = userID
      imState.value.userSig = userSig
      console.log('TIM登录成功')
      return true
    } else {
      console.error('TIM登录失败，错误码:', code)
      message.error(`即时通讯登录失败 (${code})`)
      return false
    }
  } catch (error) {
    console.error('TIM登录异常:', error)
    message.error('即时通讯登录异常')
    return false
  } finally {
    imState.value.isConnecting = false
  }
}

/**
 * 自动登录IM（用于用户登录后自动登录IM）
 */
export const autoLoginIM = async (): Promise<boolean> => {
  try {
    const userStore = useUserStore()

    // 检查用户是否已登录
    if (!userStore.isLogin) {
      return false
    }

    // 检查用户信息是否存在
    if (!userStore.userInfo) {
      return false
    }

    // 检查用户ID是否存在
    if (!userStore.userInfo.userId) {
      return false
    }

    const userID = userStore.userInfo.userId

    // 检查是否有imSign
    if (!userStore.userInfo.imSign) {
      console.error('用户信息中没有imSign字段，无法登录IM')
      throw new Error('用户信息中缺少imSign字段')
    }

    const success = await loginTIM(userID, userStore.userInfo.imSign)

    if (success) {
      console.log('IM自动登录成功')
      return true
    } else {
      console.error('IM自动登录失败')
      throw new Error('IM登录失败')
    }
  } catch (error) {
    console.error('IM自动登录异常:', error)
    throw error
  }
}

/**
 * 登出TIM
 */
export const logoutTIM = async (): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) return true

  try {
    const { code } = await tim.logout()
    if (code === 0) {
      imState.value.isLogin = false
      imState.value.userID = ''
      imState.value.userSig = ''
      return true
    }
    return false
  } catch (error) {
    console.error('TIM登出异常:', error)
    return false
  }
}

/**
 * 发送文本消息
 */
export const sendTextMessage = async (
  conversationID: string,
  text: string,
  type: 'C2C' | 'GROUP' = 'C2C'
): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const message = tim.createTextMessage({
      to: conversationID,
      conversationType: type,
      payload: { text },
    })

    const { code, data } = await tim.sendMessage(message)
    if (code === 0) {
      return data
    } else {
      throw new Error(`发送失败: ${code}`)
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    throw error
  }
}

/**
 * 获取会话列表
 */
export const getConversationList = async (): Promise<any[]> => {
  if (!tim || !imState.value.isLogin) {
    return []
  }

  try {
    const { data } = await tim.getConversationList()
    return data.conversationList || []
  } catch (error) {
    console.error('获取会话列表失败:', error)
    return []
  }
}

/**
 * 获取消息列表
 */
export const getMessageList = async (
  conversationID: string,
  nextReqMessageID?: string
): Promise<{
  messageList: any[]
  nextReqMessageID: string
  isCompleted: boolean
}> => {
  if (!tim || !imState.value.isLogin) {
    return {
      messageList: [],
      nextReqMessageID: '',
      isCompleted: true,
    }
  }

  try {
    const { data } = await tim.getMessageList({
      conversationID,
      count: 20,
      nextReqMessageID,
    })
    return {
      messageList: data.messageList || [],
      nextReqMessageID: data.nextReqMessageID || '',
      isCompleted: data.isCompleted || false,
    }
  } catch (error) {
    console.error('获取消息列表失败:', error)
    return {
      messageList: [],
      nextReqMessageID: '',
      isCompleted: true,
    }
  }
}

/**
 * 添加消息监听器
 */
export const addMessageListener = (listener: Function): void => {
  messageListeners.add(listener)
}

/**
 * 移除消息监听器
 */
export const removeMessageListener = (listener: Function): void => {
  messageListeners.delete(listener)
}

/**
 * 销毁TIM实例
 */
export const destroyTIM = (): void => {
  if (tim) {
    tim.off()
    tim = null
  }
  isInitialized = false
  imState.value.isLogin = false
  imState.value.sdkReady = false
  messageListeners.clear()
}

// 自动初始化（可选）
// #ifdef H5
// initTIM();
// #endif

/**
 * 发送图片消息
 */
export const sendImageMessage = async (
  conversationID: string,
  imageFile: File,
  type: 'C2C' | 'GROUP' = 'C2C'
): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const message = tim.createImageMessage({
      to: conversationID,
      conversationType: type,
      payload: {
        file: imageFile,
      },
    })

    const { code, data } = await tim.sendMessage(message)
    if (code === 0) {
      return data
    } else {
      throw new Error(`发送图片失败: ${code}`)
    }
  } catch (error) {
    console.error('发送图片消息失败:', error)
    throw error
  }
}

/**
 * 发送文件消息
 */
export const sendFileMessage = async (
  conversationID: string,
  file: File,
  type: 'C2C' | 'GROUP' = 'C2C'
): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const message = tim.createFileMessage({
      to: conversationID,
      conversationType: type,
      payload: {
        file: file,
      },
    })

    const { code, data } = await tim.sendMessage(message)
    if (code === 0) {
      return data
    } else {
      throw new Error(`发送文件失败: ${code}`)
    }
  } catch (error) {
    console.error('发送文件消息失败:', error)
    throw error
  }
}

/**
 * 发送语音消息
 */
export const sendAudioMessage = async (
  conversationID: string,
  audioFile: File,
  duration: number,
  type: 'C2C' | 'GROUP' = 'C2C'
): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const message = tim.createAudioMessage({
      to: conversationID,
      conversationType: type,
      payload: {
        file: audioFile,
        duration: duration,
      },
    })

    const { code, data } = await tim.sendMessage(message)
    if (code === 0) {
      return data
    } else {
      throw new Error(`发送语音失败: ${code}`)
    }
  } catch (error) {
    console.error('发送语音消息失败:', error)
    throw error
  }
}

/**
 * 撤回消息
 */
export const revokeMessage = async (message: any): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.revokeMessage(message)
    return code === 0
  } catch (error) {
    console.error('撤回消息失败:', error)
    throw error
  }
}

/**
 * 标记消息为已读
 */
export const markMessageAsRead = async (conversationID: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    return false
  }

  try {
    const { code } = await tim.setMessageRead({ conversationID })
    return code === 0
  } catch (error) {
    console.error('标记消息已读失败:', error)
    return false
  }
}

/**
 * 创建群组
 */
export const createGroup = async (options: {
  name: string
  type: string
  memberList?: Array<{ userID: string }>
}): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { data } = await tim.createGroup(options)
    return data
  } catch (error) {
    console.error('创建群组失败:', error)
    throw error
  }
}

/**
 * 加入群组
 */
export const joinGroup = async (groupID: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.joinGroup({ groupID })
    return code === 0
  } catch (error) {
    console.error('加入群组失败:', error)
    throw error
  }
}

/**
 * 退出群组
 */
export const quitGroup = async (groupID: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.quitGroup({ groupID })
    return code === 0
  } catch (error) {
    console.error('退出群组失败:', error)
    throw error
  }
}

/**
 * 获取群组列表
 */
export const getGroupList = async (): Promise<any[]> => {
  if (!tim || !imState.value.isLogin) {
    return []
  }

  try {
    const { data } = await tim.getGroupList()
    return data.groupList || []
  } catch (error) {
    console.error('获取群组列表失败:', error)
    return []
  }
}

/**
 * 获取群组成员列表
 */
export const getGroupMemberList = async (groupID: string): Promise<any[]> => {
  if (!tim || !imState.value.isLogin) {
    return []
  }

  try {
    const { data } = await tim.getGroupMemberList({ groupID })
    return data.memberList || []
  } catch (error) {
    console.error('获取群组成员列表失败:', error)
    return []
  }
}

/**
 * 添加好友
 */
export const addFriend = async (userID: string, remark?: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.addFriend({
      to: userID,
      remark: remark,
    })
    return code === 0
  } catch (error) {
    console.error('添加好友失败:', error)
    throw error
  }
}

/**
 * 删除好友
 */
export const deleteFriend = async (userID: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.deleteFriend({ to: userID })
    return code === 0
  } catch (error) {
    console.error('删除好友失败:', error)
    throw error
  }
}

/**
 * 获取好友列表
 */
export const getFriendList = async (): Promise<any[]> => {
  if (!tim || !imState.value.isLogin) {
    return []
  }

  try {
    const { data } = await tim.getFriendList()
    return data.friendList || []
  } catch (error) {
    console.error('获取好友列表失败:', error)
    return []
  }
}

/**
 * 设置好友备注
 */
export const setFriendRemark = async (userID: string, remark: string): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.setFriendRemark({ to: userID, remark })
    return code === 0
  } catch (error) {
    console.error('设置好友备注失败:', error)
    throw error
  }
}

/**
 * 添加好友监听器
 */
export const addFriendListener = (listener: Function): void => {
  friendListeners.add(listener)
}

/**
 * 移除好友监听器
 */
export const removeFriendListener = (listener: Function): void => {
  friendListeners.delete(listener)
}

/**
 * 添加群组监听器
 */
export const addGroupListener = (listener: Function): void => {
  groupListeners.add(listener)
}

/**
 * 移除群组监听器
 */
export const removeGroupListener = (listener: Function): void => {
  groupListeners.delete(listener)
}

/**
 * 获取用户资料
 */
export const getUserProfile = async (userID: string): Promise<any> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { data } = await tim.getUserProfile({ userIDList: [userID] })
    return data.userProfileList?.[0] || null
  } catch (error) {
    console.error('获取用户资料失败:', error)
    throw error
  }
}

/**
 * 更新个人资料
 */
export const updateMyProfile = async (profile: {
  nick?: string
  avatar?: string
  gender?: string
  birthday?: number
  location?: string
  selfSignature?: string
  allowType?: string
}): Promise<boolean> => {
  if (!tim || !imState.value.isLogin) {
    throw new Error('TIM未登录')
  }

  try {
    const { code } = await tim.updateMyProfile(profile)
    return code === 0
  } catch (error) {
    console.error('更新个人资料失败:', error)
    throw error
  }
}

// 消息类型定义
export interface Message {
  id: string
  type: string
  payload: any
  from: string
  to: string
  time: number
  isOwn: boolean
  avatar?: string
  nickname?: string
  timestamp: number
}

// 创建IM管理器对象
const imManager = {
  // 状态
  state: imState,

  // 初始化和登录
  init: initTIM,
  login: loginTIM,
  logout: logoutTIM,
  destroy: destroyTIM,

  // 消息相关
  sendTextMessage,
  sendImageMessage,
  sendFileMessage,
  sendAudioMessage,
  revokeMessage,
  markMessageAsRead,
  getMessageList,
  addMessageListener,
  removeMessageListener,

  // 会话相关
  getConversationList,

  // 群组相关
  createGroup,
  joinGroup,
  quitGroup,
  getGroupList,
  getGroupMemberList,
  addGroupListener,
  removeGroupListener,

  // 好友相关
  addFriend,
  deleteFriend,
  getFriendList,
  setFriendRemark,
  addFriendListener,
  removeFriendListener,

  // 用户资料
  getUserProfile,
  updateMyProfile,
}

// 默认导出IM管理器
export default imManager
