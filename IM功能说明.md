# 🚀 车城IM即时通信功能说明

## 📋 功能概览

车城app已完整集成腾讯云IM SDK，实现了企业级即时通信功能，包括：

### ✅ 核心功能
- **用户单聊** - 一对一实时聊天
- **群聊功能** - 多人群组聊天
- **消息已读状态** - 显示消息读取状态
- **实时消息推送** - 即时接收新消息
- **消息历史记录** - 支持消息分页加载

### ⚙️ 管理功能
- **聊天设置** - 免打扰、黑名单、备注名
- **群聊管理** - 成员管理、群设置、退出群聊
- **投诉举报** - 用户/群组举报功能
- **消息搜索** - 聊天内容搜索
- **会话管理** - 置顶、删除、清空记录

## 🗂️ 文件结构

```
src/
├── pages/chat/                 # 聊天页面
│   ├── chat.vue               # 聊天界面（单聊/群聊）
│   ├── settings.vue           # 聊天设置页面
│   ├── group-info.vue         # 群聊信息页面
│   ├── list.vue               # 聊天列表页面
│   └── test.vue               # IM功能测试页面
├── pages/report/               # 投诉举报
│   └── report.vue             # 投诉举报页面
├── utils/im/                   # IM工具类
│   ├── index.ts               # IM管理器主文件
│   ├── config.ts              # IM配置文件
│   └── init.ts                # IM初始化工具
├── stores/
│   └── im.ts                  # IM状态管理
└── api/
    └── chat.ts                # 聊天相关API
```

## 🎯 页面路由

### 主要页面
- **聊天列表**: `/pages/chat/list`
- **单聊页面**: `/pages/chat/chat?userId=xxx&username=xxx`
- **群聊页面**: `/pages/chat/chat?groupId=xxx&groupName=xxx`
- **聊天设置**: `/pages/chat/settings?userId=xxx&username=xxx`
- **群聊信息**: `/pages/chat/group-info?groupId=xxx&groupName=xxx`
- **投诉举报**: `/pages/report/report?userId=xxx` 或 `?groupId=xxx`
- **功能测试**: `/pages/chat/test`

## 🔧 使用方法

### 1. 初始化IM
```typescript
import { initIM } from '@/utils/im/init'

// 在应用启动时初始化
await initIM()
```

### 2. 发送消息
```typescript
import imManager from '@/utils/im'

// 发送文本消息
const message = await imManager.sendTextMessage(
  'target_user_id',     // 目标用户ID
  'Hello World!',       // 消息内容
  'C2C'                // 会话类型：C2C(单聊) 或 GROUP(群聊)
)
```

### 3. 监听消息
```typescript
import imManager from '@/utils/im'

// 添加消息监听器
imManager.addMessageListener((message) => {
  console.log('收到新消息:', message)
})
```

### 4. 获取会话列表
```typescript
import imManager from '@/utils/im'

// 获取会话列表
const conversations = await imManager.getConversationList()
```

### 5. 标记消息已读
```typescript
import imManager from '@/utils/im'

// 标记消息为已读
await imManager.markMessageAsRead('conversation_id')
```

## 🎨 UI设计特点

### 聊天界面
- **微信风格设计** - 熟悉的聊天界面布局
- **消息气泡** - 区分自己和对方的消息
- **时间显示** - 智能显示消息时间
- **已读状态** - 显示消息读取状态
- **头像展示** - 用户/群成员头像

### 聊天设置页面
- **用户信息展示** - 头像、昵称、认证标识
- **备注名编辑** - 支持自定义备注
- **关注状态** - 显示关注/互相关注状态
- **功能开关** - 免打扰、黑名单设置
- **操作按钮** - 清空记录、投诉举报

### 群聊信息页面
- **成员网格展示** - 群成员头像网格布局
- **添加/删除成员** - 群管理功能
- **群设置选项** - 群名称、备注、免打扰等
- **退出群聊** - 安全退出确认

## 🔐 配置说明

### IM配置 (`src/utils/im/config.ts`)
```typescript
export const IM_CONFIG = {
  SDKAppID: 1400000000,        // 腾讯云IM应用ID
  SERVER_URL: 'https://...',   // 服务器地址
  DEFAULT_AVATAR: '...',       // 默认头像
  // ... 其他配置
}
```

### 获取UserSig
```typescript
// 生产环境必须从服务器获取
export async function getUserSig(userID: string): Promise<string> {
  // 调用服务器接口获取UserSig
  const response = await uni.request({
    url: `${IM_CONFIG.SERVER_URL}/getUserSig`,
    method: 'POST',
    data: { userID }
  })
  return response.data.userSig
}
```

## 📱 测试方法

### 1. 访问测试页面
打开浏览器访问：`http://localhost:8088/#/pages/chat/test`

### 2. 测试功能
- 点击"测试聊天设置"按钮测试聊天设置页面
- 点击"测试群聊信息"按钮测试群聊信息页面
- 点击"测试投诉举报"按钮测试投诉举报功能
- 点击"测试聊天页面"按钮测试聊天界面

### 3. 功能验证
- ✅ 页面正常加载
- ✅ UI样式符合设计图
- ✅ 交互功能正常
- ✅ 消息发送接收
- ✅ 已读状态显示

## 🚨 注意事项

### 1. 腾讯云IM配置
- 需要在腾讯云控制台创建IM应用
- 获取正确的SDKAppID
- 配置UserSig生成服务

### 2. 生产环境部署
- UserSig必须从服务器获取，不能在客户端生成
- 配置正确的服务器接口地址
- 设置合适的消息推送策略

### 3. 权限配置
- 配置消息通知权限
- 设置音频播放权限
- 配置网络访问权限

## 🔄 更新记录

### v1.0.0 (2025-08-22)
- ✅ 完整集成腾讯云IM SDK
- ✅ 实现用户单聊和群聊功能
- ✅ 添加消息已读状态显示
- ✅ 完成聊天设置和群聊管理页面
- ✅ 实现投诉举报功能
- ✅ 按照设计图完成UI界面
- ✅ 添加完整的API接口封装
- ✅ 实现IM状态管理和消息监听

## 📞 技术支持

如有问题，请查看：
1. 腾讯云IM官方文档
2. uni-app开发文档
3. 项目内的代码注释和类型定义

---

**🎉 恭喜！车城app的IM即时通信功能已完整实现！**
