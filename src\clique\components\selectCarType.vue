<template>
  <view class="cell flex-row items-center" @click="onSelect">
    <view class="flex-1">
      <text v-if="info.typeId" class="text-[30rpx]">{{ desc }}</text>
      <text v-else class="text-[30rpx] placeholder">请选择</text>
    </view>
    <c-icon type="jinru" size="32" color="#c3c3c3"></c-icon>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SelectCarType',
})
</script>
<script setup lang="ts">
import { navigateTo } from '@/utils/util'
import { defineComponent, ref } from 'vue'
const emit = defineEmits(['confirm'])
const info = ref<any>({})
const desc = computed(() => {
  return `${info.value.brandName}/${info.value.seriesName}`
})
const onSelect = function () {
  uni.$once('selectType', (e) => {
    info.value = e
    emit('confirm', e)
  })
  navigateTo('/clique/info/carBrand')
}
</script>

<style scoped lang="scss">
.cell {
  .placeholder {
    color: #8a929f;
  }
}
</style>
