<template>
  <view class="tabbar-container" :class="[props.isLine ? 'h-[1rpx] bg-white' : 'h-[120rpx]']">
    <view v-if="props.isLine" class="line h-[1rpx]"></view>
    <view v-else class="tabbar flex h-[96rpx] items-center justify-around">
      <template v-for="(item, index) in list" :key="index">
        <view class="mid-tab" v-if="index === 2">
          <image :src="item.iconPath" class="w-[140rpx]" mode="widthFix"></image>
        </view>
        <view
          v-else
          class="flex-1 flex flex-col justify-center items-center"
          @click="switchTab(item)"
        >
          <image
            :src="props.tabIndex === index ? item.selectedIconPath : item.iconPath"
            class="w-[42rpx]"
            mode="widthFix"
          ></image>
          <view class="mt-[6rpx]">
            <text :class="[props.tabIndex === index ? 'text-primary' : '', 'text-[24rpx]']">{{
              item.text
            }}</text>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  tabIndex: {
    type: Number,
    default: 0,
  },
  isLine: {
    type: Boolean,
    default: true,
  },
})
// uni.hideTabBar()
const list = ref<any[]>([
  {
    text: '车源',
    iconPath: '/static/icons/car_resource.png',
    selectedIconPath: '/static/icons/car_resource_actived.png',
    pagePath: '/pages/index/index',
  },
  {
    text: '圈子',
    iconPath: '/static/icons/clique.png',
    selectedIconPath: '/static/icons/clique_actived.png',
    pagePath: '/clique/carInfo/list',
  },
  {
    text: '发布',
    iconPath: '/static/icons/midButton.png',
    selectedIconPath: '/static/icons/clique_actived.png',
    pagePath: '/clique/carInfo/list',
  },
  {
    text: '消息',
    iconPath: '/static/icons/message.png',
    selectedIconPath: '/static/icons/message_actived.png',
    pagePath: '/pages/contacts/contacts',
  },
  {
    text: '我的',
    iconPath: '/static/icons/my.png',
    selectedIconPath: '/static/icons/my_actived.png',
    pagePath: '/pages/user/user',
  },
])

const switchTab = function (e) {
  uni.switchTab({
    url: e.pagePath,
  })
}
</script>

<style scoped lang="scss">
.tabbar-container {
  .tabbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9;
    // background-color: #ffffff;
    box-shadow: 0 -8rpx 8rpx 0 rgba(152, 152, 152, 0.051);
    .mid-tab {
      position: relative;
      top: -27rpx;
    }
  }
  .line {
    position: fixed;
    left: 0;
    right: 0;
    // #ifdef H5
    bottom: 45px;
    // #endif
    // #ifndef H5
    bottom: -10px;
    // #endif
    z-index: 900;
    height: 10px;
    background-color: #ffffff;
    box-shadow: 0 -4px 8px 0 rgba(152, 152, 152, 0.1);
  }
}
</style>
