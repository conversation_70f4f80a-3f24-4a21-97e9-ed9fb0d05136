<route type="home" lang="json">
{
  "style": {
    "navigationBarTitleText": "圈子",
    "enablePullDownRefresh": true,
    "navigationStyle": "custom",
    "app-plus": {
      "pullToRefresh": {
        "support": true,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen>
    <view class="bg w-full absolute h-[420rpx]">
      <image :src="$imgUrl('/statics/images/header_bg2.png')" class="w-full" mode="top"></image>
    </view>
    <view class="header sticky top-0 z-20 h-[340rpx]">
      <view class="w-full h-full absolute">
        <image :src="$imgUrl('/statics/images/header_bg2.png')" class="w-full" mode="top"></image>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col">
        <c-titlebar showBack title="圈子"></c-titlebar>
        <view>
          <c-tabs :current="state.currentTab" @change="switchTab"></c-tabs>
        </view>
        <view class="px-[16rpx] py-[20rpx]">
          <c-search></c-search>
        </view>
      </view>
    </view>
    <infoList v-if="state.currentTab === 'carSource'" ref="carSourceRef"></infoList>
    <demandList v-if="state.currentTab === 'demand'" ref="demandRef"></demandList>
  </c-page>
</template>

<script setup lang="ts">
// import { ref } from 'vue'
import infoList from './components/infoList.vue'
import demandList from './components/demandList.vue'
// import { registerIntercept, navigateTo } from '@/utils/util'
import { onLoad, onPullDownRefresh, onReachBottom, onReady } from '@dcloudio/uni-app'

const state = reactive({
  currentTab: 'carSource',
})
const switchTab = function (type: string) {
  if (state.currentTab === type) {
    return
  }
  state.currentTab = type
  nextTick(() => {
    // #ifdef MP-WEIXIN
    setTimeout(() => {
      initData()
    }, 200)
    // #endif
    // #ifndef MP-WEIXIN
    initData()
    // #endif
  })
}
const carSourceRef = ref()
const demandRef = ref()
const initData = function () {
  switch (state.currentTab) {
    case 'carSource':
      if (carSourceRef.value?.initData) {
        carSourceRef.value.initData()
      } else {
        setTimeout(() => {
          carSourceRef.value.initData()
        }, 200)
      }
      break
    case 'demand':
      if (demandRef.value?.initData) {
        demandRef.value.initData()
      } else {
        setTimeout(() => {
          demandRef.value.initData()
        }, 200)
      }
      break
  }
}

onLoad((options) => {
  if (options?.type) {
    state.currentTab = options.type
  }
})
onReady(() => {
  initData()
})
onReachBottom(() => {
  switch (state.currentTab) {
    case 'carSource':
      carSourceRef.value.loadMore()
      break
    case 'demand':
      demandRef.value.loadMore()
      break
  }
})
onPullDownRefresh(() => {
  initData()
})
</script>

<style lang="scss">
// .header {
//   position: sticky;
//   z-index: 9;
// }
</style>
