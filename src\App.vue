<script setup lang="ts">
import { onLaunch, onShow } from '@dcloudio/uni-app'
import { useAppStore } from './stores/app'
import { useUserStore } from './stores/user'
import { useClientStore } from './stores/client'
import checkUpdate from './utils/check_update'

const appStore = useAppStore()
const { getUser } = useUserStore()
const clientStore = useClientStore()
clientStore.getClientInfo()
import { getToken } from '@/utils/auth'
import { message } from './utils/util'

onLaunch(async () => {
  // 获取APP配置
  await appStore.getConfig()
  if (getToken()) {
    try {
      await getUser(true)

      // 自动登录IM
      try {
        const { autoLoginIM } = await import('@/utils/im')
        await autoLoginIM()
      } catch (error) {
        console.error('IM自动登录失败:', error)
      }
    } catch (error: any) {
      message.error(error.msg)
    }
  }
})

onShow(() => {
  checkUpdate()
})
</script>
<style lang="scss">
@font-face {
  font-family: 'din-bold';
  src: url('@/static/fonts/din-bold-2.ttf');
}

.price {
  font-family: 'din-bold';
}
.bottom-line {
  border-bottom: 1rpx solid $border-color;
}
.top-line {
  border-top: 1rpx solid $border-color;
}

.viewbutton {
  margin: 0;
  background: none;
  border: none;
  outline: none;
  &:after {
    border: none;
  }
}

.bottom-btn {
  position: sticky;
  bottom: 0;
  padding: 20rpx 24rpx;
  box-shadow: 0 -8rpx 12rpx 0 rgba(152, 152, 152, 0.1);
  background-color: #ffffff;
}
// @import '@/uni_modules/cool-ui/index.scss';
</style>
