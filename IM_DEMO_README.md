# IM功能演示页面使用说明

## 概述
这是一个基于腾讯云IM SDK的功能演示页面，展示了即时通讯的核心功能，包括登录、消息发送、好友管理、群组管理等。

## 功能特性

### 1. 连接状态监控
- 实时显示IM连接状态
- 显示当前登录用户信息
- SDK就绪状态指示

### 2. 登录控制
- 用户ID和UserSig输入
- 登录/退出功能
- 连接状态实时反馈
- 测试数据填充功能

### 3. 消息功能
- 发送文本消息
- 获取会话列表
- 获取消息历史（待完善）

### 4. 好友管理
- 添加好友
- 删除好友
- 获取好友列表
- 设置好友备注

### 5. 群组管理
- 创建群组
- 加入群组
- 获取群组列表
- 群组成员管理

### 6. 操作日志
- 实时显示所有操作日志
- 滚动显示最近50条记录
- 时间戳记录

## 配置要求

### 1. 环境变量配置
在 `.env` 文件中配置：
```
VITE_TIM_APP_ID=你的腾讯云IM应用ID
```

### 2. 腾讯云IM控制台配置
1. 登录腾讯云控制台
2. 创建IM应用
3. 获取SDKAppID
4. 配置应用参数

### 3. UserSig生成
UserSig需要通过后端服务生成，不能在前端硬编码：
- 使用腾讯云提供的服务端SDK
- 实现UserSig生成接口
- 前端调用接口获取UserSig

## 使用步骤

### 1. 配置SDKAppID
- 修改 `.env` 文件中的 `VITE_TIM_APP_ID`
- 重启开发服务器

### 2. 获取UserSig
- 通过后端API获取有效的UserSig
- 或使用腾讯云控制台的调试工具生成临时UserSig

### 3. 登录测试
- 输入用户ID（例如：user001）
- 输入有效的UserSig
- 点击"登录IM"按钮

### 4. 功能测试
- 登录成功后，各功能按钮将被启用
- 可以测试发送消息、添加好友等功能
- 查看操作日志了解执行结果

## 注意事项

### 1. 安全性
- UserSig包含敏感信息，不能在前端硬编码
- 生产环境必须通过后端API获取UserSig
- 定期更新UserSig以确保安全

### 2. 网络环境
- 确保网络连接正常
- 某些网络环境可能需要配置代理
- 移动端需要相应的网络权限

### 3. 调试建议
- 查看浏览器控制台日志
- 使用腾讯云IM控制台的调试工具
- 参考操作日志排查问题

## 常见问题

### 1. 登录失败
- 检查SDKAppID是否正确
- 确认UserSig是否有效且未过期
- 查看网络连接状态

### 2. 消息发送失败
- 确认已成功登录
- 检查接收方用户ID是否正确
- 验证网络连接状态

### 3. 好友操作失败
- 确认目标用户ID存在
- 检查用户权限设置
- 查看IM控制台的用户管理

## 技术栈
- Vue 3 + TypeScript
- 腾讯云IM SDK
- Uni-app框架
- TailwindCSS样式

## 相关文档
- [腾讯云IM官方文档](https://cloud.tencent.com/document/product/269)
- [IM SDK集成指南](https://cloud.tencent.com/document/product/269/68823)
- [UserSig生成指南](https://cloud.tencent.com/document/product/269/32688)
