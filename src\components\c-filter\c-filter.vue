<template>
  <view class="container">
    <view class="filter_bar flex-row items-center justify-around bg-white">
      <view
        v-for="(item, index) in state.filterTypes"
        :key="index"
        :class="[
          'item',
          'flex-row',
          'items-center',
          ' py-[30rpx]',
          ' px-[12rpx]',
          state.openIndex === index && 'open',
        ]"
        @click="handleToggle(index)"
      >
        <text>{{ item.valueName || item.name }}</text>
        <view :class="['icon', 'ml-[12rpx]']"
          ><c-icon
            size="22"
            :color="state.openIndex === index ? '#ec702d' : '#b3b3b3'"
            type="zhankai"
          ></c-icon
        ></view>
      </view>
    </view>
    <view
      :class="['options-container flex-col', state.openIndex >= 0 && 'open']"
      :style="{
        height: state.openIndex >= 0 ? state.filterTypes[state.openIndex].height : 0,
        top: state.filterBarInfo.top + state.filterBarInfo.height + 'px',
      }"
    >
      <view v-if="state.openIndex === 0" :class="['options-pancel overflow-scroll p-[24rpx]']">
        <view class="list">
          <view
            class="list_item py-[24rpx]"
            v-for="(item, index) in state.sortOptions"
            :key="index"
            @click="onSelectFilterOption(item)"
          >
            <text
              :class="[
                'text-[26rpx]',
                state.filterTypes[state.openIndex].value === item.field && 'text-primary',
              ]"
              >{{ item.name }}</text
            >
          </view>
        </view>
      </view>
      <view v-if="state.openIndex === 1" :class="['options-pancel']">
        <!-- <view v-for="brand in state.brandList" :key="brand.letter">
          <u-index-anchor :index="brand.letter" />
          <view
            class="flex list_item items-center py-[24rpx]"
            v-for="item in brand.data"
            :key="item.field"
          >
            <image :src="item.icon" mode="widthFix" class="w-[60rpx]"></image>
            <text
              :class="[
                'text-[26rpx]',
                state.filterTypes[state.openIndex].value === item.field && 'text-primary',
              ]"
              >{{ item.name }}</text
            >
          </view>
        </view> -->
        <c-list-index :dataList="state.brandList">
          <template #defalut="{ data }">
            <view
              class="flex-row items-center py-[24rpx]"
              v-for="(item, index) in data"
              :key="index"
              @click="onSelectFilterOption(item)"
            >
              <image :src="item.icon" mode="widthFix" class="w-[60rpx]"></image>
              <text
                :class="[
                  'text-[26rpx]',
                  state.filterTypes[state.openIndex].value === item.field && 'text-primary',
                ]"
                >{{ item.name }}</text
              >
            </view>
          </template>
        </c-list-index>
      </view>
    </view>
    <view
      :class="['mask', state.openIndex >= 0 && 'open']"
      :style="{ top: state.filterBarInfo.top + state.filterBarInfo.height + 'px' }"
      @click="state.openIndex = -1"
      @touchmove.stop.prevent="moveEvent"
    ></view>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'CFilter',
})
</script>
<script setup lang="ts">
import { defineComponent, getCurrentInstance, onMounted, reactive } from 'vue'
const state = reactive({
  openIndex: -1,
  filterTypes: [
    {
      name: '默认排序',
      paramKey: 'sort',
      height: '500rpx',
      value: '',
      valueName: '默认排序',
    },
    {
      name: '品牌车系',
      paramKey: 'brand',
      height: '300rpx',
      value: '',
      valueName: '',
    },
    {
      name: '价格',
      paramKey: 'price',
      height: '500rpx',
      value: '',
      valueName: '',
    },
    {
      name: '筛选',
      paramKey: 'customer',
      height: '200rpx',
      value: '',
      valueName: '',
    },
  ],
  sortOptions: [
    {
      name: '默认排序',
      field: '',
    },
    {
      name: '价格最低',
      field: 'lowPrice',
    },
    {
      name: '价格最高',
      field: 'upPrice',
    },
    {
      name: '最新发布',
      field: 'createTime',
    },
    {
      name: '车龄最短',
      field: 'year',
    },
    {
      name: '里程最少',
      field: 'mileage',
    },
  ],
  brandList: [
    {
      letter: 'A',
      data: [
        // '奥迪',
        {
          icon: '//g.autoimg.cn/@img/car2/cardfs/series/g28/M01/F6/71/64x64_autohomecar__CjIFVGTIoWqAdlH5AABfFDX2Peo393.png',
          name: '奥迪',
          field: 'audi',
        },
      ],
    },
    {
      letter: 'B',
      data: [
        // '宝马',
        // '奔驰',
        {
          icon: '//g.autoimg.cn/@img/car2/cardfs/series/g28/M08/10/45/64x64_autohomecar__CjIFVGUNeJWAOukrAADdG-QkWXI004.png',
          name: '宝马',
          field: 'bmw',
        },
        {
          icon: 'https://g.autoimg.cn/@img/car3/cardfs/series/g25/M03/8A/AD/64x64_autohomecar__ChxkqWTIoauAFpvzAAIcVia67wY486.png',
          name: '奔驰',
          field: 'benci',
        },
      ],
    },
    {
      letter: 'C',
      data: [
        // '宝马',
        // '奔驰',
        {
          icon: '//g.autoimg.cn/@img/car2/cardfs/series/g28/M08/10/45/64x64_autohomecar__CjIFVGUNeJWAOukrAADdG-QkWXI004.png',
          name: '长安',
          field: 'bmw',
        },
        {
          icon: 'https://g.autoimg.cn/@img/car3/cardfs/series/g25/M03/8A/AD/64x64_autohomecar__ChxkqWTIoauAFpvzAAIcVia67wY486.png',
          name: '长城',
          field: 'benci',
        },
        {
          icon: 'https://g.autoimg.cn/@img/car3/cardfs/series/g25/M03/8A/AD/64x64_autohomecar__ChxkqWTIoauAFpvzAAIcVia67wY486.png',
          name: '长城欧尚',
          field: 'benci',
        },
        {
          icon: 'https://g.autoimg.cn/@img/car3/cardfs/series/g25/M03/8A/AD/64x64_autohomecar__ChxkqWTIoauAFpvzAAIcVia67wY486.png',
          name: '创维汽车',
          field: 'benci',
        },
      ],
    },
  ],
  scrollTop: 0,
  filterBarInfo: {
    top: 0,
    height: 0,
  },
})

const emit = defineEmits(['filter'])

const handleToggle = function (index: number) {
  if (state.openIndex === index) {
    state.openIndex = -1
  } else {
    state.openIndex = index
  }
}

const initData = function () {
  state.openIndex = -1
  const params = {}
  state.filterTypes.forEach((item) => {
    params[item.paramKey] = item.value
  })
  emit('filter', params)
}

const onSelectFilterOption = function (e) {
  state.filterTypes[state.openIndex].value = e.field
  state.filterTypes[state.openIndex].valueName = e.name
  initData()
}

const { proxy }: any = getCurrentInstance()
const getBarInfo = function () {
  uni
    .createSelectorQuery()
    .in(proxy)
    .select('.filter_bar')
    .boundingClientRect((res: any) => {
      if (res) {
        // console.log(res)
        // #ifdef H5
        state.filterBarInfo.top = res.top + 44
        // #endif
        // #ifndef H5
        state.filterBarInfo.top = res.top
        // #endif
        state.filterBarInfo.height = res.height
      }
    })
    .exec()
}

const moveEvent = function () {
  return false
}

onMounted(() => {
  getBarInfo()
})
</script>

<style scoped lang="scss">
.container {
  position: relative;
}
.filter_bar {
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  z-index: 2;
  .item {
    .icon {
      transition: 0.26s;
    }
    text {
      transition: 0.26s;
    }
    &.open {
      text {
        color: $color-primary;
      }
      .icon {
        transform: rotate(180deg);
      }
    }
    // .icon {
    //   &.open {
    //     transform: rotate(180deg);
    //   }
    // }
  }
}

.options-container {
  height: 0;
  // padding: 0 32rpx;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #fff;
  transition: 0.26s;
  // position: relative;
  // z-index: 3;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 2;
  &.open {
    // padding: 32rpx;
  }
  .options-pancel {
    position: relative;
    height: 100%;
    // overflow: scroll;
  }
}
.mask {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: #000000;
  opacity: 0;
  z-index: -1;
  transition: 0.26s;
  &.open {
    opacity: 0.3;
    z-index: 1;
  }
}
.list {
  .list_item {
    border-bottom: 1rpx solid $border-color;
    text {
      transition: color 0.26s;
    }
  }
}
</style>
