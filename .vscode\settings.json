{"editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "css.validate": false, "less.validate": false, "scss.validate": false, "compile-hero.disable-compile-files-on-did-save-code": false}