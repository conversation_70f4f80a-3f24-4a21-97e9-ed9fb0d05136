import { createSSRApp } from 'vue'
import App from './App.vue'
import plugins from './plugins'
import './router'
import './styles/index.scss'
import { getImageUrl } from './utils/util'
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $imgUrl: (str?: string) => string
  }
}

export function createApp() {
  const app = createSSRApp(App)
  app.use(plugins)
  app.config.globalProperties.$imgUrl = function (path?: string) {
    if (!path) {
      return ''
    }
    return getImageUrl(path)
  }
  return {
    app,
  }
}
