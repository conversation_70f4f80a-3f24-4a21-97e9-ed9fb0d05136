export interface IChatListItem {
  id: string
  title: string
  avatar: string
  message: string
  time: string
  unready: number
}

export interface IFriend {
  id: string
  username: string
  avatar: string
  userID?: string // IM用户ID
  userSig?: string // IM用户签名
}

// 聊天设置相关接口
export interface IChatSettings {
  userId: string
  doNotDisturb: boolean
  isBlocked: boolean
}

export interface IGroupSettings {
  groupId: string
  doNotDisturb: boolean
  isTop: boolean
  showMemberNickname: boolean
}

export interface IUserInfo {
  id: string
  nickname: string
  avatar: string
  remarkName: string
  isVerified: boolean
  isFollowed: boolean
}

export interface IGroupInfo {
  id: string
  name: string
  remark: string
  memberCount: number
  members: IGroupMember[]
}

export interface IGroupMember {
  id: string
  nickname: string
  avatar: string
  role: 'owner' | 'admin' | 'member'
  type?: 'member' | 'add' | 'remove'
}

// 消息相关接口
export interface IMessage {
  id: string
  content: string
  type: 'text' | 'image' | 'voice' | 'video' | 'file'
  senderId: string
  senderName: string
  senderAvatar: string
  timestamp: number
  isSelf: boolean
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
}

// 聊天会话接口
export interface IChatConversation {
  id: string
  type: 'user' | 'group'
  targetId: string
  title: string
  avatar: string
  lastMessage: string
  lastMessageTime: number
  unreadCount: number
  isTop: boolean
  doNotDisturb: boolean
}
