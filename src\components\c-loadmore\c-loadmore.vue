<template>
  <view class="load_more flex-row" @click="onClick">
    <c-icon v-if="status === 'loading'" type="loading"></c-icon>
    <text class="text">{{ load_text[status] }}</text>
  </view>
</template>

<script>
export default {
  name: 'CLoadMore',
  components: {},
  props: {
    status: {
      type: String,
      default: 'loading',
      validator: (value) => {
        return ['loading', 'loadend', 'nomore', 'loaderror'].includes(value)
      },
    },
    load_text: {
      type: Object,
      default: () => ({
        loading: '加载中',
        loadend: '　',
        nomore: '没有更多了',
        loaderror: '加载失败，请点击重试',
      }),
    },
  },
  data() {
    return {}
  },
  methods: {
    onClick() {
      if (this.status === 'loaderror') {
        this.$emit('reload')
      }
    },
  },
}
</script>

<style scoped lang="scss">
.load_more {
  padding: 24rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #666;
  .text {
    font-size: 28rpx;
    margin-left: 16rpx;
  }
}
</style>
