<route type="home" lang="json">
{
  "style": {
    "navigationBarTitleText": "车源",
    "enablePullDownRefresh": true,
    "navigationStyle": "custom",
    "app-plus": {
      "pullToRefresh": {
        "support": true,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen hasBottomBar>
    <view class="bg w-full absolute h-[360rpx]">
      <image
        :src="$imgUrl('/statics/images/header_bg.png')"
        class="w-full"
        mode="aspectFill"
      ></image>
    </view>
    <view class="header sticky top-0 z-20 h-[284rpx] overflow-hidden">
      <view class="w-full h-[360rpx] absolute">
        <image
          :src="$imgUrl('/statics/images/header_bg.png')"
          class="w-full h-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col">
        <c-titlebar title="车源圈"></c-titlebar>
        <view class="px-[16rpx] py-[20rpx]">
          <c-search
            v-model="state.queryForm.kw"
            :area="state.area.cityName"
            @confirm="initData"
            @selectRegion="onSelectRegion"
          ></c-search>
        </view>
      </view>
    </view>
    <view class="content-container flex-1 bg-white relative z-1 rounded-t-[30rpx] overflow-scroll">
      <infoItem v-for="item in state.dataList" :key="item.carSourceId" :data="item"></infoItem>
      <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
    </view>
    <c-tabbar></c-tabbar>
  </c-page>
</template>

<script setup lang="ts">
// import { ref } from 'vue'
import {
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
} from '@dcloudio/uni-app'
import { getPageInfo, navigateTo } from '@/utils/util'
import { carList, type ICarlistQuery, type ICarResource } from '@/api/carResource'

const state = reactive({
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  area: {
    cityName: '',
    cityCode: '',
  },
  queryForm: {
    current: 1,
    size: 20,
  } as ICarlistQuery,
  dataList: [] as ICarResource[],
})

const getData = function () {
  state.loadStatus = 'loading'
  carList(state.queryForm)
    .then((res) => {
      console.log(res)
      if (res.code === 0) {
        if (res.data?.length) {
          state.dataList.push(...res.data)
          state.loadStatus = 'loadend'
        } else {
          state.loadStatus = 'nomore'
        }
      } else {
        state.loadStatus = 'loaderror'
      }
    })
    .catch((err) => {
      console.log(err)
      state.loadStatus = 'loaderror'
    })
    .finally(() => {
      uni.stopPullDownRefresh()
    })
  // return new Promise((reslove) => {
  //   setTimeout(() => {
  //     state.dataList.push(...dataList)
  //     state.loadStatus = 'loadend'
  //     reslove(true)
  //     uni.stopPullDownRefresh()
  //   }, 200)
  // })
}

const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}
initData()

const onSelectRegion = function (e) {
  state.area = e
  state.queryForm.cityCode = e.cityCode
  initData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}
onReachBottom(() => {
  if (state.loadStatus === 'loadend') {
    loadMore()
  }
})

onPageScroll((e) => {
  console.log(e)
})

onPullDownRefresh(() => {
  initData()
})

// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  // console.log(getPageInfo())
  return {
    title: '车城CC',
    path: `${getPageInfo().route}`,
  }
})
onShareTimeline(() => {
  // console.log(getPageInfo())
  return {
    title: '车城CC',
    path: `${getPageInfo().route}`,
  }
})
// #endif
</script>

<style lang="scss">
// .header {
//   position: sticky;
//   z-index: 9;
// }
</style>
