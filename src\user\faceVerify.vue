<route lang="json">
{
  "style": {
    "navigationBarTitleText": "人脸实人认证"
  },
  "auth": true
}
</route>
<template>
  <web-view :src="url" @message="onPageMesage"> </web-view>
</template>

<script setup lang="ts">
// import { message } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'

const url = ref('')
onLoad((options) => {
  if (options?.url) {
    url.value = decodeURIComponent(options.url)
    console.log(url.value)
  }
})
const onPageMesage = function (e) {
  console.log('web-view消息')
  console.log(e.detail)
  const data = e.detail.data[0]
  console.log(data)
  console.log(data.certifyId)
  if (data?.passed !== 'T') {
    // message.warning('人脸认证失败')
    uni.$emit('certifyRes', {
      success: false,
    })
    return
  } else {
    uni.$emit('certifyRes', {
      success: true,
    })
  }
}
</script>

<style scoped lang="scss"></style>
