<template>
  <view class="flex-row items-center h-[60rpx] px-[10rpx]" :class="props.type">
    <view
      v-for="item in props.list"
      :key="item.type"
      class="relative tab-item py-[12rpx] px-[24rpx]"
      @click="onClickTab(item)"
    >
      <view class="w-full h-full z-10">
        <text
          :class="[
            props.current === item.type
              ? props.size === 'default'
                ? 'text-title font-bold text-xl'
                : 'text-title font-bold text-3xl'
              : 'text-content text-xl',
          ]"
          >{{ item.name }}</text
        >
      </view>
      <image
        v-if="props.showBottomLine && props.current === item.type"
        :src="'/static/icons/tab_bottom_line.png'"
        class="w-[80rpx] h-[20rpx] absolute left-[40rpx] bottom-0 z-0"
        mode="widthFix"
      ></image>
    </view>
  </view>
</template>

<script setup lang="ts">
import { type PropType } from 'vue'
type Tab = {
  name: string
  type: string
}
const props = defineProps({
  list: {
    type: Array as PropType<Tab[]>,
    default: () => [
      {
        name: '车源圈',
        type: 'carSource',
      },
      {
        name: '求购圈',
        type: 'demand',
      },
      {
        name: '朋友圈',
        type: 'friend',
      },
      // {
      //   name: '生活圈',
      //   type: 'life',
      // },
    ],
  },
  current: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'justify-around',
  },
  size: {
    type: String,
    default: 'default',
  },
  showBottomLine: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits(['change'])
const onClickTab = function (tab) {
  emits('change', tab.type)
}
</script>

<style scoped lang="scss">
.tab-item text {
  transition: 0.26s;
}
</style>
