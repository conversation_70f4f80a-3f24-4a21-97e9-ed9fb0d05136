<template>
  <image :src="remoteSrc" :class="props.class" :mode="props.mode" :style="props.style"></image>
</template>

<script setup lang="ts">
import { getImageUrl } from '@/utils/util'

const props = withDefaults(
  defineProps<{
    src: string
    mode:
      | 'widthFix'
      | 'scaleToFill'
      | 'aspectFit'
      | 'aspectFill'
      | 'heightFix'
      | 'top'
      | 'bottom'
      | 'center'
      | 'left'
      | 'right'
      | 'top left'
      | 'top right'
      | 'bottom left'
      | 'bottom right'
    class: string | string[]
    style: string | Record<string, string>
  }>(),
  {
    src: '',
    mode: 'widthFix',
    class: '',
    style: '',
  }
)
const remoteSrc = computed(() => {
  return getImageUrl(props.src)
})
</script>
