<template>
  <view class="content-container flex-1 bg-white relative z-1 rounded-t-[30rpx] overflow-scroll">
    <view class="chat-list">
      <infoItem v-for="item in state.dataList" :key="item.carSourceId" :data="item"></infoItem>
    </view>
    <view
      class="add-btn w-[100rpx] h-[100rpx] justify-center items-center rounded-[50%] fixed bottom-[200rpx] right-[24rpx] z-30"
      @click="toPublish"
    >
      <c-icon type="plus" size="50" color="#ffffff"></c-icon>
    </view>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { navigateTo } from '@/utils/util'
import { carList, type ICarResource } from '@/api/carResource'
const state = reactive({
  currentTab: 'carSource',
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
  },
  dataList: [] as ICarResource[],
})

const getData = function () {
  state.loadStatus = 'loading'
  carList(state.queryForm)
    .then((res) => {
      console.log(res)
      if (res.code === 0) {
        if (res.data?.length) {
          state.dataList.push(...res.data)
          state.loadStatus = 'loadend'
        } else {
          state.loadStatus = 'nomore'
        }
      } else {
        state.loadStatus = 'loaderror'
      }
    })
    .catch((err) => {
      console.log(err)
      state.loadStatus = 'loaderror'
    })
    .finally(() => {
      uni.stopPullDownRefresh()
    })
}

// getData()

const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  if (state.loadStatus === 'loadend') {
    state.queryForm.current++
    getData()
  }
}

const toPublish = function () {
  navigateTo({
    path: `/clique/info/publish`,
  })
}

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss">
.add-btn {
  background: linear-gradient(270deg, #1262ff 0%, #0a89ff 100%);

  box-shadow: 0px 4px 10px 0px rgba(118, 118, 118, 0.3);
}
</style>
