<template>
  <view class="bottom-line flex-row items-start bg-white m-[30rpx] pb-[30rpx]" @tap="toDetail">
    <view class="avatar w-[60rpx] h-[60rpx] rounded-[30rpx] shrink-0 overflow-hidden">
      <image
        :src="$imgUrl(props.data.userAvatar || '/statics/user/default_avatar.png')"
        class="w-[100%]"
        mode="widthFix"
      ></image>
    </view>
    <view class="flex-1 ml-[20rpx] pr-[24rpx]">
      <!-- 用户信息 -->
      <view class="mb-[20rpx]">
        <view class="flex-row items-center justify-between">
          <view class="mb-[8rpx]">
            <text class="username text-[#576B95] font-medium">{{ props.data.userName }}</text>
          </view>
          <view>
            <text class="price text-[36rpx] font-bold text-highlight"
              >{{ props.data.fixedPrice }}万</text
            >
          </view>
          <!-- <view
            class="flex-row items-center w-[106rpx] h-[48rpx] justify-center rounded-[24rpx] bg-primary/10"
          >
            <c-icon type="plus" size="20" color="#0767ff"></c-icon>
            <text class="text-primary ml-[6rpx] text-[24rpx]">关注</text>
          </view> -->
        </view>
        <view class="flex-row">
          <text class="time text-[24rpx] text-secondary">{{ props.data.createTime }}</text>
          <text class="location ml-[12rpx] text-[24rpx] text-secondary">{{
            props.data.cityName
          }}</text>
        </view>
      </view>
      <!-- 内容 -->
      <view class="mb-[20rpx]">
        <view class="overflow-hidden" :class="[props.data.isOpen ? 'h-auto' : 'max-h-[160rpx]']">
          <text class="text-[28rpx] leading-[40rpx] descp">{{ props.data.vehicleCondition }}</text>
        </view>
        <view @click.stop="props.data.isOpen = !props.data.isOpen" class="w-[80rpx]">
          <text class="text-[#576B95]">{{ props.data.isOpen ? '收起' : '全文' }}</text>
        </view>
      </view>
      <!-- 图片 -->
      <view>
        <view
          v-if="
            props.data.appCarSourceImageVoList && props.data.appCarSourceImageVoList.length === 1
          "
        >
          <image
            :src="props.data.appCarSourceImageVoList?.[0]?.vehicleImageUrl"
            lazy-load
            fade-show
            class="max-w-[576rpx] max-h-[340rpx] rounded-[20rpx]"
            mode="aspectFill"
            @tap.stop="handlePreviewImage(props.data.appCarSourceImageVoList?.[0]?.vehicleImageUrl)"
          ></image>
        </view>
        <view v-else class="flex-row gap-[10rpx] flex-wrap">
          <view
            v-for="(item, index) in sliceImagelist"
            :key="index"
            class="w-[186rpx] h-[160rpx] relative rounded-[20rpx] overflow-hidden"
          >
            <image
              :src="item.vehicleImageUrl"
              lazy-load
              fade-show
              class="w-full h-full"
              mode="aspectFill"
              @tap.stop="handlePreviewImage(item.vehicleImageUrl)"
            >
            </image>
            <view
              v-if="index === props.imgMaxCount - 1"
              class="w-full h-full absolute z-10 items-center justify-center bg-black/50"
            >
              <text class="text-white"
                >+{{ (props.data.appCarSourceImageVoList?.length || 0) - props.imgMaxCount }}</text
              >
            </view>
          </view>
        </view>
      </view>
      <!-- 脚部 -->
      <view class="footer mt-[24rpx] flex-row items-center justify-between">
        <!-- <view>
          <text class="price text-[36rpx] font-bold text-highlight">{{ props.data.price }}万</text>
        </view> -->
        <!-- <view class="flex-row"> -->
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon type="chat" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">咨询</text>
        </view>
        <view
          class="flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="registerIntercept({ ok: handleMakePhoneCall }, props.data.mobile)"
        >
          <c-icon type="phone" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">电话</text>
        </view>
        <!-- #ifdef MP -->
        <button
          open-type="share"
          class="viewbutton flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="() => false"
        >
          <c-icon type="share" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">分享</text>
        </button>
        <!-- #endif -->
        <!-- #ifndef MP -->
        <view
          class="viewbotton flex flex-row items-center p-[8rpx] mr-[8rpx]"
          @click.stop="handleShare()"
        >
          <c-icon type="share" size="30" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">分享</text>
        </view>
        <!-- #endif -->
        <view class="flex-row items-center p-[8rpx] mr-[8rpx]">
          <c-icon
            v-if="props.data.collectStatus"
            type="shoucangchenggong"
            size="32"
            color="#FCBD54"
          ></c-icon>
          <c-icon v-else type="shoucang4" size="32" color="#5F6678"></c-icon>
          <text class="ml-[6rpx] text-tip text-[28rpx]">收藏</text>
        </view>
        <!-- </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { ICarResourceItem } from '@/api/carResource'
import { execShare, getPageInfo, makePhoneCall, navigateTo, previewImage } from '@/utils/util'
import { registerIntercept } from '@/utils/auth'
import { type PropType } from 'vue'
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
const props = defineProps({
  data: {
    type: Object as PropType<ICarResourceItem>,
    default: () => ({}),
  },
  imgMaxCount: {
    type: Number,
    default: 3,
  },
})

const sliceImagelist = computed(() => {
  return props.data.appCarSourceImageVoList?.slice(0, props.imgMaxCount) || []
})

const handlePreviewImage = function (url: string) {
  previewImage(url, props.data.appCarSourceImageVoList?.map((item) => item.vehicleImageUrl) || [])
}
const handleMakePhoneCall = function (mobile: string) {
  makePhoneCall({
    phoneNumber: mobile,
  })
}
const toDetail = function () {
  navigateTo({
    path: `/clique/info/infoDetail?id=${props.data.carSourceId}`,
  })
  uni.$once('getInfoDetailData', function (fn) {
    fn(props.data)
  })
  // registerIntercept({
  //   ok: () => {
  //     navigateTo({
  //       path: `/clique/infoDetail?id=${props.data.id}`,
  //     })
  //   },
  // })
}

// #ifdef APP
const handleShare = function (scene: 'WXSceneSession' | 'WXSceneTimeline' = 'WXSceneSession') {
  execShare({
    type: 0,
    title: props.data.vehicleModel,
    scene,
    imageUrl: props.data.appCarSourceImageVoList?.[0]?.vehicleImageUrl,
    miniProgram: {
      id: import.meta.env.VITE_WX_MINIPROGRAM_ID,
      path: `${getPageInfo().route}?id=${props.data.carSourceId}`,
    },
  })
}
// #endif
</script>
<!-- <style lang="scss" scoped>
.descp {
  word-wrap: break-word;
  white-space: pre-wrap;
}
</style> -->
