// import { resolve } from 'node:path'
import process from 'node:process'
import { ConfigEnv, defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import postcssRemToResponsivePixel from 'postcss-rem-to-responsive-pixel'
import postcssWeappTailwindcssRename from 'weapp-tailwindcss-webpack-plugin/postcss'
import vwt from 'weapp-tailwindcss-webpack-plugin/vite'
import uniPages from '@uni-helper/vite-plugin-uni-pages'
import AutoImport from 'unplugin-auto-import/vite'

const isH5 = process.env.UNI_PLATFORM === 'h5'
const isApp = process.env.UNI_PLATFORM === 'app'
const weappTailwindcssDisabled = isH5 || isApp

const postcssPlugin = [autoprefixer(), tailwindcss()]
if (!weappTailwindcssDisabled) {
  postcssPlugin.push(
    postcssRemToResponsivePixel({
      rootValue: 32,
      propList: ['*'],
      transformUnit: 'rpx',
    })
  )
  postcssPlugin.push(postcssWeappTailwindcssRename())
}

const viteConfig = defineConfig((mode: ConfigEnv) => {
  const env = loadEnv(mode.mode, process.cwd())
  return {
    plugins: [
      uniPages({
        dir: 'src/pages',
        subPackages: ['src/clique', 'src/user'],
        exclude: ['**/components/**/*.*', '**/static/**/*.*'],
        outDir: 'src',
        homePage: '/index/index',
      }),
      uni(),
      AutoImport({
        exclude: [/\/hybrid\/html\/statics\/js\/*.js/, /\/hybrid\/html\/*.html/],
        // include: [
        //   /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        //   /\.vue$/,
        //   /\.vue\?vue/, // .vue
        //   /\.md$/, // .md
        // ],
        imports: ['vue', 'pinia', 'vue-i18n'],
        //为true时在项目根目录自动创建
        dts: 'types/auto-imports.d.ts',
        // eslint报错解决
        eslintrc: {
          enabled: true, // Default `false`
          filepath: 'eslintrc-auto-import.json', // 在根目录生成
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
      }),
      weappTailwindcssDisabled ? undefined : vwt(),
    ],
    css: {
      postcss: {
        plugins: postcssPlugin,
      },
    },
    server: {
      port: 8088,
      hmr: true, // 启用热更新
      proxy: {
        '/api': {
          target: env.VITE_APP_BASE_URL, // 目标服务器地址
          changeOrigin: true, // 是否修改请求头中的 Origin 字段
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        '/app': {
          target: env.VITE_APP_BASE_URL, // 目标服务器地址
          changeOrigin: true, // 是否修改请求头中的 Origin 字段
        },
        '/mobile/api': {
          target: env.VITE_APP_BASE_URL, // 目标服务器地址
          changeOrigin: true, // 是否修改请求头中的 Origin 字段
          rewrite: (path) => path.replace(/^\/mobile\/api/, ''),
        },
      },
    },
  }
})

// https://vitejs.dev/config/
export default viteConfig
