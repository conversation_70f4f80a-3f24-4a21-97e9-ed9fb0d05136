/**
 * 用户通知管理API
 */
import { request } from '@/utils/request'

export interface UserNotification {
  id: string
  userId: string
  type: 'system' | 'interaction' | 'order' | 'refund' | 'follow' | 'like' | 'comment' | 'message'
  title: string
  content: string
  data?: any // 额外数据，如订单ID、用户ID等
  isRead: boolean
  readTime?: string
  createTime: string
  expireTime?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  category?: string
  sourceId?: string // 来源ID，如订单ID、评论ID等
  sourceType?: string // 来源类型
  actionUrl?: string // 点击跳转链接
  imageUrl?: string // 通知图片
}

export interface NotificationResponse {
  code: number
  message: string
  data: UserNotification
}

export interface NotificationListResponse {
  code: number
  message: string
  data: UserNotification[]
  total?: number
}

export interface NotificationPageParams {
  current?: number
  size?: number
  userId?: string
  type?: string
  isRead?: boolean
  category?: string
  startTime?: string
  endTime?: string
}

export interface NotificationStats {
  total: number
  unread: number
  system: number
  interaction: number
  order: number
  todayCount: number
  weekCount: number
}

/**
 * 修改用户系统、互动通知
 */
export const updateUserNotification = (id: string, data: Partial<UserNotification>) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification',
    method: 'PUT',
    data: {
      id,
      ...data,
    },
  })
}

/**
 * 新增用户系统、互动通知
 */
export const createUserNotification = (data: {
  userId: string
  type: string
  title: string
  content: string
  data?: any
  priority?: string
  category?: string
  sourceId?: string
  sourceType?: string
  actionUrl?: string
  imageUrl?: string
  expireTime?: string
}) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification',
    method: 'POST',
    data,
  })
}

/**
 * 通过id删除用户系统、互动通知
 */
export const deleteUserNotification = (id: string) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification',
    method: 'DELETE',
    data: {
      id,
    },
  })
}

/**
 * 批量设置已读用户已读通知
 */
export const markNotificationsAsRead = (notificationIds: string[]) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/read',
    method: 'POST',
    data: {
      notificationIds,
    },
  })
}

/**
 * 设置一键已读
 */
export const markAllNotificationsAsRead = (userId: string, type?: string) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/readAll',
    method: 'POST',
    data: {
      userId,
      type,
    },
  })
}

/**
 * 批量导入通知
 */
export const importNotifications = (notifications: Array<Partial<UserNotification>>) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/import',
    method: 'POST',
    data: {
      notifications,
    },
  })
}

/**
 * 分页查询通知
 */
export const getNotificationsPage = (params: NotificationPageParams = {}) => {
  return request<NotificationListResponse>({
    url: '/userNotification/page',
    method: 'GET',
    params: {
      current: params.current || 1,
      size: params.size || 20,
      userId: params.userId,
      type: params.type,
      isRead: params.isRead,
      category: params.category,
      startTime: params.startTime,
      endTime: params.endTime,
    },
  })
}

/**
 * 获取当前用户未读通知数量
 */
export const getUserNotificationCount = (userId: string) => {
  return request<{
    code: number
    message: string
    data: NotificationStats
  }>({
    url: '/userNotification/getUserNotificationNum',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 通过id查询通知详情
 */
export const getNotificationInfo = (id: string) => {
  return request<NotificationResponse>({
    url: '/userNotification/getInfo',
    method: 'GET',
    params: {
      id,
    },
  })
}

/**
 * 导出通知列表
 */
export const exportNotifications = (params?: NotificationPageParams) => {
  return request<Blob>({
    url: '/userNotification/export',
    method: 'GET',
    params,
    responseType: 'blob',
  })
}

/**
 * 通过条件查询通知详情
 */
export const getNotificationDetails = (params: {
  userId?: string
  type?: string
  sourceId?: string
  sourceType?: string
}) => {
  return request<NotificationListResponse>({
    url: '/userNotification/details',
    method: 'GET',
    params,
  })
}

/**
 * 获取用户通知设置
 */
export const getUserNotificationSettings = (userId: string) => {
  return request<{
    code: number
    message: string
    data: {
      systemNotification: boolean
      interactionNotification: boolean
      orderNotification: boolean
      emailNotification: boolean
      smsNotification: boolean
      pushNotification: boolean
      doNotDisturbStart?: string
      doNotDisturbEnd?: string
    }
  }>({
    url: '/userNotification/settings',
    method: 'GET',
    params: {
      userId,
    },
  })
}

/**
 * 更新用户通知设置
 */
export const updateUserNotificationSettings = (userId: string, settings: {
  systemNotification?: boolean
  interactionNotification?: boolean
  orderNotification?: boolean
  emailNotification?: boolean
  smsNotification?: boolean
  pushNotification?: boolean
  doNotDisturbStart?: string
  doNotDisturbEnd?: string
}) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/settings',
    method: 'PUT',
    data: {
      userId,
      ...settings,
    },
  })
}

/**
 * 批量发送通知
 */
export const batchSendNotifications = (data: {
  userIds: string[]
  type: string
  title: string
  content: string
  data?: any
  priority?: string
  category?: string
  actionUrl?: string
  imageUrl?: string
  expireTime?: string
}) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/batch-send',
    method: 'POST',
    data,
  })
}

/**
 * 发送系统广播通知
 */
export const sendBroadcastNotification = (data: {
  type: string
  title: string
  content: string
  data?: any
  priority?: string
  category?: string
  actionUrl?: string
  imageUrl?: string
  expireTime?: string
  targetUserIds?: string[] // 指定用户，为空则发送给所有用户
}) => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/broadcast',
    method: 'POST',
    data,
  })
}

/**
 * 清理过期通知
 */
export const cleanExpiredNotifications = () => {
  return request<{ code: number; message: string }>({
    url: '/userNotification/clean-expired',
    method: 'POST',
  })
}

/**
 * 获取通知统计信息
 */
export const getNotificationStatistics = (params?: {
  startTime?: string
  endTime?: string
  type?: string
}) => {
  return request<{
    code: number
    message: string
    data: {
      totalCount: number
      readCount: number
      unreadCount: number
      typeStats: Array<{ type: string; count: number }>
      dailyStats: Array<{ date: string; count: number }>
    }
  }>({
    url: '/userNotification/statistics',
    method: 'GET',
    params,
  })
}
