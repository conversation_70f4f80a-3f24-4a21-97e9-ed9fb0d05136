<template>
  <view class="content-container flex-1 bg-white relative z-1 rounded-t-[30rpx] overflow-scroll">
    <view class="chat-list">
      <demandItem v-for="item in state.dataList" :key="item.id" :data="item"></demandItem>
    </view>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import type { ICarSource } from 'types/carSource.interface'
import demandItem from './demandItem.vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
const state = reactive({
  currentTab: 'carSource',
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
  },
  dataList: [] as ICarSource[],
})
const dataList: ICarSource[] = [
  {
    id: '12121212',
    username: '韦美欣',
    avatar: '/statics/user/default_avatar.png',
    // description: '福特车源服务交流群',
    title: '',
    description:
      '福特 探险者(进口)2017款\n 几手 实表多少？\n  福特 探险者(进口)2017款\n 几手 \n实表多少？',
    time: '2025-07-28 19:32:01',
    isSubscribe: false,
    isFollow: false,
    phone: '15866226825',
    location: '枣庄',
    price: '16.36',
    imageList: [
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car1.jpg',
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car2.jpg',
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car2.jpg',
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car2.jpg',
    ],
  },
  {
    id: '1212121111',
    title: '',
    username: '韦美欣',
    avatar: '/statics/user/default_avatar.png',
    // description: '福特车源服务交流群',
    description: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    isSubscribe: false,
    isFollow: true,
    phone: '15866226825',
    location: '枣庄',
    price: '16.36',
    imageList: ['https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car1.jpg'],
  },
  {
    id: '1212121111',
    title: '',
    username: '韦美欣',
    avatar: '/statics/user/default_avatar.png',
    // description: '福特车源服务交流群',
    description: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    isSubscribe: false,
    isFollow: false,
    phone: '15866226825',
    location: '枣庄',
    price: '16.36',
    imageList: ['https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car1.jpg'],
  },
  {
    id: '1212121111',
    title: '',
    username: '韦美欣',
    avatar: '/statics/user/default_avatar.png',
    // description: '福特车源服务交流群',
    description: '福特 探险者(进口)2017款 几手 实表多少？',
    time: '2025-07-28 19:32:01',
    isSubscribe: false,
    isFollow: false,
    phone: '15866226825',
    location: '枣庄',
    price: '16.36',
    imageList: ['https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car1.jpg'],
  },
]

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((reslove) => {
    setTimeout(() => {
      const _dataList = cloneDeep(dataList)
      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      reslove(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}
// getData()
const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}
defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss"></style>
