<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择省",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page>
    <view>
      <c-list-item title="全国" @click="handleClear()"></c-list-item>
      <c-list-item
        v-for="item in dataList"
        :key="item.adcode"
        :title="item.name"
        @click="toCity(item)"
      ></c-list-item>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { getAreaData, type IArea } from '@/api/user'
import { navigateTo } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const dataList = ref<IArea[]>([])

const getProviceList = function () {
  getAreaData('1').then((res) => {
    console.log(res)
    if (res.data) {
      dataList.value = res.data
    }
  })
}
getProviceList()
let selectLevel = ''
onLoad((options) => {
  if (options?.level) {
    selectLevel = options.level
  }
})

const toCity = function (e) {
  navigateTo(
    `/user/cityList?provinceCode=${e.adcode}&provinceName=${e.name}&selectLevel=${selectLevel}`
  )
}

const handleClear = function () {
  uni.$emit('selectRegion', {})
  uni.navigateBack({
    delta: 1,
  })
}
</script>

<style scoped lang="scss"></style>
