<template>
  <view class="form-item" :class="{ flex: labelPosition === 'left' }">
    <view class="label text-title" :style="{ width: labelWidth || parentLabelWidth }">
      <text>{{ label }}</text>
      <text v-if="rule.required || required" class="start color-danger">*</text>
    </view>
    <view class="content">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CFormItem',
  components: {},
  data() {
    return {
      parentLabelWidth: '', //父级cForm设置的label宽度
      rule: {},
    }
  },
  props: {
    label: {
      //label的文本
      type: String,
      default: '',
    },
    prop: {
      //表单字段
      type: String,
      default: '',
    },
    labelWidth: {
      //label的宽度
      type: String,
      default: '',
    },
    labelPosition: {
      type: String,
      default: 'left', //label位置 支持left和top
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    const cForm = this.getForm(this.$parent)
    if (cForm?.labelWidth) {
      this.parentLabelWidth = cForm.labelWidth
    }
    if (cForm?.rules && this.prop) {
      if (cForm.rules[this.prop] && cForm.rules[this.prop]) {
        this.rule = cForm.rules[this.prop]
      }
    }
  },
  methods: {
    // 获取父级中的cForm组件
    getForm(parent) {
      const parentInfo = this.getParentInfo(parent)
      if (parentInfo.$options?.name === 'CForm') {
        return parentInfo
      } else if (parentInfo.$parent) {
        return this.getForm(parentInfo.$parent)
      } else {
        return undefined
      }
    },
    getParentInfo(parent) {
      return parent
    },
  },
}
</script>

<style scoped lang="scss">
.form-item {
  width: 100%;
  min-width: 300rpx;
  // margin: 0 24rpx;
  flex: 1;
  // padding: 0 18rpx;
  position: relative;
  background-color: #ffffff;
  &.flex {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    align-items: center;
  }
  // &:not(:last-child) {
  &:after {
    content: '';
    height: 1rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #f3f3f3;
  }
  // }
  .label {
    flex-direction: row;
    // justify-content: space-between;
    flex-shrink: 0;
    width: 120rpx;
    margin-right: 12rpx;
    line-height: 1;
    padding: 30rpx 0;
    font-size: 32rpx;
    // display: flex;
    // align-items: center;
    .start {
      // line-height: 1;
      position: relative;
      top: 6rpx;
      margin-left: 8rpx;
    }
  }
  .color-danger {
    color: $color-danger;
  }
  .content {
    flex: 1;
    width: 100%;
    // display: flex;
    font-size: 30rpx;
    line-height: 56rpx;
    overflow: hidden;
    .warning {
      font-size: 24rpx;
      color: $color-danger;
    }
    :deep() {
      input {
        font-size: 30rpx;
      }
    }
  }
  .placeholder {
    color: #8a929f;
  }
}
</style>
