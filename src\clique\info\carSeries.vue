<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择系列",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page fixedHeight>
    <c-list-index :dataList="state.dataList" ref="listIndexRef" :showLatter="false">
      <template #defalut="{ data }">
        <view
          class="flex-row items-center py-[24rpx] px-xs bottom-line"
          v-for="(item, index) in data"
          :key="index"
          @click="toCarType(item)"
        >
          <!-- <image :src="item.seriesImg" mode="widthFix" class="mr-mn w-[80rpx] h-[80rpx]"></image> -->
          <text :class="['text-xl', state.current === item.brandId && 'text-primary']">{{
            item.seriesName
          }}</text>
        </view>
      </template>
    </c-list-index>
  </c-page>
</template>

<script setup lang="ts">
import { carSeriesList, type ISeries } from '@/api/carResource'
import { navigateTo } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const state = reactive({
  multiple: false,
  dataList: [] as any[],
  current: '',
  currentList: [],
})
const listIndexRef = ref()
const getList = function (brandId: string) {
  carSeriesList(brandId).then((res) => {
    if (res.data) {
      for (let key in res.data) {
        state.dataList.push({
          letter: key,
          data: res.data[key],
        })
        // if (key === 'C') {
        //   break
        // }
      }
      nextTick(() => {
        listIndexRef.value.computeNodeInfo()
      })
    }
  })
}
let query: Record<string, any> = {}
onLoad((options) => {
  console.log(options)
  if (options?.brandId) {
    getList(options.brandId)
  }
  if (options) {
    query = options
  }
})

const toCarType = function (e) {
  state.current = e.seriesId
  console.log(query)
  if (query.level === '2') {
    uni.$emit('selectSeries', { ...query, seriesId: e.seriesId, seriesName: e.seriesName })
    uni.navigateBack({
      delta: 2,
    })
    return
  }
  navigateTo(
    `/clique/info/carType?brandId=${query.brandId}&brandName=${query.brandName}&seriesId=${e.seriesId}&seriesName=${e.seriesName}`
  )
}
</script>

<style scoped lang="scss"></style>
