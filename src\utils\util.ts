import { isObject } from '@vue/shared'
import * as CryptoJS from 'crypto-js'

/**
 * @description 获取元素节点信息（在组件中的元素必须要传ctx）
 * @param  { String } selector 选择器 '.app' | '#app'
 * @param  { Bo<PERSON>an } all 是否多选
 * @param  { ctx } context 当前组件实例
 */
export const getRect = (selector: string, all = false, context?: any) => {
  return new Promise((resolve, reject) => {
    let query = uni.createSelectorQuery()
    if (context) {
      query = uni.createSelectorQuery().in(context)
    }
    query[all ? 'selectAll' : 'select'](selector)
      .boundingClientRect(function (rect) {
        if (all && Array.isArray(rect) && rect.length) {
          return resolve(rect)
        }
        if (!all && rect) {
          return resolve(rect)
        }
        reject('找不到元素')
      })
      .exec()
  })
}

/**
 * @description 获取当前页面实例
 */
export function currentPage() {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  return currentPage || {}
}

/**
 * @description 后台选择链接专用跳转
 */
interface Link {
  path: string
  isTab?: boolean
  query?: Record<string, any>
  needReg?: boolean
}

export enum LinkTypeEnum {
  'SHOP_PAGES' = 'shop',
  'CUSTOM_LINK' = 'custom',
}

export function navigateTo(
  link: Link | string,
  options: UniApp.NavigateToSuccessOptions = {} as UniApp.NavigateToSuccessOptions,
  navigateType: 'navigateTo' | 'reLaunch' | 'switchTab' = 'navigateTo'
) {
  let url = ''
  let needReg = false
  if (typeof link === 'object') {
    url = link.query ? `${link.path}?${objectToQuery(link.query)}` : link.path
    needReg = link.needReg || false
  } else {
    url = link
  }
  switch (navigateType) {
    case 'navigateTo':
      uni.navigateTo({ url, ...options, needReg: needReg })
      break
    case 'reLaunch':
      uni.reLaunch({ url, animationDuration: 200, ...options })
      break
    case 'switchTab':
      uni.switchTab({ url, animationDuration: 200, ...options })
  }
}

/**
 * @description 是否为空
 * @param {unknown} value
 * @return {Boolean}
 */
export const isEmpty = (value: unknown) => {
  return value == null && typeof value == 'undefined'
}

/**
 * @description 对象格式化为Query语法
 * @param { Object } params
 * @return {string} Query语法
 */
export function objectToQuery(params: Record<string, any>): string {
  let query = ''
  for (const props of Object.keys(params)) {
    const value = params[props]
    const part = encodeURIComponent(props) + '='
    if (!isEmpty(value)) {
      console.log(encodeURIComponent(props), isObject(value))
      if (isObject(value)) {
        for (const key of Object.keys(value)) {
          if (!isEmpty(value[key])) {
            const params = props + '[' + key + ']'
            const subPart = encodeURIComponent(params) + '='
            query += subPart + encodeURIComponent(value[key]) + '&'
          }
        }
      } else {
        query += part + encodeURIComponent(value) + '&'
      }
    }
  }
  return query.slice(0, -1)
}

/**
 * @description 格式化输出价格
 * @param  { string } price 价格
 * @param  { string } take 小数点操作
 * @param  { string } prec 小数位补
 */
export function formatPrice({ price, take = 'all', prec = undefined }: any) {
  let [integer, decimals = ''] = (price + '').split('.')

  // 小数位补
  if (prec !== undefined) {
    const LEN = decimals.length
    for (let i = prec - LEN; i > 0; --i) decimals += '0'
    decimals = decimals.substr(0, prec)
  }

  switch (take) {
    case 'int':
      return integer
    case 'dec':
      return decimals
    case 'all':
      return integer + '.' + decimals
  }
}

/**
 * @description 组合异步任务
 * @param  { string } task 异步任务
 */

export function series(...task: Array<(_arg: any) => any>) {
  return function (): Promise<any> {
    return new Promise((resolve, reject) => {
      const iteratorTask = task.values()
      const next = (res?: any) => {
        const nextTask = iteratorTask.next()
        if (nextTask.done) {
          resolve(res)
        } else {
          Promise.resolve(nextTask.value(res)).then(next).catch(reject)
        }
      }
      next()
    })
  }
}

/**
 * @description 添加单位
 * @param {String | Number} value 值 100
 * @param {String} unit 单位 px em rem
 */
export const addUnit = (value: string | number, unit = 'rpx') => {
  return !Object.is(Number(value), NaN) ? `${value}${unit}` : value
}

/**
 * 自动适配不同的后端架构
 * 1. 例如 /act/oa/task ,在微服务架构保持不变,在单体架构编程 /admin/oa/task
 * 2. 特殊 /gen/xxx ,在微服务架构、单体架构编程 都需保持不变
 *
 * @param originUrl 原始路径
 */
export const adaptationUrl = (originUrl?: string) => {
  // 微服务架构 不做路径转换,为空不做路径转换
  const isMicro = import.meta.env.VITE_IS_MICRO
  if (isEmpty(isMicro) || isMicro === 'true') {
    return originUrl
  }

  // 验证码服务
  if (originUrl?.startsWith('/code/')) {
    return `/admin${originUrl}`
  }

  // 如果是代码生成服务，不做路径转换
  if (originUrl?.startsWith('/gen')) {
    return originUrl
  }
  // 转为 /admin 路由前缀的请求
  return `/admin/${originUrl?.split('/').splice(2).join('/')}`
}

/**
 *加密处理
 */
export function encryption(src: string, keyWord: string) {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  // 加密
  const encrypted = CryptoJS.AES.encrypt(src, key, {
    iv: key,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.NoPadding,
  })
  return encrypted.toString()
}

/**
 *加密处理
 */
export function encryptionBase64(rawStr: string) {
  const wordArray = CryptoJS.enc.Utf8.parse(rawStr)
  return CryptoJS.enc.Base64.stringify(wordArray)
}

export const message = {
  success: (options: string | UniNamespace.ShowToastOptions) => {
    if (typeof options === 'string') {
      uni.showToast({
        title: options,
        duration: 2000,
        icon: 'success',
        mask: true,
      })
    } else {
      uni.showToast({ icon: 'success', mask: true, ...options })
    }
  },
  warning: (options: string | UniNamespace.ShowToastOptions) => {
    if (typeof options === 'string') {
      uni.showToast({
        title: options,
        duration: 2000,
        icon: 'none',
        mask: false,
      })
    } else {
      uni.showToast({ icon: 'none', mask: false, ...options })
    }
  },
  error: (options: string | UniNamespace.ShowToastOptions) => {
    if (typeof options === 'string') {
      uni.showToast({
        title: options,
        duration: 2000,
        icon: 'none',
        mask: true,
      })
    } else {
      uni.showToast({ icon: 'none', mask: true, ...options })
    }
  },
  loading: (title: string) => {
    uni.showLoading({
      title,
      mask: true,
    })
  },
  hideLoading: () => {
    uni.hideLoading()
  },
  tip(content: string, title = '提示') {
    return new Promise((resolve) => {
      uni.showModal({
        title: title,
        content,
        showCancel: false,
        success: () => {
          resolve(true)
        },
      })
    })
  },
}

export const showModalSync = function (options: UniNamespace.ShowModalOptions) {
  let isApp = true
  // #ifndef APP-PLUS
  isApp = false
  // #endif
  if (isApp) {
    return new Promise((resolve, reject) => {
      const buttons: string[] = []
      buttons.push(options.confirmText || '确定')
      if (options.showCancel !== false) {
        buttons.push(options.cancelText || '取消')
      }
      plus.nativeUI.confirm(
        options.content || '',
        function (e) {
          console.log(e)
          if (e.index === 0) {
            resolve(true)
          } else {
            reject('用户取消')
          }
        },
        {
          title: options.title || '提示',
          buttons,
        }
      )
    })
  } else {
    return new Promise((resolve, reject) => {
      uni.showModal({
        title: '提示',
        cancelText: '取消',
        confirmText: '确定',
        ...options,
        success: (e) => {
          if (e.confirm) {
            resolve(true)
          }
          if (e.cancel) {
            reject('用户取消')
          }
        },
        fail: (err) => {
          reject(err)
        },
      })
    })
  }
}

export const getProvider = function (
  service: 'oauth' | 'share' | 'payment' | 'push'
): Promise<UniApp.GetProviderRes['provider']> {
  return new Promise((resolve, reject) => {
    uni.getProvider({
      service,
      success: (res) => {
        if (res.provider && res.provider.length > 0) {
          resolve(res.provider)
        } else {
          reject(new Error('没有可用的服务提供商'))
        }
      },
      fail: (err) => {
        reject(err)
      },
    })
  })
}

export const oauthLogin = function (
  provider: UniApp.LoginOptions['provider']
): Promise<UniApp.LoginRes> {
  return new Promise((resolve, reject) => {
    uni.login({
      provider,
      onlyAuthorize: false,
      success: (res) => {
        console.log(res)
        if (res.code) {
          resolve(res)
        } else {
          reject(new Error('授权登录失败'))
        }
      },
      fail: (err) => {
        console.log(err)
        reject(err)
      },
    })
  })
}
export const getUserInfo = function (
  provider: UniApp.GetUserInfoOptions['provider']
): Promise<UniApp.GetUserInfoRes> {
  return new Promise((resolve, reject) => {
    uni.getUserInfo({
      provider,
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        console.log(err)
        reject(err)
      },
    })
  })
}

export const toLogin = function () {
  // #ifdef MP
  uni.navigateTo({
    url: '/user/authLogin',
  })
  // #endif
  // #ifndef MP-WEIXIN
  uni.navigateTo({
    url: '/user/accountLogin',
  })
  // #endif
}

export const redirectToLogin = function () {
  // #ifdef MP
  uni.redirectTo({
    url: '/user/authLogin',
  })
  // #endif
  // #ifndef MP-WEIXIN
  uni.redirectTo({
    url: '/user/accountLogin',
  })
  // #endif
}

export const getPageInfo = function (index: number = -1) {
  const pageList = getCurrentPages()
  if (index < 0 || index >= pageList.length) {
    return pageList[pageList.length - 1]
  }
  return pageList[index]
}

export const getImageUrl = function (url?: string) {
  const { VITE_APP_IMG_URL } = import.meta.env
  if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
    return url
  }
  // Otherwise, append VITE_APP_BASE_URL to the url
  // return VITE_IS_H5 === 'true' ? `/api${url}` : `${VITE_APP_IMG_URL}${url}`
  return `${VITE_APP_IMG_URL}${url}`
}

export const previewImage = function (current: string, urls: string[]) {
  const remoteCurrent = getImageUrl(current)
  const remoteUrls = urls.map((url) => getImageUrl(url))
  uni.previewImage({
    urls: remoteUrls,
    indicator: 'number',
    current: remoteCurrent,
  })
}

export const platform = function () {
  const system = uni.getSystemInfoSync()
  return system.platform
}

export const makePhoneCall = function (options: UniNamespace.MakePhoneCallOptions) {
  // #ifdef APP-PLUS
  const system = uni.getSystemInfoSync()
  if (system.platform === 'android') {
    // 导入Activity、Intent类
    try {
      var Intent = plus.android.importClass('android.content.Intent') as any
      var Uri = plus.android.importClass('android.net.Uri') as any
      // 获取主Activity对象的实例
      var main = plus.android.runtimeMainActivity() as any
      // 创建Intent
      var uri = Uri.parse(`tel:${options.phoneNumber}`) // 这里可修改电话号码
      var call = new Intent('android.intent.action.CALL', uri)
      // 调用startActivity方法拨打电话
      main.startActivity(call)
    } catch (error) {
      uni.makePhoneCall(options)
    }
  } else {
    uni.makePhoneCall(options)
  }
  // #endif
  // #ifndef APP-PLUS
  uni.makePhoneCall(options)
  // #endif
}

export const execShare = function (options: UniNamespace.ShareOptions) {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 5,
    fail(err) {
      console.log(err)
    },
    ...options,
  })
}
