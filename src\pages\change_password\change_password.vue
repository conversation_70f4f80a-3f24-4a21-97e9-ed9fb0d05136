<route lang="json">
{
  "style": {
    "navigationBarTitleText": "修改密码"
  }
}
</route>
<template>
  <view
    class="register bg-white min-h-full flex flex-col items-center px-[40rpx] pt-[100rpx] box-border"
  >
    <view class="w-full">
      <view class="text-2xl font-medium mb-[60rpx]">
        {{ type == 'set' ? '设置登录密码' : '修改登录密码' }}
      </view>
      <u-form borderBottom :label-width="150">
        <u-form-item label="原密码" borderBottom v-if="type != 'set'">
          <u-input
            class="flex-1"
            type="password"
            v-model="formData.oldPassword"
            :border="false"
            placeholder="请输入原来的密码"
          />
        </u-form-item>
        <u-form-item label="新密码" borderBottom>
          <u-input
            class="flex-1"
            type="password"
            v-model="formData.password"
            placeholder="6-20位数字+字母或符号组合"
            :border="false"
            @input="checkPasswordStrength"
            @focus="passwordFocused = true"
            @blur="passwordFocused = false"
          />
        </u-form-item>
        <view v-if="passwordFocused" class="mt-2 password-strength">
          <view class="strength-bar">
            <view
              :class="['strength-level', strengthClass]"
              :style="{ width: strengthWidth }"
            ></view>
          </view>
          <text class="strength-text" :class="strengthClass">{{ strengthText }}</text>
        </view>
        <u-form-item label="确认密码" borderBottom>
          <u-input
            class="flex-1"
            type="password"
            v-model="formData.newpassword1"
            placeholder="再次输入新密码"
            :border="false"
          />
        </u-form-item>
      </u-form>
      <view class="mt-[100rpx]">
        <u-button type="primary" shape="circle" @click="handleConfirm"> 确定 </u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { userChangePwd } from '@/api/user'
import { onLoad } from '@dcloudio/uni-app'
import { reactive, ref, computed } from 'vue'

const type = ref('')
const formData = reactive<any>({
  oldPassword: '',
  password: '',
  newpassword1: '',
})

const passwordFocused = ref(false)
const strengthLevel = ref(0)

const strengthText = computed(() => {
  switch (strengthLevel.value) {
    case 1:
      return '简单'
    case 2:
      return '中等'
    case 3:
      return '复杂'
    default:
      return '弱'
  }
})

const strengthClass = computed(() => {
  switch (strengthLevel.value) {
    case 1:
      return 'strength-weak'
    case 2:
      return 'strength-medium'
    case 3:
      return 'strength-strong'
    default:
      return ''
  }
})

const strengthWidth = computed(() => `${strengthLevel.value * 33.33}%`)

const checkPasswordStrength = () => {
  const password = formData.password
  let strength = 0

  if (password.length >= 8) strength++
  if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  strengthLevel.value = strength
}

const handleConfirm = async () => {
  if (!formData.oldPassword && type.value != 'set') return uni.$u.toast('请输入原来的密码')
  if (!formData.password) return uni.$u.toast('请输入密码')
  if (strengthLevel.value < 2) return uni.$u.toast('密码强度不够，请设置更复杂的密码')
  if (!formData.newpassword1) return uni.$u.toast('请输入确认密码')
  if (formData.password != formData.newpassword1) return uni.$u.toast('两次输入的密码不一致')
  const { msg, code } = await userChangePwd(formData)

  if (code === 1) {
    uni.$u.toast(msg)
  } else {
    uni.$u.toast('操作成功')
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  }
}

onLoad((options: Record<string, string>) => {
  type.value = options.type || ''
  if (type.value == 'set') {
    uni.setNavigationBarTitle({
      title: '设置登录密码',
    })
  }
})
</script>

<style lang="scss">
page {
  height: 100%;
}

.password-strength {
  margin-top: 8rpx;
  transition: opacity 0.3s ease;

  .strength-bar {
    height: 4rpx;
    background-color: #e0e0e0;
    margin-bottom: 4rpx;
  }

  .strength-level {
    height: 100%;
    transition: width 0.3s ease;
  }

  .strength-text {
    font-size: 24rpx;
  }

  .strength-weak {
    background-color: #ff4d4f;
    color: #ff4d4f;
  }

  .strength-medium {
    background-color: #faad14;
    color: #faad14;
  }

  .strength-strong {
    background-color: #52c41a;
    color: #52c41a;
  }
}
</style>
