<template>
  <view class="flex-row items-center px-sm py-[12rpx] bg-warning/20 rounded-[26rpx]">
    <c-icon type="huati" size="26" color="#FF6600"></c-icon>
    <text class="ml-[6rpx] text-warning text-[24rpx]">{{ props.content }}</text>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  content: {
    type: String,
    default: '',
  },
})
</script>

<style scoped lang="scss"></style>
