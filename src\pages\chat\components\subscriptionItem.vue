<template>
  <view class="flex-row items-center p-[10rpx] mb-[20rpx] bg-white">
    <view
      class="avatar-cell w-[100rpx] h-[100rpx] rounded-[50rpx] overflow-hidden bg-light mr-[20rpx]"
    >
      <image
        :src="props.data.avatar || '/statics/user/default_avatar.png'"
        class="w-[100%] h-[100%]"
      ></image>
    </view>
    <view class="content flex-1 overflow-hidden">
      <view class="title mb-[10rpx]">
        <text class="text-lg">{{ props.data.username }}</text>
      </view>
    </view>
    <view
      class="flex-row items-center w-[106rpx] h-[48rpx] justify-center rounded-[24rpx] bg-primary/10"
    >
      <c-icon type="plus" size="20" color="#0767ff"></c-icon>
      <text class="text-primary ml-[6rpx] text-[24rpx]">订阅</text>
    </view>
    <!-- <view class="ml-[12rpx]">
      <c-icon type="plus" color="#999999"></c-icon>
    </view> -->
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import type { IFriend } from 'types/chat.interface'
import { type PropType } from 'vue'
const props = defineProps({
  data: {
    type: Object as PropType<IFriend>,
    default: () => ({}),
  },
})
</script>

<style scoped lang="scss">
.badge {
  height: 26rpx;
  border-radius: 13rpx;
  min-width: 36rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}
.message {
  text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
