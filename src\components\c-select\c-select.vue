<template>
  <view class="select-box" :class="{ one_row: oneRow, 'bottom-line': bottomLine }">
    <label v-if="label"
      ><text>{{ label }}</text></label
    >
    <view
      class="select-row flex-row"
      :class="[size, border ? 'border' : '', disabled ? 'disabled' : '']"
      @click="pickerClick"
    >
      <view class="select" v-if="multiple || customer">
        <view class="flex-row space-between items-center">
          <slot>
            <view
              :class="viewSelectedLabel ? 'hasvalue' : 'novalue'"
              :style="'text-align:' + align"
            >
              {{ viewSelectedLabel || placeholder }}
            </view>
          </slot>
          <view class="flex-row items-center">
            <view v-if="clearable && viewSelectedLabel" class="clear_btn" @click.stop="handleClear">
              <c-icon type="clear" color="#c0c4cc"></c-icon>
            </view>
            <text v-if="unit" class="unit">{{ unit }}</text>
            <c-icon v-if="show_icon" type="jinru" class="arrow" color="#c3c3c3"></c-icon>
          </view>
        </view>
        <c-popup position="bottom" v-model="show_customer_select">
          <view class="option_list">
            <view class="header flex-row">
              <text class="cancel_btn" @click.stop.prevent="show_customer_select = false"
                >取消</text
              >
              <text class="tip">{{ tipTitle }}</text>
              <text
                class="ok_btn"
                :class="{
                  disabled:
                    (!tepm_current_value || tepm_current_value.length === 0) &&
                    (!modelValue || modelValue.length === 0),
                }"
                @click.stop.prevent="onConfirmSelect"
                >完成</text
              >
            </view>
            <view v-if="filterable || remote" class="search_box">
              <input
                type="search"
                adjust-position
                v-model="keyContent"
                :placeholder="searchPlaceholder"
                @confirm="handleFilter"
              />
              <view class="search-btn" @click="handleFilter">检索</view>
            </view>
            <scroll-view scroll-y class="content">
              <view
                class="item bottom-line"
                v-for="(item, index) in options"
                :key="index"
                :class="{
                  active:
                    (tepm_current_value || tepm_current_value === 0 || tepm_current_value === '') &&
                    (multiple
                      ? tepm_current_value.includes(item[valueName])
                      : tepm_current_value === item[valueName]),
                  disabled: item.disabled,
                }"
                @click.stop.prevent="onClickSelect(item)"
              >
                <slot name="option" :data="item">
                  <text class="text">{{ item[labelName] }}</text>
                </slot>
                <view class="icon">
                  <c-icon type="chenggong" size="26rpx" color="#0767ff"></c-icon>
                </view>
              </view>
            </scroll-view>
          </view>
        </c-popup>
      </view>
      <picker
        v-else
        :disabled="disabled"
        @change="pickerChange"
        :value="index"
        :range="options"
        :range-key="labelName"
      >
        <view class="flex-row space-between items-center">
          <slot>
            <view
              :class="viewSelectedLabel ? 'hasvalue' : 'novalue'"
              :style="'text-align:' + align"
            >
              {{ viewSelectedLabel || placeholder }}
            </view>
          </slot>
          <view class="flex-row items-center">
            <view v-if="clearable && viewSelectedLabel" class="clear_btn" @click.stop="handleClear">
              <c-icon type="clear" style="color: #c0c4cc"></c-icon>
              <!-- <myIcon
								v-if="show_icon"
								type="clear"
								color="#dedede"
								:size="size === 'small' ? 28 : 32"
							></myIcon> -->
            </view>
            <text v-if="unit" class="unit">{{ unit }}</text>
            <c-icon v-if="show_icon" size="32" type="jinru" class="arrow" color="#c3c3c3"></c-icon>
          </view>
        </view>
      </picker>
    </view>
  </view>
</template>

<script>
export default {
  components: {},
  props: {
    // 是否可以多选
    multiple: {
      type: Boolean,
      default: false,
    },
    // 是否使用自定义样式
    customer: {
      type: Boolean,
      default: false,
    },
    // 是否显示清除按钮
    clearable: {
      type: Boolean,
      default: false,
    },
    // 是否可筛选
    filterable: {
      type: Boolean,
      default: false,
    },
    // 是否使用远程搜索
    remote: {
      type: Boolean,
      default: false,
    },
    searchPlaceholder: {
      type: String,
      default: '请输入检索',
    },
    label: String,
    name: String,
    modelValue: {
      type: [String, Number, Array],
      default: '',
    },
    range: {
      type: Array,
      default: () => [],
    },
    valueName: {
      type: String,
      default: 'value',
    },
    labelName: {
      type: String,
      default: 'name',
    },
    border: Boolean,
    unit: String,
    size: {
      //default big large small
      type: [String],
      default: 'default',
    },
    disabled: {
      type: [Boolean],
      default: false,
    },
    oneRow: {
      type: [Boolean],
      default: false,
    },
    bottomLine: {
      type: [Boolean],
      default: false,
    },
    show_icon: {
      type: [Boolean],
      default: true,
    },
    align: {
      type: String,
      default: 'left',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    tipTitle: {
      type: String,
      default: '请选择',
    },
    remoteMethod: {
      type: Function,
      default: null,
    },
    expandfield: {
      type: String,
      default: '检索',
    },
    // 是否开启创建字段同步到远程
    remoteSync: {
      type: [Boolean],
      default: false,
    },
    remoteSyncMethod: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      options: [],
      tepm_current_value: '',
      show_customer_select: false,
      keyContent: '',
      createContent: '',
      selectedOptions: [], //已经选择的选项
      tempSelectedOptions: [],
      expandKey: '',
    }
  },
  watch: {
    modelValue(val) {
      this.setShowSelected(this.range, val)
    },
    range: {
      handler(val) {
        if (val) {
          this.options = [...val]
        } else {
          this.options = []
        }
        this.setShowSelected(val, this.modelValue)
      },
      deep: true,
      immediate: true,
    },
    show_customer_select(val) {
      this.tempSelectedOptions = [...this.selectedOptions]
      if (val) {
        this.$emit('panelShow')
        if (this.multiple) {
          this.tepm_current_value = Array.from(this.modelValue)
        } else {
          this.tepm_current_value = this.modelValue
        }
      }
    },
  },
  computed: {
    index() {
      if (
        this.modelValue ||
        this.modelValue === '' ||
        this.modelValue === '0' ||
        this.modelValue === 0
      ) {
        return this.options.findIndex((item) => item[this.valueName] === this.modelValue)
      } else {
        return -1
      }
    },
    viewSelectedLabel() {
      if (this.isEmpty(this.modelValue) || this.modelValue.length === 0) {
        return ''
      }
      if (!this.selectedOptions || this.selectedOptions.length === 0) {
        if (this.multiple) {
          return this.modelValue.length ? this.modelValue : ''
        } else {
          return this.modelValue
        }
      }
      let selected_option_label = ''
      let selected_option_val = ''
      if (Object.prototype.toString.call(this.modelValue) === '[object Array]') {
        let selectedOption = []
        this.modelValue.forEach((item) => {
          let _currOptions = this.range.find((_item) => _item[this.valueName] === item)
          if (!_currOptions) {
            _currOptions = [...this.options, ...this.selectedOptions].find(
              (_item) => _item[this.valueName] === item
            )
          }
          if (_currOptions) {
            selectedOption.push(_currOptions)
          } else if (item) {
            selectedOption.push({
              [this.labelName]: item,
            })
            let newOption = {}
            newOption[this.labelName] = item
            newOption[this.valueName] = item
            this.options.unshift(newOption)
          }
        })
        selected_option_label = selectedOption.map((item) => item[this.labelName]).join(',')
        // selected_option_label = this.options
        // 	.filter((item) => this.modelValue.includes(item[this.valueName]))
        // 	.map((item) => item[this.labelName])
        // 	.join(',')
      } else {
        const currentOption = [...this.options, ...this.selectedOptions].find(
          (item) => this.modelValue === item[this.valueName]
        )
        selected_option_label = (currentOption || {})[this.labelName]
        selected_option_val = (currentOption || {})[this.valueName]
      }
      return selected_option_label || selected_option_val || this.modelValue || ''
    },
  },
  created() {
    if (!this.modelValue && this.multiple) {
      // this.modelValue = []
      this.tepm_current_value = []
    }
  },
  // filters: {
  //   viewSelectedLabel(val, placeholder) {
  //     if (!val || val.length === 0) {
  //       return placeholder
  //     }
  //     var selected_option = this.options
  //       .filter((item) => this.modelValue.includes(item[this.valueName]))
  //       .join(',')
  //     return selected_option || ''
  //   },
  // },
  methods: {
    isEmpty(val) {
      return !val && val !== 0
    },
    setShowSelected(range, val) {
      if (this.multiple && val) {
        val.forEach((item) => {
          if (!this.selectedOptions.find((_item) => _item[this.valueName] === item)) {
            const _curr = range.find((_item) => _item[this.valueName] === item)
            if (_curr) {
              this.selectedOptions.push(_curr)
            }
          }
        })
      } else {
        if (!this.selectedOptions.find((_item) => _item[this.valueName] === val)) {
          const _curr = range.find((_item) => _item[this.valueName] === val)
          if (_curr) {
            this.selectedOptions.push(_curr)
          }
        }
      }
    },
    pickerChange(e) {
      let index = parseInt(e.detail.value) || 0
      if (index < 0) {
        index = 0
      }
      var value = this.options[index][this.valueName]
      this.$emit('update:modelValue', value)
      this.$emit('change', value)
    },
    handleClear() {
      this.$emit('update:modelValue', this.multiple ? [] : '')
      this.$emit('change', this.multiple ? [] : '')
    },
    pickerClick() {
      if (this.disabled) {
        return
      }
      if (this.multiple || this.customer) {
        this.show_customer_select = true
      }
    },
    onClickSelect(e) {
      if (e.disabled) {
        return
      }
      // console.log(this.tempSelectedOptions)
      if (this.multiple) {
        if (this.tepm_current_value.includes(e[this.valueName])) {
          this.tepm_current_value = this.tepm_current_value.filter(
            (item) => item !== e[this.valueName]
          )
          this.tempSelectedOptions = this.tempSelectedOptions.filter((item) => {
            return item[this.valueName] !== e[this.valueName]
          })
        } else {
          this.tempSelectedOptions.push(e)
          // console.log(this.tempSelectedOptions)
          this.tepm_current_value.push(e[this.valueName])
        }
      } else {
        this.tempSelectedOptions = [e]
        this.tepm_current_value = e[this.valueName]
        this.onConfirmSelect()
      }
    },
    onConfirmSelect() {
      if (
        (this.isEmpty(this.tepm_current_value) || this.tepm_current_value.length === 0) &&
        (this.isEmpty(this.modelValue) || this.modelValue.length === 0)
      ) {
        return
      }
      this.show_customer_select = false
      let value
      if (this.multiple) {
        value = Array.from(this.tepm_current_value)
      } else {
        value = this.tepm_current_value
      }
      this.selectedOptions = [...this.tempSelectedOptions]
      // console.log(this.selectedOptions)
      this.$emit('update:modelValue', value) //兼容小程序的双向绑定
      this.$emit('change', value, this.selectedOptions)
    },
    handleFilter() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        if (this.remoteMethod) {
          this.remoteMethod(this.keyContent)
        } else {
          this.options = this.range.filter((item) => item[this.labelName].includes(this.keyContent))
        }
        // #ifdef APP
        uni.hideKeyboard()
        // plus.key.hideSoftKeybord()
        // #endif
      }, 200)
    },
  },
}
</script>

<style lang="scss" scoped>
.option_list {
  // #ifdef H5
  // margin-bottom: 50px;
  // #endif
  &.has_bottombar {
    margin-bottom: 50px;
  }

  background-color: #fff;

  .header {
    // padding: 24rpx;
    justify-content: space-between;
    font-size: 32rpx;
    border-top: 4rpx solid $color-primary;
    background-image: linear-gradient(
      0deg,
      rgba(246, 246, 246, 0) 0%,
      rgba($color-primary, 0.1) 100%
    );

    .tip {
      padding: 24rpx 0;
      flex: 1;
      text-align: center;
      color: #333;
    }

    .cancel_btn {
      padding: 24rpx;
      color: #888;
    }

    .ok_btn {
      padding: 24rpx;
      color: #409eff;
      transition: 0.26s;

      &.disabled {
        opacity: 0.6;
      }
    }
  }

  .search_box {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12rpx 86rpx;
    border-bottom: 1rpx solid #f3f3f3;

    input {
      flex: 1;
      height: 66rpx;
      padding: 12rpx 12rpx;
      font-size: 32rpx;
      background-color: #f7f7f7;
    }

    .search-btn {
      line-height: 60rpx;
      padding: 0 32rpx;
      margin-left: 24rpx;
      border-radius: 6rpx;
      // border: 1rpx solid #409eff;
      // color: #409eff;
      border: 1rpx solid #409eff;
      color: #409eff;
      transition: 0.26s;

      &.disabled {
        opacity: 0.6;
      }
    }
  }

  .content {
    max-height: 45vh;
    min-height: 600rpx;
    padding: 0 48rpx;

    .item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;

      :deep(.icon) {
        display: none;
      }

      &.active {
        color: #409eff;
        :deep(text) {
          color: #409eff !important;
        }

        :deep(.icon) {
          display: block;
          color: #409eff;
        }
      }

      &.disabled {
        color: #999;
      }

      .text {
        margin-right: 16rpx;
      }
    }
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
}

label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.select-box {
  width: 100%;
  // padding: 24rpx 0;
}

.one_row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0;

  label {
    margin-bottom: 0;
    font-size: 32rpx;
    color: #666;
    margin-right: 24rpx;
  }

  .select-row {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.border {
      padding: 12rpx;
      border-radius: 12rpx;
      border: 1rpx solid #f3f3f3;
    }

    &.disabled {
      background-color: #f3f3f3;
    }

    picker {
      overflow: hidden;

      view {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        // text-align: right;
        font-size: 28rpx;
        // color: #999;
      }
    }

    &.large {
      border-radius: 8rpx;
      padding: 12rpx 16rpx;
      font-size: 28rpx;

      view {
        font-size: 28rpx;
      }

      .arrow {
        font-size: 28rpx;
      }
    }

    &.big {
      border-radius: 8rpx;
      padding: 12rpx 16rpx;
      font-size: 32rpx;

      view {
        font-size: 32rpx;
      }

      .arrow {
        font-size: 32rpx;
      }
    }

    &.small {
      border-radius: 8rpx;
      padding: 6rpx 12rpx;
      font-size: 24rpx;

      view {
        font-size: 24rpx;
      }

      .arrow {
        font-size: 24rpx;
      }
    }
  }
}

.select-row {
  align-items: center;
  // line-height: 62upx;
  background-color: #fff;

  &.border {
    padding: 12rpx;
    border-radius: 12rpx;
    border: 1rpx solid #f3f3f3;
  }

  &.disabled {
    background-color: #f3f3f3;
  }

  .select {
    flex: 1;
    font-size: 32rpx;
  }

  .items-center {
    align-items: center;
  }

  picker {
    flex: 1;

    // height: 62upx;
    view {
      font-size: 28rpx;
    }
  }

  .hasvalue {
    flex-shrink: 0;
    flex: 1;
    font-size: 30rpx;
  }

  .novalue {
    flex-shrink: 0;
    flex: 1;
    font-size: 30rpx;
    color: #8a929f;
  }
  // .clear_btn{
  // 	padding: 2rpx;
  // }

  .unit {
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #f3f3f3;
  }

  uni-icon {
    line-height: 62rpx;
    margin-left: 10rpx;
  }

  &.big {
    border-radius: 8rpx;

    .novalue {
      font-size: 32rpx;
    }

    .hasvalue {
      font-size: 32rpx;
    }

    &.border {
      padding: 12rpx;
    }
  }

  &.large {
    border-radius: 8rpx;

    .hasvalue {
      font-size: 36rpx;
    }

    .novalue {
      font-size: 36rpx;
    }

    &.border {
      padding: 12rpx;
    }
  }

  &.small {
    border-radius: 8rpx;

    .hasvalue {
      font-size: 24rpx;
    }

    .novalue {
      font-size: 24rpx;
    }

    &.border {
      padding: 4rpx;
    }
  }
}

.bottom-line {
  position: relative;
  padding: 12rpx;
  background-color: #fff;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    // -webkit-transform: scaleY(0.5);
    // transform: scaleY(0.5);
    background-color: #f3f3f3;
  }
}
</style>
