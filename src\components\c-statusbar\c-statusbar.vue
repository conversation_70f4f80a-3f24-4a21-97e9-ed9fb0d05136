<template>
  <view :style="{ paddingTop }"></view>
</template>

<script setup lang="ts">
import { useClientStore } from '@/stores/client'

const { clientInfo } = useClientStore()
const paddingTop = computed(() => {
  console.log(clientInfo.platform)
  console.log(clientInfo.statusBarHeight)
  if (clientInfo.platform === 'mp') {
    return (clientInfo.statusBarHeight || 40) + 'px'
  }
  return (clientInfo.statusBarHeight || 40) + 'px'
})
</script>

<style scoped lang="scss"></style>
