<route lang="json">
{
  "style": {
    "navigationBarTitleText": "黑名单管理",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="blacklist-page">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <view class="loading-icon">
          <text class="icon">⏳</text>
        </view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="blacklist.length === 0" class="empty-state">
        <view class="empty-icon">
          <text class="icon">🚫</text>
        </view>
        <view class="empty-text">
          <text class="empty-title">暂无黑名单用户</text>
          <text class="empty-desc">被拉黑的用户将显示在这里</text>
        </view>
      </view>

      <!-- 黑名单列表 -->
      <view v-else class="blacklist-content">
        <view class="list-header">
          <text class="header-text">共 {{ blacklist.length }} 个用户</text>
        </view>

        <view class="blacklist-list">
          <view v-for="user in blacklist" :key="user.id" class="blacklist-item">
            <!-- 用户头像 -->
            <view class="user-avatar">
              <image :src="user.avatar" class="avatar" mode="aspectFill" />
            </view>

            <!-- 用户信息 -->
            <view class="user-info">
              <view class="user-name-row">
                <text class="user-name">{{ user.nickname }}</text>
                <view v-if="user.isVerified" class="verified-badge">
                  <text class="badge-text">T</text>
                </view>
              </view>
              <text class="block-time">{{ formatBlockTime(user.blockTime) }}</text>
            </view>

            <!-- 解除按钮 -->
            <view class="action-section">
              <view class="unblock-btn" @click="unblockUser(user)">
                <text class="unblock-text">解除拉黑</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'
import { getBlacklistPage, removeFromBlacklist, type BlacklistUser } from '@/api/blacklist.ts'

const blacklist = ref<BlacklistUser[]>([])
const loading = ref(false)
const total = ref(0)
const pageParams = reactive({
  current: 1,
  size: 20,
})

onMounted(() => {
  loadBlacklist()
})

const loadBlacklist = async () => {
  try {
    loading.value = true
    const response = await getBlacklistPage(pageParams)

    if (response.code === 200) {
      blacklist.value = response.data || []
      total.value = response.total || 0
    } else {
      uni.showToast({
        title: response.message || '获取黑名单失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取黑名单失败:', error)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none',
    })

    // 如果API失败，使用模拟数据作为备用
    blacklist.value = [
      {
        id: '1',
        nickname: '程振宇',
        avatar: '/static/images/user/tx.png',
        isVerified: false,
        blockTime: '2024-01-15 14:30:00',
      },
      {
        id: '2',
        nickname: '成瑞辰',
        avatar: '/static/images/user/tx.png',
        isVerified: false,
        blockTime: '2024-01-10 09:15:00',
      },
      {
        id: '3',
        nickname: '王伟洋',
        avatar: '/static/images/user/tx.png',
        isVerified: false,
        blockTime: '2024-01-08 16:45:00',
      },
    ]
  } finally {
    loading.value = false
  }
}

const formatBlockTime = (blockTime: string) => {
  const now = dayjs()
  const blockDate = dayjs(blockTime)
  const diffDays = now.diff(blockDate, 'day')

  if (diffDays === 0) {
    return '今天拉黑'
  } else if (diffDays === 1) {
    return '昨天拉黑'
  } else if (diffDays < 7) {
    return `${diffDays}天前拉黑`
  } else {
    return blockDate.format('YYYY-MM-DD 拉黑')
  }
}

const unblockUser = (user: BlacklistUser) => {
  uni.showModal({
    title: '解除拉黑',
    content: `确定要将 ${user.nickname} 移出黑名单吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await removeFromBlacklist(user.userId || user.id)

          if (response.code === 200) {
            // 从本地列表中移除
            const index = blacklist.value.findIndex((item) => item.id === user.id)
            if (index > -1) {
              blacklist.value.splice(index, 1)
              total.value = Math.max(0, total.value - 1)
            }

            uni.showToast({
              title: '已解除拉黑',
              icon: 'success',
            })
          } else {
            uni.showToast({
              title: response.message || '解除拉黑失败',
              icon: 'none',
            })
          }
        } catch (error) {
          console.error('解除拉黑失败:', error)
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none',
          })
        }
      }
    },
  })
}
</script>

<style scoped lang="scss">
.blacklist-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 60rpx;
  text-align: center;
}

.loading-icon {
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 60rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 40rpx;
}

.icon {
  font-size: 120rpx;
  opacity: 0.3;
}

.empty-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

.blacklist-content {
  padding: 0;
}

.list-header {
  background-color: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-text {
  font-size: 26rpx;
  color: #666;
}

.blacklist-list {
  background-color: white;
}
.blacklist-item {
  flex-direction: row;
}
.user-info {
  align-items: start;
}

.blacklist-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.user-avatar {
  margin-right: 30rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
}

.user-info {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-right: 15rpx;
}

.verified-badge {
  width: 32rpx;
  height: 32rpx;
  background-color: #1989fa;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  color: white;
  font-size: 18rpx;
  font-weight: bold;
}

.block-time {
  font-size: 24rpx;
  color: #999;
}

.action-section {
  margin-left: 20rpx;
}

.unblock-btn {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  border: 1rpx solid #e0e0e0;
}

.unblock-text {
  font-size: 24rpx;
  color: #666;
}
</style>
