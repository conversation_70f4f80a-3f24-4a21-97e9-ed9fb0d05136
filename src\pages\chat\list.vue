<route type="home" lang="json">
{
  "style": {
    "navigationBarTitleText": "消息",
    "enablePullDownRefresh": false,
    "navigationStyle": "custom"
  },
  "auth": false
}
</route>
<template>
  <c-page fullScreen hasBottomBar>
    <view class="bg w-full absolute h-[360rpx]">
      <image
        :src="$imgUrl('/statics/images/header_bg2.png')"
        class="w-full"
        mode="aspectFill"
      ></image>
    </view>
    <view class="header sticky top-0 z-10 h-[280rpx]">
      <view class="w-full h-[360rpx] absolute">
        <image
          :src="$imgUrl('/statics/images/header_bg2.png')"
          class="w-full h-full"
          mode="aspectFill"
        ></image>
      </view>
      <view class="absolute top-0 left-0 h-full w-full flex flex-col justify-end">
        <!-- <c-titlebar title="圈子"></c-titlebar> -->
        <view>
          <c-tabs
            :current="state.currentTab"
            :list="state.tabList"
            type="justify-start"
            :showBottomLine="false"
            size="large"
            @change="switchTab"
          ></c-tabs>
        </view>
        <view class="px-[26rpx] py-[20rpx]">
          <c-search :showArea="false" placeholder="备注/昵称/聊天记录"></c-search>
        </view>
      </view>
    </view>
    <view :class="[state.currentTab !== 'message' && 'none']">
      <chatList ref="messageRef"></chatList>
    </view>
    <friendList v-if="state.currentTab === 'friend'" ref="friendRef"></friendList>
    <subscriptionList
      v-if="state.currentTab === 'subscription'"
      ref="subscriptionRef"
    ></subscriptionList>
    <c-tabbar></c-tabbar>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import chatList from './components/chatList.vue'
import friendList from './components/friendList.vue'
import subscriptionList from './components/subscriptionList.vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'

const state = reactive({
  currentTab: 'message',
  tabList: [
    {
      name: '消息',
      type: 'message',
    },
    {
      name: '通讯录',
      type: 'friend',
    },
    {
      name: '订阅',
      type: 'subscription',
    },
  ],
})
const switchTab = function (type: string) {
  if (state.currentTab === type) {
    return
  }
  state.currentTab = type
  nextTick(() => {
    // #ifdef MP-WEIXIN
    setTimeout(() => {
      initData()
    }, 200)
    // #endif
    // #ifndef MP-WEIXIN
    initData()
    // #endif
  })
}
const messageRef = ref()
const friendRef = ref()
const subscriptionRef = ref()
const initData = function () {
  switch (state.currentTab) {
    case 'message':
      if (messageRef.value?.initData) {
        messageRef.value.initData()
      } else {
        setTimeout(() => {
          messageRef.value.initData()
        }, 200)
      }
      break
    case 'friend':
      if (friendRef.value?.initData) {
        friendRef.value.initData()
      } else {
        setTimeout(() => {
          friendRef.value.initData()
        }, 200)
      }
      break
    case 'subscription':
      if (subscriptionRef.value?.initData) {
        subscriptionRef.value.initData()
      } else {
        setTimeout(() => {
          subscriptionRef.value.initData()
        }, 200)
      }
      break
  }
}
onMounted(() => {
  initData()
})
onReachBottom(() => {
  switch (state.currentTab) {
    case 'message':
      messageRef.value.loadMore()
      break
    case 'friend':
      friendRef.value.loadMore()
      break
    case 'subscription':
      subscriptionRef.value.loadMore()
      break
  }
})
onPullDownRefresh(() => {
  initData()
})
</script>

<style lang="scss">
.header {
  position: sticky;
  z-index: 9;
}
</style>
