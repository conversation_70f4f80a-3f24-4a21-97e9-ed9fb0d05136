import { http } from '@/utils/http'

// 聊天设置相关接口
export interface ChatSettings {
  userId: string
  doNotDisturb: boolean
  isBlocked: boolean
}

export interface GroupSettings {
  groupId: string
  doNotDisturb: boolean
  isTop: boolean
  showMemberNickname: boolean
}

export interface UserInfo {
  id: string
  nickname: string
  avatar: string
  remarkName: string
  isVerified: boolean
  isFollowed: boolean
}

export interface GroupInfo {
  id: string
  name: string
  remark: string
  memberCount: number
  members: GroupMember[]
}

export interface GroupMember {
  id: string
  nickname: string
  avatar: string
  role: 'owner' | 'admin' | 'member'
}

// 获取用户信息
export const getUserInfo = (userId: string): Promise<UserInfo> => {
  return http.get(`/api/chat/user/${userId}`)
}

// 更新聊天设置
export const updateChatSettings = (settings: Partial<ChatSettings>): Promise<void> => {
  return http.post('/api/chat/settings', settings)
}

// 更新免打扰设置
export const updateDoNotDisturb = (userId: string, doNotDisturb: boolean): Promise<void> => {
  return http.post('/api/chat/do-not-disturb', { userId, doNotDisturb })
}

// 更新黑名单状态
export const updateBlacklistStatus = (userId: string, isBlocked: boolean): Promise<void> => {
  return http.post('/api/chat/blacklist', { userId, isBlocked })
}

// 更新关注状态
export const updateFollowStatus = (userId: string, isFollowed: boolean): Promise<void> => {
  return http.post('/api/chat/follow', { userId, isFollowed })
}

// 更新用户备注
export const updateUserRemark = (userId: string, remarkName: string): Promise<void> => {
  return http.post('/api/chat/remark', { userId, remarkName })
}

// 清空聊天记录
export const clearChatHistory = (userId: string): Promise<void> => {
  return http.delete(`/api/chat/history/${userId}`)
}

// 群聊相关接口

// 获取群信息
export const getGroupInfo = (groupId: string): Promise<GroupInfo> => {
  return http.get(`/api/group/${groupId}`)
}

// 获取群成员列表
export const getGroupMembers = (groupId: string): Promise<GroupMember[]> => {
  return http.get(`/api/group/${groupId}/members`)
}

// 更新群设置
export const updateGroupSettings = (settings: Partial<GroupSettings>): Promise<void> => {
  return http.post('/api/group/settings', settings)
}

// 更新群名称
export const updateGroupName = (groupId: string, name: string): Promise<void> => {
  return http.post('/api/group/name', { groupId, name })
}

// 更新群备注
export const updateGroupRemark = (groupId: string, remark: string): Promise<void> => {
  return http.post('/api/group/remark', { groupId, remark })
}

// 更新我在群里的昵称
export const updateMyGroupNickname = (groupId: string, nickname: string): Promise<void> => {
  return http.post('/api/group/my-nickname', { groupId, nickname })
}

// 添加群成员
export const addGroupMembers = (groupId: string, userIds: string[]): Promise<void> => {
  return http.post('/api/group/add-members', { groupId, userIds })
}

// 移除群成员
export const removeGroupMembers = (groupId: string, userIds: string[]): Promise<void> => {
  return http.post('/api/group/remove-members', { groupId, userIds })
}

// 退出群聊
export const leaveGroup = (groupId: string): Promise<void> => {
  return http.post('/api/group/leave', { groupId })
}

// 清空群聊天记录
export const clearGroupChatHistory = (groupId: string): Promise<void> => {
  return http.delete(`/api/group/history/${groupId}`)
}

// 创建群聊
export const createGroup = (name: string, userIds: string[]): Promise<{ groupId: string }> => {
  return http.post('/api/group/create', { name, userIds })
}

// 搜索聊天内容
export const searchChatContent = (keyword: string, userId?: string, groupId?: string): Promise<any[]> => {
  const params = { keyword, userId, groupId }
  return http.get('/api/chat/search', { params })
}

// 举报用户
export const reportUser = (userId: string, reason: string, description?: string): Promise<void> => {
  return http.post('/api/report/user', { userId, reason, description })
}

// 举报群聊
export const reportGroup = (groupId: string, reason: string, description?: string): Promise<void> => {
  return http.post('/api/report/group', { groupId, reason, description })
}

// 获取聊天列表
export const getChatList = (): Promise<any[]> => {
  return http.get('/api/chat/list')
}

// 发送消息
export const sendMessage = (data: {
  type: 'user' | 'group'
  targetId: string
  content: string
  messageType: 'text' | 'image' | 'voice' | 'video' | 'file'
}): Promise<void> => {
  return http.post('/api/chat/send', data)
}

// 获取聊天历史
export const getChatHistory = (data: {
  type: 'user' | 'group'
  targetId: string
  page?: number
  limit?: number
}): Promise<any[]> => {
  return http.get('/api/chat/history', { params: data })
}

// 标记消息已读
export const markMessageRead = (data: {
  type: 'user' | 'group'
  targetId: string
  messageIds?: string[]
}): Promise<void> => {
  return http.post('/api/chat/read', data)
}

// 撤回消息
export const recallMessage = (messageId: string): Promise<void> => {
  return http.post('/api/chat/recall', { messageId })
}

// 转发消息
export const forwardMessage = (data: {
  messageId: string
  targetType: 'user' | 'group'
  targetIds: string[]
}): Promise<void> => {
  return http.post('/api/chat/forward', data)
}

// 获取未读消息数量
export const getUnreadCount = (): Promise<{ total: number; details: any[] }> => {
  return http.get('/api/chat/unread-count')
}

// 设置消息置顶
export const setChatTop = (data: {
  type: 'user' | 'group'
  targetId: string
  isTop: boolean
}): Promise<void> => {
  return http.post('/api/chat/top', data)
}

// 删除聊天会话
export const deleteChat = (data: {
  type: 'user' | 'group'
  targetId: string
}): Promise<void> => {
  return http.delete('/api/chat/conversation', { data })
}
