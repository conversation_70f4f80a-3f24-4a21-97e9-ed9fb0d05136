<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择市",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page>
    <view>
      <c-list-item
        v-for="item in dataList"
        :key="item.adcode"
        :title="item.name"
        @click="toArea(item)"
      ></c-list-item>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { getAreaData, type IArea } from '@/api/user'
import { navigateTo } from '@/utils/util'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const dataList = ref<IArea[]>([])

const getCityList = function (provinceCode) {
  getAreaData('2', provinceCode).then((res) => {
    if (res.data) {
      dataList.value = res.data.filter((item) => item.parentId === provinceCode)
    }
  })
}
let query: Record<string, any> = {}
let selectLevel = ''
onLoad((options) => {
  if (options?.provinceCode) {
    getCityList(options.provinceCode)
  }
  if (options) {
    query = options
  }

  if (options?.selectLevel) {
    selectLevel = options.selectLevel
  }
  // console.log(query)
  // console.log(options?.provinceName)
})

const toArea = function (e) {
  if (selectLevel === '2') {
    uni.$emit('selectRegion', { ...query, cityCode: e.adcode, cityName: e.name })
    uni.navigateBack({
      delta: 2,
    })
    return
  }
  // navigateTo({
  //   path: `/user/areaList`,
  //   query: {
  //     cityCode: e.adcode,
  //     cityName: e.name,
  //     ...query,
  //   },
  // })
  navigateTo(
    `/user/areaList?provinceCode=${query.provinceCode}&provinceName=${query.provinceName}&cityCode=${e.adcode}&cityName=${e.name}`
  )
}
</script>

<style scoped lang="scss"></style>
