/**
 * 腾讯云IM SDK 集成工具类
 * 提供即时通信功能：单聊、群聊、消息已读状态等
 */

import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'
import { IM_CONFIG } from './config'

export interface IMConfig {
  SDKAppID: number
  userID: string
  userSig: string
}

export interface Message {
  ID: string
  type: string
  payload: any
  from: string
  to: string
  time: number
  isRead: boolean
  conversationID: string
  conversationType: string
  nick?: string
  avatar?: string
}

export interface Conversation {
  conversationID: string
  type: string
  userProfile?: any
  groupProfile?: any
  lastMessage?: Message
  unreadCount: number
  isPinned: boolean
  muteNotifications: boolean
}

class IMManager {
  private tim: any = null
  private isLoggedIn = false
  private messageListeners: Array<(message: Message) => void> = []
  private conversationListeners: Array<(conversations: Conversation[]) => void> = []
  private readReceiptListeners: Array<(data: any) => void> = []

  /**
   * 初始化IM SDK
   */
  init(config: IMConfig) {
    try {
      // 创建 SDK 实例
      this.tim = TIM.create({
        SDKAppID: config.SDKAppID,
      })

      // 注册腾讯云即时通信 IM 上传插件
      this.tim.registerPlugin({
        'tim-upload-plugin': TIMUploadPlugin,
      })

      // 设置日志级别 (根据环境动态设置)
      const logLevel =
        import.meta.env.MODE === 'development'
          ? IM_CONFIG.LOG_LEVEL.VERBOSE
          : IM_CONFIG.LOG_LEVEL.RELEASE
      this.tim.setLogLevel(logLevel)

      // 监听事件
      this.bindEvents()

      console.log('IM SDK 初始化成功')
      return true
    } catch (error) {
      console.error('IM SDK 初始化失败:', error)
      return false
    }
  }

  /**
   * 绑定IM事件监听
   */
  private bindEvents() {
    if (!this.tim) return

    // 监听消息接收
    this.tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived.bind(this))

    // 监听会话列表更新
    this.tim.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, this.onConversationListUpdated.bind(this))

    // 监听消息已读回执
    this.tim.on(
      TIM.EVENT.MESSAGE_READ_RECEIPT_RECEIVED,
      this.onMessageReadReceiptReceived.bind(this)
    )

    // 监听SDK状态变化
    this.tim.on(TIM.EVENT.SDK_READY, this.onSDKReady.bind(this))
    this.tim.on(TIM.EVENT.SDK_NOT_READY, this.onSDKNotReady.bind(this))

    // 监听被踢下线
    this.tim.on(TIM.EVENT.KICKED_OUT, this.onKickedOut.bind(this))

    // 监听网络状态变化
    this.tim.on(TIM.EVENT.NET_STATE_CHANGE, this.onNetStateChange.bind(this))
  }

  /**
   * 登录IM
   */
  async login(userID: string, userSig: string): Promise<boolean> {
    try {
      const res = await this.tim.login({
        userID,
        userSig,
      })

      if (res.code === 0) {
        this.isLoggedIn = true
        console.log('IM 登录成功')
        return true
      } else {
        console.error('IM 登录失败:', res)
        return false
      }
    } catch (error) {
      console.error('IM 登录异常:', error)
      return false
    }
  }

  /**
   * 登出IM
   */
  async logout(): Promise<boolean> {
    try {
      const res = await this.tim.logout()
      if (res.code === 0) {
        this.isLoggedIn = false
        console.log('IM 登出成功')
        return true
      } else {
        console.error('IM 登出失败:', res)
        return false
      }
    } catch (error) {
      console.error('IM 登出异常:', error)
      return false
    }
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(
    to: string,
    text: string,
    conversationType: string = TIM.TYPES.CONV_C2C
  ): Promise<Message | null> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return null
    }

    try {
      const message = this.tim.createTextMessage({
        to,
        conversationType,
        payload: {
          text,
        },
      })

      const res = await this.tim.sendMessage(message)
      if (res.code === 0) {
        return this.formatMessage(res.data.message)
      } else {
        console.error('发送消息失败:', res)
        return null
      }
    } catch (error) {
      console.error('发送消息异常:', error)
      return null
    }
  }

  /**
   * 发送图片消息
   */
  async sendImageMessage(
    to: string,
    file: File,
    conversationType: string = TIM.TYPES.CONV_C2C
  ): Promise<Message | null> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return null
    }

    try {
      const message = this.tim.createImageMessage({
        to,
        conversationType,
        payload: {
          file,
        },
      })

      const res = await this.tim.sendMessage(message)
      if (res.code === 0) {
        return this.formatMessage(res.data.message)
      } else {
        console.error('发送图片失败:', res)
        return null
      }
    } catch (error) {
      console.error('发送图片异常:', error)
      return null
    }
  }

  /**
   * 获取会话列表
   */
  async getConversationList(): Promise<Conversation[]> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return []
    }

    try {
      const res = await this.tim.getConversationList()
      if (res.code === 0) {
        return res.data.conversationList.map(this.formatConversation.bind(this))
      } else {
        console.error('获取会话列表失败:', res)
        return []
      }
    } catch (error) {
      console.error('获取会话列表异常:', error)
      return []
    }
  }

  /**
   * 获取消息列表
   */
  async getMessageList(
    conversationID: string,
    nextReqMessageID?: string
  ): Promise<{ messageList: Message[]; nextReqMessageID: string; isCompleted: boolean }> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return { messageList: [], nextReqMessageID: '', isCompleted: true }
    }

    try {
      const res = await this.tim.getMessageList({
        conversationID,
        nextReqMessageID,
        count: 15,
      })

      if (res.code === 0) {
        return {
          messageList: res.data.messageList.map(this.formatMessage.bind(this)),
          nextReqMessageID: res.data.nextReqMessageID,
          isCompleted: res.data.isCompleted,
        }
      } else {
        console.error('获取消息列表失败:', res)
        return { messageList: [], nextReqMessageID: '', isCompleted: true }
      }
    } catch (error) {
      console.error('获取消息列表异常:', error)
      return { messageList: [], nextReqMessageID: '', isCompleted: true }
    }
  }

  /**
   * 标记消息为已读
   */
  async markMessageAsRead(conversationID: string): Promise<boolean> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return false
    }

    try {
      const res = await this.tim.setMessageRead({
        conversationID,
      })

      if (res.code === 0) {
        console.log('消息已标记为已读')
        return true
      } else {
        console.error('标记消息已读失败:', res)
        return false
      }
    } catch (error) {
      console.error('标记消息已读异常:', error)
      return false
    }
  }

  /**
   * 发送消息已读回执
   */
  async sendMessageReadReceipt(messageList: Message[]): Promise<boolean> {
    if (!this.isLoggedIn || !messageList.length) {
      return false
    }

    try {
      const res = await this.tim.sendMessageReadReceipt(messageList.map((msg) => ({ ID: msg.ID })))

      if (res.code === 0) {
        console.log('已读回执发送成功')
        return true
      } else {
        console.error('发送已读回执失败:', res)
        return false
      }
    } catch (error) {
      console.error('发送已读回执异常:', error)
      return false
    }
  }

  /**
   * 创建群组
   */
  async createGroup(options: {
    type: string
    name: string
    memberList?: Array<{ userID: string; role?: string }>
    introduction?: string
    notification?: string
  }): Promise<{ groupID: string } | null> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return null
    }

    try {
      const res = await this.tim.createGroup(options)
      if (res.code === 0) {
        console.log('群组创建成功:', res.data.group.groupID)
        return { groupID: res.data.group.groupID }
      } else {
        console.error('创建群组失败:', res)
        return null
      }
    } catch (error) {
      console.error('创建群组异常:', error)
      return null
    }
  }

  /**
   * 加入群组
   */
  async joinGroup(groupID: string): Promise<boolean> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return false
    }

    try {
      const res = await this.tim.joinGroup({
        groupID,
        type: TIM.TYPES.GRP_WORK,
      })

      if (res.code === 0) {
        console.log('加入群组成功')
        return true
      } else {
        console.error('加入群组失败:', res)
        return false
      }
    } catch (error) {
      console.error('加入群组异常:', error)
      return false
    }
  }

  /**
   * 退出群组
   */
  async quitGroup(groupID: string): Promise<boolean> {
    if (!this.isLoggedIn) {
      console.error('请先登录IM')
      return false
    }

    try {
      const res = await this.tim.quitGroup(groupID)
      if (res.code === 0) {
        console.log('退出群组成功')
        return true
      } else {
        console.error('退出群组失败:', res)
        return false
      }
    } catch (error) {
      console.error('退出群组异常:', error)
      return false
    }
  }

  // 事件监听器管理
  addMessageListener(listener: (message: Message) => void) {
    this.messageListeners.push(listener)
  }

  removeMessageListener(listener: (message: Message) => void) {
    const index = this.messageListeners.indexOf(listener)
    if (index > -1) {
      this.messageListeners.splice(index, 1)
    }
  }

  addConversationListener(listener: (conversations: Conversation[]) => void) {
    this.conversationListeners.push(listener)
  }

  removeConversationListener(listener: (conversations: Conversation[]) => void) {
    const index = this.conversationListeners.indexOf(listener)
    if (index > -1) {
      this.conversationListeners.splice(index, 1)
    }
  }

  addReadReceiptListener(listener: (data: any) => void) {
    this.readReceiptListeners.push(listener)
  }

  removeReadReceiptListener(listener: (data: any) => void) {
    const index = this.readReceiptListeners.indexOf(listener)
    if (index > -1) {
      this.readReceiptListeners.splice(index, 1)
    }
  }

  // 私有方法
  private formatMessage(message: any): Message {
    return {
      ID: message.ID,
      type: message.type,
      payload: message.payload,
      from: message.from,
      to: message.to,
      time: message.time * 1000,
      isRead: message.isRead || false,
      conversationID: message.conversationID,
      conversationType: message.conversationType,
      nick: message.nick,
      avatar: message.avatar,
    }
  }

  private formatConversation(conversation: any): Conversation {
    return {
      conversationID: conversation.conversationID,
      type: conversation.type,
      userProfile: conversation.userProfile,
      groupProfile: conversation.groupProfile,
      lastMessage: conversation.lastMessage
        ? this.formatMessage(conversation.lastMessage)
        : undefined,
      unreadCount: conversation.unreadCount,
      isPinned: conversation.isPinned || false,
      muteNotifications: conversation.muteNotifications || false,
    }
  }

  // 事件处理方法
  private onMessageReceived(event: any) {
    const messageList = event.data.map(this.formatMessage.bind(this))
    messageList.forEach((message) => {
      this.messageListeners.forEach((listener) => listener(message))
    })
  }

  private onConversationListUpdated(event: any) {
    const conversationList = event.data.map(this.formatConversation.bind(this))
    this.conversationListeners.forEach((listener) => listener(conversationList))
  }

  private onMessageReadReceiptReceived(event: any) {
    this.readReceiptListeners.forEach((listener) => listener(event.data))
  }

  private onSDKReady() {
    console.log('IM SDK 准备就绪')
  }

  private onSDKNotReady() {
    console.log('IM SDK 未准备就绪')
  }

  private onKickedOut(event: any) {
    console.log('被踢下线:', event.data.type)
    this.isLoggedIn = false
  }

  private onNetStateChange(event: any) {
    console.log('网络状态变化:', event.data.state)
  }

  // Getter
  get isReady() {
    return this.isLoggedIn
  }

  get sdk() {
    return this.tim
  }
}

// 导出单例
export const imManager = new IMManager()
export default imManager
