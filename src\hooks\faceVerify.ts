const aliyunVerify = uni.requireNativePlugin('AP-FaceDetectModule')

import { getAliCertifyId, getAliCertifyRes } from '@/api/user'

export const useFaceVerify = function () {
  const getMetaInfo = function () {
    var metaInfo = aliyunVerify.getMetaInfo()

    let p = uni.getSystemInfoSync().platform
    if (p === 'ios') {
      metaInfo = JSON.stringify(metaInfo)
    }

    // uni.showToast({
    //   title: '返回的内容' + t,
    //   icon: 'none',
    // })

    // console.log(t)
    return metaInfo
  }

  const getVerifyId = async function (): Promise<string> {
    const metaInfo = getMetaInfo()
    const certifyRes = await getAliCertifyId({
      cardName: '邢启政',
      cardNo: '370481199108104616',
      metaInfo,
    })
    const certifyId = certifyRes.data.certifyId
    return certifyId
  }

  const verify = function (certifyId: string): Promise<any> {
    return new Promise((resolve) => {
      aliyunVerify.verify({ certifyId: certifyId }, function (res) {
        resolve(res)
      })
    })
  }

  const checkVerifyRes = async function (certifyId?: string) {
    if (!certifyId) {
      certifyId = await getVerifyId()
    }
    console.log(certifyId)
    const verifyRes = await verify(certifyId)
    console.log(verifyRes)
    switch (verifyRes.code) {
      case 1001:
        throw new Error('系统错误')
      case 1003:
        throw new Error('验证中断')
      case 2002:
        throw new Error('网络错误')
      case 2003:
        throw new Error('客户端设备时间错误')
      case 2006:
        throw new Error('认证失败')
      default:
        console.log(verifyRes.code)
    }
    const res = await getAliCertifyRes(verifyRes.id)
    if (res.data?.passed === 'T') {
      console.log(res.data)
      return res.data
    } else {
      throw new Error('人脸认证失败')
    }
  }
  return {
    checkVerifyRes,
  }
}
