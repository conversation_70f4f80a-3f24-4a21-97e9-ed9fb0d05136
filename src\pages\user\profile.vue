<route lang="json">
{
  "style": {
    "navigationBarTitleText": "用户详情",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>
<template>
  <c-page>
    <view class="profile-container">
      <!-- 用户基本信息 -->
      <view class="user-info bg-white p-[40rpx] mb-[20rpx]">
        <view class="flex-row items-center mb-[40rpx]">
          <!-- 头像 -->
          <view class="avatar w-[160rpx] h-[160rpx] rounded-[80rpx] overflow-hidden mr-[40rpx]">
            <image :src="userInfo.avatar" class="w-full h-full"></image>
          </view>

          <!-- 基本信息 -->
          <view class="flex-1">
            <view class="mb-[20rpx]">
              <text class="text-[36rpx] font-bold">{{ userInfo.username }}</text>
            </view>
            <view class="mb-[10rpx]">
              <text class="text-[28rpx] text-muted">ID: {{ userInfo.userId }}</text>
            </view>
            <view v-if="userInfo.location">
              <text class="text-[28rpx] text-muted">📍 {{ userInfo.location }}</text>
            </view>
          </view>
        </view>

        <!-- 个人简介 -->
        <view v-if="userInfo.signature" class="signature">
          <text class="text-[28rpx] text-content">{{ userInfo.signature }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="actions bg-white p-[40rpx] mb-[20rpx]">
        <view class="flex-row justify-between">
          <c-button @click="sendMessage" type="primary" class="flex-1 mr-[20rpx]">
            发消息
          </c-button>
          <c-button
            @click="toggleFollow"
            :type="userInfo.isFollowed ? 'default' : 'primary'"
            :plain="userInfo.isFollowed"
            class="flex-1"
          >
            {{ userInfo.isFollowed ? '已关注' : '关注' }}
          </c-button>
        </view>
      </view>

      <!-- 用户动态 -->
      <view class="dynamics bg-white p-[40rpx]">
        <view class="title mb-[30rpx]">
          <text class="text-[32rpx] font-bold">动态</text>
        </view>

        <view v-if="dynamicList.length === 0" class="empty text-center py-[80rpx]">
          <text class="text-muted">暂无动态</text>
        </view>

        <view v-else>
          <view
            v-for="(item, index) in dynamicList"
            :key="index"
            class="dynamic-item mb-[40rpx] pb-[40rpx] border-b border-gray-100"
          >
            <view class="content mb-[20rpx]">
              <text class="text-[28rpx]">{{ item.content }}</text>
            </view>

            <!-- 图片 -->
            <view v-if="item.images && item.images.length > 0" class="images mb-[20rpx]">
              <view class="flex-row flex-wrap">
                <image
                  v-for="(img, imgIndex) in item.images"
                  :key="imgIndex"
                  :src="img"
                  class="w-[200rpx] h-[200rpx] rounded-[10rpx] mr-[10rpx] mb-[10rpx]"
                  mode="aspectFill"
                  @click="previewImage(img, item.images)"
                ></image>
              </view>
            </view>

            <view class="meta flex-row items-center justify-between">
              <text class="text-[24rpx] text-muted">{{ item.time }}</text>
              <view class="actions flex-row items-center">
                <view class="like mr-[30rpx]" @click="toggleLike(index)">
                  <c-icon
                    :type="item.isLiked ? 'dianzan-fill' : 'dianzan'"
                    :color="item.isLiked ? '#ff4757' : '#999'"
                    size="24"
                  ></c-icon>
                  <text class="ml-[10rpx] text-[24rpx] text-muted">{{ item.likeCount }}</text>
                </view>
                <view class="comment">
                  <c-icon type="pinglun" color="#999" size="24"></c-icon>
                  <text class="ml-[10rpx] text-[24rpx] text-muted">{{ item.commentCount }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from '@/utils/util'

interface Dynamic {
  content: string
  images?: string[]
  time: string
  likeCount: number
  commentCount: number
  isLiked: boolean
}

const userInfo = reactive({
  userId: '',
  username: '',
  avatar: '/static/images/user/default_avatar.png',
  location: '',
  signature: '',
  isFollowed: false,
})

const dynamicList = ref<Dynamic[]>([
  {
    content: '今天看了一辆不错的车，准备入手！',
    images: [
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car1.jpg',
      'https://carcity-statics.oss-cn-hangzhou.aliyuncs.com/statics/dev/car2.jpg',
    ],
    time: '2小时前',
    likeCount: 12,
    commentCount: 3,
    isLiked: false,
  },
  {
    content: '分享一些买车的经验，希望对大家有帮助',
    time: '1天前',
    likeCount: 8,
    commentCount: 5,
    isLiked: true,
  },
])

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  userInfo.userId = options.userId || '001'
  userInfo.username = options.username || '用户'
  userInfo.location = '北京市'
  userInfo.signature = '专业二手车交易，诚信为本'
})

const sendMessage = () => {
  uni.navigateTo({
    url: `/pages/chat/chat?userId=${userInfo.userId}&username=${encodeURIComponent(
      userInfo.username
    )}`,
  })
}

const toggleFollow = () => {
  userInfo.isFollowed = !userInfo.isFollowed
  message.success(userInfo.isFollowed ? '关注成功' : '取消关注')
}

const toggleLike = (index: number) => {
  const item = dynamicList.value[index]
  item.isLiked = !item.isLiked
  item.likeCount += item.isLiked ? 1 : -1
}

const previewImage = (current: string, urls: string[]) => {
  uni.previewImage({
    current,
    urls,
  })
}
</script>

<style scoped lang="scss">
.profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.dynamic-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}
</style>
