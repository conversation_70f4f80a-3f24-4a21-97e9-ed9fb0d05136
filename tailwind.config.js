/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{html,js,ts,jsx,tsx,vue}'],
  theme: {
    colors: {
      white: '#ffffff',
      black: '#000000',
      secondary: '#9e9e9e',
      main: '#333333',
      title: '#1a1a1a',
      navTitle: '#383838',
      content: '#666666',
      muted: '#999999',
      border: '#DCDCDC',
      light: '#e5e5e5',
      tip: '#5f6678',
      primary: '#0767ff',
      highlight: '#fa2c19',
      // primary: {
      //   DEFAULT: '#ec702d',
      // },
      success: '#5ac725',
      warning: '#FF6600',
      error: '#f56c6c',
      info: '#909399',
      page: '#f8f8f8',
    },
    fontSize: {
      mn: '20rpx',
      xs: '24rpx',
      sm: '26rpx',
      base: '28rpx',
      lg: '30rpx',
      xl: '32rpx',
      '2xl': '34rpx',
      '3xl': '36rpx',
      '4xl': '38rpx',
      '5xl': '40rpx',
    },
    spacing: {
      0: 0,
      mn: '10rpx',
      xs: '16rpx',
      sm: '20rpx',
      base: '24rpx',
      lg: '28rpx',
      xl: '30rpx',
      '2xl': '32rpx',
      '3xl': '34rpx',
      '4xl': '36rpx',
      '5xl': '38rpx',
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false,
  },
}
