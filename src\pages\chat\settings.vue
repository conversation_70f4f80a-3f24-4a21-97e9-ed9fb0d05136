<route lang="json">
{
  "style": {
    "navigationBarTitleText": "聊天设置",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="chat-settings-page">
      <!-- 用户信息区域 -->
      <view class="user-info-section">
        <view class="user-card">
          <!-- 用户头像 -->
          <view class="avatar-container">
            <image :src="userInfo.avatar" class="avatar" mode="aspectFill" />
          </view>

          <!-- 用户信息 -->
          <view class="user-details">
            <view class="user-name-row">
              <text class="user-name">{{ userInfo.nickname }}</text>
              <!-- 认证标识 -->
              <!-- <view v-if="userInfo.isVerified" class="verified-badge">
                <text class="badge-text">T</text>
              </view> -->
            </view>
            <!-- 备注名 -->
            <view class="remark-row">
              <text class="remark-label">备注名：</text>
              <text class="remark-name">{{ userInfo.remarkName || '名牌车张总' }}</text>
              <text class="edit-btn" @click="editRemark">编辑</text>
            </view>
          </view>

          <!-- 关注按钮 -->
          <view class="follow-section">
            <view class="follow-btn" :class="{ disabled: loading }" @click="toggleFollow">
              <text class="follow-text">{{ userInfo.isFollowed ? '互相关注' : '关注' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 设置选项区域 -->
      <view class="settings-section">
        <!-- 设置免打扰 -->
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-title">设置免打扰</text>
            <text class="setting-desc">收到消息时不会通过公众号通知你</text>
          </view>
          <view class="setting-right">
            <switch :checked="settings.doNotDisturb" @change="toggleDoNotDisturb" color="#07C160" />
          </view>
        </view>

        <!-- 加入黑名单 -->
        <view class="setting-item">
          <view class="setting-left">
            <text class="setting-title">加入黑名单</text>
            <text class="setting-desc">他将无法关注，给您发消息</text>
          </view>
          <view class="setting-right">
            <switch :checked="settings.isBlocked" @change="toggleBlacklist" color="#07C160" />
          </view>
        </view>

        <!-- 清空聊天记录 -->
        <view class="setting-item clickable" @click="clearChatHistory">
          <view class="setting-left">
            <text class="setting-title">清空聊天记录</text>
          </view>
          <view class="setting-right">
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 黑名单管理 -->
        <view class="setting-item clickable" @click="goToBlacklist">
          <view class="setting-left">
            <text class="setting-title">黑名单管理</text>
          </view>
          <view class="setting-right">
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 投诉 -->
        <view class="setting-item clickable" @click="reportUser">
          <view class="setting-left">
            <text class="setting-title">投诉</text>
          </view>
          <view class="setting-right">
            <text class="arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑备注弹窗 -->
    <view v-if="showRemarkPopup" class="popup-mask" @click="cancelEditRemark">
      <view class="popup-container" @click.stop>
        <view class="remark-popup">
          <view class="popup-title">
            <text class="title-text">设置备注名</text>
          </view>
          <view class="input-section">
            <textarea
              v-model="remarkInput"
              placeholder="请输入备注名"
              maxlength="20"
              class="remark-input"
              :focus="showRemarkPopup"
            />
          </view>
          <view class="popup-buttons">
            <view class="btn cancel-btn" @click="cancelEditRemark">
              <text class="btn-text">取消</text>
            </view>
            <view class="btn confirm-btn" @click="confirmEditRemark">
              <text class="btn-text confirm-text">确定</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  addUserFollow,
  cancelUserFollow,
  updateFollowRemarkName,
  setFollowDoNotDisturb,
  cancelFollowDoNotDisturb,
  getFriendsConfig,
  checkFollowRelation,
} from '@/api/userFollow.ts'
import { getTestUserById, getAllTestUsers } from '@/config/im'

interface UserInfo {
  id: string
  nickname: string
  username: string
  phone: string
  avatar: string
  remarkName: string
  isVerified: boolean
  isFollowed: boolean
}

interface Settings {
  doNotDisturb: boolean
  isBlocked: boolean
}

const userInfo = reactive<UserInfo>({
  id: '',
  nickname: '默认用户',
  username: '',
  phone: '',
  avatar: '/static/images/user/default_avatar.png',
  remarkName: '',
  isVerified: true,
  isFollowed: true,
})

const settings = reactive<Settings>({
  doNotDisturb: true,
  isBlocked: false,
})

const remarkInput = ref('')
const showRemarkPopup = ref(false)
const loading = ref(false)
const currentUserId = ref('administrator') // 当前登录用户ID，实际应从用户状态获取
const pageOptions = ref<any>({})

// 页面加载时获取参数
onLoad((options) => {
  pageOptions.value = options || {}
  initUserInfo()
})

// 初始化用户信息
const initUserInfo = async () => {
  const userId = pageOptions.value.userId

  if (userId) {
    // 尝试从真实用户数据中获取
    const realUser = getTestUserById(userId)

    if (realUser) {
      // 使用真实用户数据
      userInfo.id = realUser.userID
      userInfo.nickname = realUser.nickname
      userInfo.username = realUser.username
      userInfo.phone = realUser.phone
      userInfo.avatar = realUser.avatar
        ? `/static/images/user/${realUser.avatar}`
        : '/static/images/user/default_avatar.png'
      userInfo.remarkName = realUser.nickname
    } else {
      // 使用传入的参数作为默认值
      userInfo.id = userId
      userInfo.nickname = decodeURIComponent(pageOptions.value.username || '用户')
      userInfo.username = userInfo.nickname
      userInfo.avatar = '/static/images/user/default_avatar.png'
      userInfo.remarkName = userInfo.nickname
    }

    // 加载用户关注状态和设置
    await loadUserSettings()
  } else {
    // 如果没有传入用户ID，使用第一个测试用户
    const testUsers = getAllTestUsers()
    if (testUsers.length > 0) {
      const defaultUser = testUsers[0]
      userInfo.id = defaultUser.userID
      userInfo.nickname = defaultUser.nickname
      userInfo.username = defaultUser.username
      userInfo.phone = defaultUser.phone
      userInfo.avatar = defaultUser.avatar
        ? `/static/images/user/${defaultUser.avatar}`
        : '/static/images/user/default_avatar.png'
      userInfo.remarkName = defaultUser.nickname
    }
  }
}

// 加载用户设置信息
const loadUserSettings = async () => {
  if (!userInfo.id || !currentUserId.value) return

  try {
    loading.value = true

    // 检查关注关系
    const relationResponse = await checkFollowRelation(currentUserId.value, userInfo.id)
    if (relationResponse.code === 200) {
      userInfo.isFollowed = relationResponse.data.isFollowing
      if (relationResponse.data.remarkName) {
        userInfo.remarkName = relationResponse.data.remarkName
      }
    }

    // 获取好友配置（免打扰等设置）
    const configResponse = await getFriendsConfig(currentUserId.value, userInfo.id)
    if (configResponse.code === 200) {
      settings.doNotDisturb = configResponse.data.isDoNotDisturb
      if (configResponse.data.remarkName) {
        userInfo.remarkName = configResponse.data.remarkName
      }
    }
  } catch (error) {
    console.error('加载用户设置失败:', error)
  } finally {
    loading.value = false
  }
}

const toggleDoNotDisturb = async (e: any) => {
  const newValue = e.detail.value

  if (!userInfo.id || !currentUserId.value) {
    uni.showToast({
      title: '用户信息不完整',
      icon: 'none',
    })
    return
  }

  try {
    loading.value = true

    if (newValue) {
      // 开启免打扰
      const response = await setFollowDoNotDisturb(currentUserId.value, userInfo.id, true)
      if (response.code === 200) {
        settings.doNotDisturb = true
        uni.showToast({
          title: '已开启免打扰',
          icon: 'success',
        })
      } else {
        throw new Error(response.message || '设置失败')
      }
    } else {
      // 关闭免打扰
      const response = await cancelFollowDoNotDisturb(currentUserId.value, userInfo.id)
      if (response.code === 200) {
        settings.doNotDisturb = false
        uni.showToast({
          title: '已关闭免打扰',
          icon: 'success',
        })
      } else {
        throw new Error(response.message || '设置失败')
      }
    }
  } catch (error) {
    console.error('设置免打扰失败:', error)
    uni.showToast({
      title: '设置失败，请稍后重试',
      icon: 'none',
    })
    // 恢复原状态
    settings.doNotDisturb = !newValue
  } finally {
    loading.value = false
  }
}

const toggleBlacklist = async (e: any) => {
  const newValue = e.detail.value

  if (!userInfo.id || !currentUserId.value) {
    uni.showToast({
      title: '用户信息不完整',
      icon: 'none',
    })
    return
  }

  if (newValue) {
    uni.showModal({
      title: '确认加入黑名单',
      content: '加入黑名单后，对方将无法给您发送消息',
      success: async (res) => {
        if (res.confirm) {
          try {
            loading.value = true
            // 模拟API调用
            await new Promise((resolve) => setTimeout(resolve, 1000))
            const response = { code: 200 }
            if (response.code === 200) {
              settings.isBlocked = true
              uni.showToast({
                title: '已加入黑名单',
                icon: 'success',
              })
            } else {
              throw new Error('加入黑名单失败')
            }
          } catch (error) {
            console.error('加入黑名单失败:', error)
            uni.showToast({
              title: '操作失败，请稍后重试',
              icon: 'none',
            })
            // 恢复原状态
            settings.isBlocked = false
          } finally {
            loading.value = false
          }
        } else {
          // 用户取消，恢复开关状态
          settings.isBlocked = false
        }
      },
    })
  } else {
    try {
      loading.value = true
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const response = { code: 200 }
      if (response.code === 200) {
        settings.isBlocked = false
        uni.showToast({
          title: '已移出黑名单',
          icon: 'success',
        })
      } else {
        throw new Error('移出黑名单失败')
      }
    } catch (error) {
      console.error('移出黑名单失败:', error)
      uni.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none',
      })
      // 恢复原状态
      settings.isBlocked = true
    } finally {
      loading.value = false
    }
  }
}

const toggleFollow = async () => {
  if (!userInfo.id || !currentUserId.value) {
    uni.showToast({
      title: '用户信息不完整',
      icon: 'none',
    })
    return
  }

  try {
    loading.value = true

    if (userInfo.isFollowed) {
      // 取消关注
      const response = await cancelUserFollow(currentUserId.value, userInfo.id)
      if (response.code === 200) {
        userInfo.isFollowed = false
        uni.showToast({
          title: '已取消关注',
          icon: 'success',
        })
      } else {
        throw new Error(response.message || '取消关注失败')
      }
    } else {
      // 添加关注
      const response = await addUserFollow(currentUserId.value, userInfo.id, userInfo.remarkName)
      if (response.code === 200) {
        userInfo.isFollowed = true
        uni.showToast({
          title: '已关注',
          icon: 'success',
        })
      } else {
        throw new Error(response.message || '关注失败')
      }
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

const editRemark = () => {
  remarkInput.value = userInfo.remarkName
  showRemarkPopup.value = true
}

const cancelEditRemark = () => {
  showRemarkPopup.value = false
}

const confirmEditRemark = async () => {
  const trimmedInput = remarkInput.value.trim()
  if (!trimmedInput) {
    uni.showToast({
      title: '请输入备注名',
      icon: 'none',
    })
    return
  }

  if (!userInfo.id || !currentUserId.value) {
    uni.showToast({
      title: '用户信息不完整',
      icon: 'none',
    })
    return
  }

  try {
    loading.value = true
    const response = await updateFollowRemarkName(currentUserId.value, userInfo.id, trimmedInput)
    if (response.code === 200) {
      userInfo.remarkName = trimmedInput
      showRemarkPopup.value = false
      uni.showToast({
        title: '备注名已更新',
        icon: 'success',
      })
    } else {
      throw new Error(response.message || '更新备注名失败')
    }
  } catch (error) {
    console.error('更新备注名失败:', error)
    uni.showToast({
      title: '更新失败，请稍后重试',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

const clearChatHistory = () => {
  uni.showModal({
    title: '清空聊天记录',
    content: '确定要清空与该用户的所有聊天记录吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '聊天记录已清空',
          icon: 'success',
        })
      }
    },
  })
}

const reportUser = () => {
  uni.navigateTo({
    url: '/pages/report/report?userId=' + userInfo.id,
  })
}

const goToBlacklist = () => {
  uni.navigateTo({
    url: '/pages/chat/blacklist',
  })
}
</script>

<style scoped lang="scss">
.chat-settings-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-info-section {
  padding: 0;
  margin-bottom: 20rpx;
}

.user-card {
  background-color: white;
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.user-details {
  flex: 1;
}
.user-card,
.remark-row,
.popup-buttons {
  flex-direction: row;
}

.user-name-row {
  display: flex;
  align-items: start;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 15rpx;
}

.verified-badge {
  width: 32rpx;
  height: 32rpx;
  background-color: #1989fa;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  color: white;
  font-size: 18rpx;
  font-weight: bold;
}

.remark-row {
  display: flex;
  align-items: center;
}

.remark-label {
  font-size: 26rpx;
  color: #666;
}

.remark-name {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.edit-btn {
  font-size: 26rpx;
  color: #1989fa;
}

.follow-section {
  margin-left: 20rpx;
}

.follow-btn {
  padding: 8rpx 20rpx;
  border: 1rpx solid #1989fa;
  border-radius: 6rpx;
  transition: all 0.2s ease;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &:active:not(.disabled) {
    background-color: #1989fa;

    .follow-text {
      color: white;
    }
  }
}

.follow-text {
  font-size: 24rpx;
  color: #1989fa;
}

.settings-section {
  background-color: white;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-direction: row;

  &.last-item {
    border-bottom: none;
  }

  &.clickable:active {
    background-color: #f5f5f5;
  }
}

.setting-left {
  flex: 1;
}

.setting-title {
  font-size: 30rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.setting-right {
  display: flex;
  align-items: center;
}

.arrow {
  font-size: 24rpx;
  color: #ccc;
}

.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.popup-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.remark-popup {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 600rpx;
  max-width: 85vw;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.popup-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.input-section {
  margin-bottom: 30rpx;
}

uni-textarea {
  height: 75px;
}

.remark-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;

  &:focus {
    border-color: #007aff;
    background-color: white;
  }
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.btn {
  flex: 1;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 12rpx;
  transition: all 0.2s ease;

  &.cancel-btn {
    border: 1rpx solid #e0e0e0;
    background-color: #f8f8f8;

    &:active {
      background-color: #e8e8e8;
    }
  }

  &.confirm-btn {
    background-color: #007aff;

    &:active {
      background-color: #0056cc;
    }
  }
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #666;

  &.confirm-text {
    color: white;
  }
}
</style>
