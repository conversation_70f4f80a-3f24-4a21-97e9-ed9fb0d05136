<route type="page" lang="json">
{
  "style": {
    "navigationBarTitleText": "选择区/县",
    "enablePullDownRefresh": false,
    "app-plus": {
      "pullToRefresh": {
        "support": false,
        "color": "#0767FF"
      }
    }
  },
  "auth": false
}
</route>
<template>
  <c-page>
    <view>
      <c-list-item
        v-for="item in dataList"
        :key="item.adcode"
        :title="item.name"
        @click="onSelect(item)"
      ></c-list-item>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { getAreaData, type IArea } from '@/api/user'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
const dataList = ref<IArea[]>([])

let query = {}
const getAreaList = function (cityCode) {
  getAreaData('3', cityCode).then((res) => {
    if (res.data) {
      dataList.value = res.data.filter((item) => item.parentId === cityCode)
    }
  })
}

onLoad((options) => {
  console.log(options)
  if (options?.cityCode) {
    getAreaList(options.cityCode)
  }
  if (options) {
    query = options
    // console.log(query)
    // console.log(query.provinceName)
    // console.log(query.cityName)
  }
})
const onSelect = function (e) {
  // console.log({ ...query, ...e })
  uni.$emit('selectArea', { ...query, areaCode: e.adcode, areaName: e.name })
  uni.navigateBack({
    delta: 3,
  })
}
</script>

<style scoped lang="scss"></style>
