<template>
  <view
    class="my-input flex-row flex-1"
    :class="[size, border ? 'border' : '', disabled ? 'disabled' : '']"
  >
    <view class="flex-row flex-1 items-center">
      <slot name="prefix"></slot>
      <textarea
        class="input textarea"
        v-if="type === 'textarea'"
        :style="{ height: height }"
        :value="modelValue"
        :maxlength="maxlength"
        :focus="focus"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="onInput"
        @focus="onFocus"
        @blur="onBlur"
        @confirm="onConfirm"
        placeholder-style="color:#8a929f;"
      ></textarea>
      <input
        v-else
        class="input flex-1"
        :type="type"
        :value="modelValue"
        :maxlength="maxlength"
        :focus="focus"
        :disabled="disabled"
        :readonly="readonly"
        :placeholder="placeholder"
        @input="onInput"
        @focus="onFocus"
        @blur="onBlur"
        @confirm="onConfirm"
        placeholder-style="color:#8a929f;"
      />
      <slot name="suffix">
        <text v-if="unit" class="unit">{{ unit }}</text>
      </slot>
    </view>
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'CInput',
  inheritAttrs: false,
  components: {},
  emits: ['update:modelValue', 'input', 'focus', 'blur', 'confirm'],
  props: {
    modelValue: [String, Number],
    placeholder: {
      type: String,
      default: '请输入',
    },
    maxlength: {
      type: [String, Number],
      default: 120,
    },
    unit: String,
    border: Boolean,
    disabled: Boolean,
    readonly: Boolean,
    //default big large small
    size: {
      type: String,
      default: 'default',
    },
    type: {
      type: String,
      default: 'text',
    },
    height: {
      type: String,
      default: '',
    },
    focus: {
      type: Boolean,
      default: false,
    },
  },
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  data() {
    return {}
  },
  methods: {
    onInput(e) {
      this.$emit('input', e.detail.value)
      this.$emit('update:modelValue', e.detail.value)
    },
    onBlur(e) {
      uni.hideKeyboard()
      this.$emit('blur', e.detail.value)
    },
    onFocus(e) {
      this.$emit('focus', e)
    },
    onConfirm(e) {
      uni.hideKeyboard()
      this.$emit('confirm', e)
    },
  },
}
</script>

<style lang="scss">
.flex-row {
  display: flex;
}
.items-center {
  align-items: center;
}
.flex-1 {
  flex: 1;
}
.my-input {
  align-items: center;
  justify-content: space-between;
  border-radius: 12rpx;
  background-color: #fff;
  &.disabled {
    background-color: #f8f8f8;
    .input {
      color: #999;
    }
  }
  .input {
    font-size: 30rpx;
    &.textarea {
      width: 100%;
    }
  }
  .unit {
    margin-left: 12rpx;
    font-size: 30rpx;
    color: #999;
  }
  &.border {
    border: 1rpx solid #f3f3f3;
    padding: 8rpx;
  }
  &.big {
    border-radius: 12rpx;
    font-size: 32rpx;
    .input {
      font-size: 32rpx;
    }
    &.border {
      padding: 12rpx;
    }
  }
  &.large {
    border-radius: 8rpx;
    font-size: 36rpx;
    .input {
      font-size: 36rpx;
    }
    &.border {
      padding: 12rpx;
    }
  }
  &.small {
    border-radius: 6rpx;
    font-size: 26rpx;
    .input {
      font-size: 26rpx;
    }
    &.border {
      padding: 6rpx;
    }
  }
}
</style>
