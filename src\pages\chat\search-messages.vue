<route lang="json">
{
  "style": {
    "navigationBarTitleText": "搜索聊天记录",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="search-container">
      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-box">
          <image src="/static/images/im/icon.png" class="search-icon" mode="aspectFit" />
          <input 
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索聊天记录"
            confirm-type="search"
            @confirm="performSearch"
            @input="onInputChange"
            focus
          />
          <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
            <text class="clear-text">×</text>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="results-section">
        <!-- 加载状态 -->
        <view v-if="isSearching" class="loading-state">
          <text class="loading-text">搜索中...</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="!searchKeyword" class="empty-state">
          <image src="/static/images/im/icon.png" class="empty-icon" mode="aspectFit" />
          <text class="empty-text">输入关键词搜索聊天记录</text>
        </view>

        <!-- 无结果 -->
        <view v-else-if="searchResults.length === 0 && !isSearching" class="no-results">
          <image src="/static/images/im/fj.png" class="no-results-icon" mode="aspectFit" />
          <text class="no-results-text">未找到相关聊天记录</text>
        </view>

        <!-- 搜索结果列表 -->
        <view v-else class="results-list">
          <view class="results-header">
            <text class="results-count">找到 {{ searchResults.length }} 条相关记录</text>
          </view>
          
          <view 
            v-for="result in searchResults" 
            :key="result.id"
            class="result-item"
            @click="goToMessage(result)"
          >
            <view class="result-header">
              <image :src="result.senderAvatar" class="sender-avatar" mode="aspectFill" />
              <view class="result-info">
                <text class="sender-name">{{ result.senderName }}</text>
                <text class="message-time">{{ formatTime(result.timestamp) }}</text>
              </view>
            </view>
            
            <view class="result-content">
              <text class="message-text" v-html="highlightKeyword(result.content)"></text>
            </view>
            
            <!-- 消息类型标识 -->
            <view v-if="result.type !== 'text'" class="message-type">
              <text class="type-text">[{{ getMessageTypeText(result.type) }}]</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索历史 -->
      <view v-if="!searchKeyword && searchHistory.length > 0" class="history-section">
        <view class="history-header">
          <text class="history-title">搜索历史</text>
          <view class="clear-history-btn" @click="clearHistory">
            <text class="clear-history-text">清空</text>
          </view>
        </view>
        
        <view class="history-list">
          <view 
            v-for="(keyword, index) in searchHistory" 
            :key="index"
            class="history-item"
            @click="searchFromHistory(keyword)"
          >
            <image src="/static/images/im/icon.png" class="history-icon" mode="aspectFit" />
            <text class="history-keyword">{{ keyword }}</text>
            <view class="remove-history-btn" @click.stop="removeFromHistory(index)">
              <text class="remove-text">×</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'

interface SearchResult {
  id: string
  content: string
  type: 'text' | 'image' | 'voice' | 'file'
  timestamp: number
  senderName: string
  senderAvatar: string
  senderId: string
  messageId: string
}

// 响应式数据
const conversationId = ref('')
const conversationName = ref('')
const searchKeyword = ref('')
const isSearching = ref(false)
const searchResults = ref<SearchResult[]>([])
const searchHistory = ref<string[]>([])

// 生命周期
onLoad((options) => {
  conversationId.value = options.conversationId || ''
  conversationName.value = decodeURIComponent(options.conversationName || '聊天记录')
  
  uni.setNavigationBarTitle({
    title: `搜索 - ${conversationName.value}`
  })
})

onMounted(() => {
  loadSearchHistory()
})

// 加载搜索历史
const loadSearchHistory = () => {
  try {
    const history = uni.getStorageSync('chat_search_history') || []
    searchHistory.value = history
  } catch (error) {
    console.error('加载搜索历史失败:', error)
  }
}

// 保存搜索历史
const saveSearchHistory = () => {
  try {
    uni.setStorageSync('chat_search_history', searchHistory.value)
  } catch (error) {
    console.error('保存搜索历史失败:', error)
  }
}

// 输入变化
const onInputChange = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = []
  }
}

// 执行搜索
const performSearch = async () => {
  const keyword = searchKeyword.value.trim()
  if (!keyword) return

  // 添加到搜索历史
  if (!searchHistory.value.includes(keyword)) {
    searchHistory.value.unshift(keyword)
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }
    saveSearchHistory()
  }

  isSearching.value = true
  
  try {
    // TODO: 调用API搜索聊天记录
    // 这里使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟
    
    searchResults.value = [
      {
        id: '1',
        content: `这是一条包含"${keyword}"的测试消息`,
        type: 'text',
        timestamp: Date.now() - 86400000,
        senderName: '张三',
        senderAvatar: '/static/images/user/tx.png',
        senderId: 'user1',
        messageId: 'msg1'
      },
      {
        id: '2',
        content: `另一条关于"${keyword}"的聊天记录`,
        type: 'text',
        timestamp: Date.now() - 3600000,
        senderName: '李四',
        senderAvatar: '/static/images/user/tx.png',
        senderId: 'user2',
        messageId: 'msg2'
      }
    ]
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败',
      icon: 'error'
    })
  } finally {
    isSearching.value = false
  }
}

// 从历史记录搜索
const searchFromHistory = (keyword: string) => {
  searchKeyword.value = keyword
  performSearch()
}

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
}

// 清空搜索历史
const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
        saveSearchHistory()
      }
    }
  })
}

// 从历史中移除
const removeFromHistory = (index: number) => {
  searchHistory.value.splice(index, 1)
  saveSearchHistory()
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 86400000) { // 24小时内
    return dayjs(timestamp).format('HH:mm')
  } else if (diff < 86400000 * 7) { // 7天内
    return dayjs(timestamp).format('MM-DD HH:mm')
  } else {
    return dayjs(timestamp).format('YYYY-MM-DD')
  }
}

// 高亮关键词
const highlightKeyword = (text: string): string => {
  if (!searchKeyword.value) return text
  
  const keyword = searchKeyword.value.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span style="color: #1989fa; background-color: #e8f4ff;">$1</span>')
}

// 获取消息类型文本
const getMessageTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'image': '图片',
    'voice': '语音',
    'file': '文件',
    'video': '视频'
  }
  return typeMap[type] || '消息'
}

// 跳转到消息
const goToMessage = (result: SearchResult) => {
  // 返回聊天页面并定位到指定消息
  uni.navigateBack({
    success: () => {
      // 可以通过事件总线或其他方式通知聊天页面定位到指定消息
      uni.$emit('locateMessage', {
        messageId: result.messageId,
        timestamp: result.timestamp
      })
    }
  })
}
</script>

<style scoped lang="scss">
.search-container {
  padding: 20rpx;
}

.search-section {
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #f8f8f8;
  border-radius: 25rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  background: transparent;
  border: none;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ddd;
  border-radius: 50%;
  margin-left: 16rpx;
}

.clear-text {
  font-size: 24rpx;
  color: white;
}

.results-section {
  flex: 1;
}

.loading-state,
.empty-state,
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.loading-text,
.empty-text,
.no-results-text {
  font-size: 28rpx;
  color: #999;
}

.empty-icon,
.no-results-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.results-list {
  background-color: white;
  border-radius: 12rpx;
}

.results-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.results-count {
  font-size: 24rpx;
  color: #666;
}

.result-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background-color: #f8f8f8;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.sender-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.result-info {
  flex: 1;
}

.sender-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.message-time {
  font-size: 22rpx;
  color: #999;
}

.result-content {
  margin-bottom: 8rpx;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.message-type {
  margin-top: 8rpx;
}

.type-text {
  font-size: 24rpx;
  color: #1989fa;
}

.history-section {
  margin-top: 40rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.clear-history-btn {
  padding: 8rpx 16rpx;
}

.clear-history-text {
  font-size: 24rpx;
  color: #666;
}

.history-list {
  background-color: white;
  border-radius: 12rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background-color: #f8f8f8;
}

.history-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.history-keyword {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.remove-history-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.remove-text {
  font-size: 24rpx;
  color: #999;
}
</style>
