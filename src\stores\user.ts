import { getUserCenter, getUserTocCenter } from '@/api/user'
import { TOKEN_KEY } from '@/enums/cacheEnums'
import cache from '@/utils/cache'
import { defineStore } from 'pinia'
const isToc = import.meta.env.VITE_IS_TOC === 'true'
type User = {
  username: string
  nickname?: string
  userId: string
  wxOpenid?: string
  avatar?: string
  name?: string
  phone?: string
  imSign?: string
}
interface UserSate {
  userInfo: User
  token: string | null
  temToken: string | null
  verifyInfo: {
    /**
     * 是否认证成功
     */
    verifyed: boolean
    /**
     * 认证是否已经过去
     */
    isExpired: boolean
    /**
     * 到期时间
     */
    expireTime?: string
  }
}
export const useUserStore = defineStore({
  id: 'userStore',
  state: (): UserSate => ({
    userInfo: {} as User,
    token: cache.get(TOKEN_KEY) || null,
    temToken: null,
    verifyInfo: {
      verifyed: false,
      isExpired: false,
      expireTime: '',
    },
  }),
  getters: {
    isLogin: (state) => !!state.token,
  },
  actions: {
    async getUser(noLogin = false) {
      if (isToc) {
        const { data } = await getUserTocCenter({}, { noLogin })
        this.userInfo = {
          ...data.appUser,
          imSign: data.imSign, // 将imSign字段添加到userInfo中
        }
        this.verifyInfo = {
          verifyed: data.authenticated,
          isExpired: !data.member,
          expireTime: data.memberExpireTime,
        }
      } else {
        const { data } = await getUserCenter({}, { noLogin })
        this.userInfo = data.sysUser
      }
    },
    login(token: string) {
      this.token = token
      cache.set(TOKEN_KEY, token)
    },
    logout() {
      this.token = ''
      this.userInfo = {} as User
      cache.remove(TOKEN_KEY)
    },
  },
})
