<template>
  <view class="cell flex-row items-center" @click="onSelectArea">
    <view class="flex-1">
      <text v-if="areaInfo.areaCode" class="text-[30rpx]">{{ areaDesc }}</text>
      <text v-else class="text-[30rpx] placeholder">请选择</text>
    </view>
    <c-icon type="jinru" size="32" color="#c3c3c3"></c-icon>
  </view>
</template>

<script lang="ts">
export default defineComponent({
  name: 'ChooseLocation',
})
</script>
<script setup lang="ts">
import { navigateTo } from '@/utils/util'
import { defineComponent, ref } from 'vue'
const emit = defineEmits(['confirm'])
const areaInfo = ref<any>({})
const areaDesc = computed(() => {
  return `${areaInfo.value.provinceName}/${areaInfo.value.cityName}/${areaInfo.value.areaName}`
})
const onSelectArea = function () {
  uni.$once('selectArea', (e) => {
    areaInfo.value = e
    emit('confirm', e)
  })
  navigateTo('/user/provinceList')
}
</script>

<style scoped lang="scss">
.cell {
  .placeholder {
    color: #8a929f;
  }
}
</style>
