<template>
  <view>
    <c-statusbar></c-statusbar>
    <view class="title flex-row items-center justify-center" :style="{ height: props.height }">
      <slot name="leftBtn">
        <view v-if="showBack" class="h-full w-[44px] items-center justify-center" @tap="back">
          <c-icon type="fanhui"></c-icon>
        </view>
      </slot>
      <view class="flex-1">
        <slot>
          <view class="flex-row justify-center"
            ><text class="text-[40rpx leading-[44px] text-title font-medium">{{
              props.title
            }}</text></view
          >
        </slot>
      </view>
      <slot name="rightBtn">
        <view class="h-full w-[44px] items-center justify-center"> </view>
      </slot>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  height: {
    type: String,
    default: '44px',
  },
  showBack: {
    type: Boolean,
    default: false,
  },
})

const back = function () {
  uni.navigateBack()
}
</script>

<style scoped lang="scss"></style>
