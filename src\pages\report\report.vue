<route lang="json">
{
  "style": {
    "navigationBarTitleText": "投诉举报",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="report-container bg-gray-50 min-h-screen p-[30rpx]">
      <!-- 举报类型 -->
      <view class="report-type bg-white rounded-[20rpx] p-[30rpx] mb-[30rpx]">
        <view class="title mb-[30rpx]">
          <text class="text-[32rpx] font-medium">请选择举报类型</text>
        </view>
        <view class="type-list">
          <view 
            v-for="(type, index) in reportTypes" 
            :key="index"
            class="type-item flex items-center justify-between p-[20rpx] border-b border-gray-100"
            @click="selectType(type)"
          >
            <text class="text-[30rpx]">{{ type.label }}</text>
            <view class="radio" :class="{ active: selectedType?.value === type.value }">
              <view class="radio-inner"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细描述 -->
      <view class="report-description bg-white rounded-[20rpx] p-[30rpx] mb-[30rpx]">
        <view class="title mb-[20rpx]">
          <text class="text-[32rpx] font-medium">详细描述</text>
          <text class="text-[24rpx] text-gray-500 ml-[10rpx]">(选填)</text>
        </view>
        <textarea 
          v-model="description"
          placeholder="请详细描述您遇到的问题..."
          class="w-full h-[200rpx] p-[20rpx] border border-gray-200 rounded-[10rpx] text-[28rpx]"
          maxlength="500"
        />
        <view class="text-right mt-[10rpx]">
          <text class="text-[24rpx] text-gray-400">{{ description.length }}/500</text>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <c-button 
          @click="submitReport" 
          type="primary" 
          :disabled="!selectedType"
          class="w-full"
        >
          提交举报
        </c-button>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { reportUser, reportGroup } from '@/api/chat'

interface ReportType {
  value: string
  label: string
}

const reportTypes: ReportType[] = [
  { value: 'spam', label: '垃圾信息' },
  { value: 'harassment', label: '骚扰他人' },
  { value: 'fraud', label: '诈骗行为' },
  { value: 'inappropriate', label: '不当内容' },
  { value: 'violence', label: '暴力威胁' },
  { value: 'other', label: '其他' }
]

const selectedType = ref<ReportType | null>(null)
const description = ref('')
const reportTarget = reactive({
  type: '', // 'user' | 'group'
  id: '',
  name: ''
})

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  if (options.userId) {
    reportTarget.type = 'user'
    reportTarget.id = options.userId
    reportTarget.name = decodeURIComponent(options.username || '用户')
  } else if (options.groupId) {
    reportTarget.type = 'group'
    reportTarget.id = options.groupId
    reportTarget.name = decodeURIComponent(options.groupName || '群聊')
  }

  // 更新导航栏标题
  uni.setNavigationBarTitle({
    title: `举报${reportTarget.type === 'user' ? '用户' : '群聊'}`
  })
})

const selectType = (type: ReportType) => {
  selectedType.value = type
}

const submitReport = async () => {
  if (!selectedType.value) {
    uni.showToast({
      title: '请选择举报类型',
      icon: 'error'
    })
    return
  }

  try {
    uni.showLoading({
      title: '提交中...'
    })

    if (reportTarget.type === 'user') {
      await reportUser(reportTarget.id, selectedType.value.value, description.value)
    } else if (reportTarget.type === 'group') {
      await reportGroup(reportTarget.id, selectedType.value.value, description.value)
    }

    uni.hideLoading()
    uni.showModal({
      title: '举报成功',
      content: '感谢您的举报，我们会尽快处理。',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  } catch (error) {
    uni.hideLoading()
    console.error('提交举报失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'error'
    })
  }
}
</script>

<style scoped lang="scss">
.report-container {
  .report-type {
    .type-item {
      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: #f5f5f5;
      }

      .radio {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid #ddd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          border-color: #0767ff;

          .radio-inner {
            width: 20rpx;
            height: 20rpx;
            background-color: #0767ff;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .report-description {
    textarea {
      resize: none;
      font-family: inherit;
    }
  }
}
</style>
