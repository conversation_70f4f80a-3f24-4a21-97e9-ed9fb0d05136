/**
 * 通知管理工具
 * 用于管理系统通知和互动通知的实时推送
 */

export interface SystemNotification {
  id: string
  type: '系统通知' | '订单通知' | '消息通知' | '退款通知'
  title: string
  description: string
  time: string
  isRead: boolean
  detailUrl?: string
}

export interface InteractionNotification {
  id: string
  type: 'follow' | 'like' | 'comment' | 'share'
  userId: string
  userName: string
  userAvatar: string
  content: string
  time: string
  isRead: boolean
}

class NotificationManager {
  private systemNotifications: SystemNotification[] = []
  private interactionNotifications: InteractionNotification[] = []
  private listeners: Array<(type: 'system' | 'interaction', data: any) => void> = []

  constructor() {
    this.initMockData()
    this.startMockPush()
  }

  // 初始化模拟数据
  private initMockData() {
    this.systemNotifications = [
      {
        id: '1',
        type: '系统通知',
        title: '恭喜您！您已开通本产品年费会员',
        description: '您的会员权益已生效，快去体验吧！',
        time: '06-23 18:32',
        isRead: false,
      },
      {
        id: '2',
        type: '订单通知',
        title: '您有一笔新的订单成交',
        description: '订单号：202406231832001，金额：￥1,299',
        time: '06-23 18:32',
        isRead: false,
      },
    ]

    this.interactionNotifications = [
      {
        id: '1',
        type: 'follow',
        userId: 'user1',
        userName: '程振宇',
        userAvatar: '/static/images/user/avatar1.png',
        content: '关注了你',
        time: '周二',
        isRead: false,
      },
      {
        id: '2',
        type: 'like',
        userId: 'user2',
        userName: '奶油收藏家',
        userAvatar: '/static/images/user/avatar2.png',
        content: '赞了你的动态',
        time: '2小时前',
        isRead: false,
      },
    ]
  }

  // 开始模拟推送
  private startMockPush() {
    // 每30秒模拟一次新通知
    setInterval(() => {
      if (Math.random() > 0.5) {
        this.addMockSystemNotification()
      } else {
        this.addMockInteractionNotification()
      }
    }, 30000)
  }

  // 添加模拟系统通知
  private addMockSystemNotification() {
    const mockNotifications = [
      {
        type: '系统通知' as const,
        title: '系统维护通知',
        description: '系统将于今晚23:00-01:00进行维护，请提前做好准备',
      },
      {
        type: '订单通知' as const,
        title: '新订单提醒',
        description: '您有一笔新的订单待处理，请及时查看',
      },
      {
        type: '消息通知' as const,
        title: '活动通知',
        description: '双十一大促活动即将开始，敬请期待！',
      },
    ]

    const mock = mockNotifications[Math.floor(Math.random() * mockNotifications.length)]
    const notification: SystemNotification = {
      id: Date.now().toString(),
      type: mock.type,
      title: mock.title,
      description: mock.description,
      time: this.formatTime(new Date()),
      isRead: false,
    }

    this.systemNotifications.unshift(notification)
    this.notifyListeners('system', notification)
  }

  // 添加模拟互动通知
  private addMockInteractionNotification() {
    const mockUsers = [
      { name: '张三', avatar: '/static/images/user/avatar1.png' },
      { name: '李四', avatar: '/static/images/user/avatar2.png' },
      { name: '王五', avatar: '/static/images/user/avatar3.png' },
    ]

    const mockActions = [
      { type: 'follow' as const, content: '关注了你' },
      { type: 'like' as const, content: '赞了你的动态' },
      { type: 'comment' as const, content: '评论了你的动态' },
    ]

    const user = mockUsers[Math.floor(Math.random() * mockUsers.length)]
    const action = mockActions[Math.floor(Math.random() * mockActions.length)]

    const notification: InteractionNotification = {
      id: Date.now().toString(),
      type: action.type,
      userId: Date.now().toString(),
      userName: user.name,
      userAvatar: user.avatar,
      content: action.content,
      time: this.formatTime(new Date()),
      isRead: false,
    }

    this.interactionNotifications.unshift(notification)
    this.notifyListeners('interaction', notification)
  }

  // 格式化时间
  private formatTime(date: Date): string {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
  }

  // 添加监听器
  addListener(callback: (type: 'system' | 'interaction', data: any) => void) {
    this.listeners.push(callback)
  }

  // 移除监听器
  removeListener(callback: (type: 'system' | 'interaction', data: any) => void) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // 通知监听器
  private notifyListeners(type: 'system' | 'interaction', data: any) {
    this.listeners.forEach(callback => callback(type, data))
  }

  // 获取系统通知列表
  getSystemNotifications(): SystemNotification[] {
    return this.systemNotifications
  }

  // 获取互动通知列表
  getInteractionNotifications(): InteractionNotification[] {
    return this.interactionNotifications
  }

  // 获取未读系统通知数量
  getUnreadSystemCount(): number {
    return this.systemNotifications.filter(n => !n.isRead).length
  }

  // 获取未读互动通知数量
  getUnreadInteractionCount(): number {
    return this.interactionNotifications.filter(n => !n.isRead).length
  }

  // 标记系统通知为已读
  markSystemNotificationRead(id: string) {
    const notification = this.systemNotifications.find(n => n.id === id)
    if (notification) {
      notification.isRead = true
    }
  }

  // 标记互动通知为已读
  markInteractionNotificationRead(id: string) {
    const notification = this.interactionNotifications.find(n => n.id === id)
    if (notification) {
      notification.isRead = true
    }
  }

  // 清空所有通知
  clearAllNotifications() {
    this.systemNotifications = []
    this.interactionNotifications = []
  }
}

// 创建全局实例
export const notificationManager = new NotificationManager()

// 导出类型
export { NotificationManager }
