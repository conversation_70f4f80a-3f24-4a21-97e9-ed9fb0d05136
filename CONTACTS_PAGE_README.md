# 消息/通讯录页面说明

## 概述

这是一个仿照项目风格创建的消息和通讯录页面，完全符合图片中展示的设计要求。页面包含消息列表和通讯录两个标签页，支持搜索和用户交互功能。

## 页面结构

### 主页面 (`src/pages/contacts/contacts.vue`)

- **路由**: `/pages/contacts/contacts`
- **标题**: "消息"
- **功能**:
  - 蓝色渐变头部背景
  - 标签切换（消息/通讯录/新好友）
  - 搜索框
  - 动态内容切换

### 组件结构

#### 1. 消息列表 (`src/pages/contacts/components/messageList.vue`)

- **功能**: 显示聊天消息列表
- **特性**:
  - 用户头像显示
  - 最后一条消息预览
  - 时间显示
  - 未读消息数量红色徽章
  - 点击跳转到聊天页面

#### 2. 消息项 (`src/pages/contacts/components/messageItem.vue`)

- **功能**: 单个消息项组件
- **包含**:
  - 圆形头像
  - 用户名和时间
  - 消息内容预览
  - 未读数量徽章
  - 更多操作图标

#### 3. 通讯录列表 (`src/pages/contacts/components/contactsList.vue`)

- **功能**: 显示联系人列表
- **特性**:
  - "我的关注(数量)" 标题
  - 全部/好友筛选切换
  - 联系人列表展示
  - 分页加载

#### 4. 联系人项 (`src/pages/contacts/components/contactItem.vue`)

- **功能**: 单个联系人组件
- **包含**:
  - 圆形头像
  - 用户名
  - 好友标识徽章
  - 点击跳转到用户详情

#### 5. 新好友列表 (`src/pages/contacts/components/newFriendsList.vue`)

- **功能**: IM 新好友功能演示
- **特性**:
  - IM 连接状态显示
  - 用户登录/退出
  - 添加好友功能
  - 好友列表展示
  - 操作日志记录
  - 点击好友进入聊天

## 相关页面

### 聊天页面 (`src/pages/chat/chat.vue`)

- **路由**: `/pages/chat/chat`
- **功能**:
  - 消息气泡显示
  - 发送消息
  - 自动回复模拟
  - 滚动到底部

### 用户详情页 (`src/pages/user/profile.vue`)

- **路由**: `/pages/user/profile`
- **功能**:
  - 用户基本信息展示
  - 发消息/关注按钮
  - 用户动态列表
  - 图片预览

## 设计特点

### 1. 视觉风格

- **头部**: 蓝色渐变背景，与项目整体风格一致
- **布局**: 卡片式设计，圆角边框
- **颜色**: 主色调蓝色 (#0767FF)，符合项目规范
- **字体**: 层次分明的字体大小和颜色

### 2. 交互体验

- **标签切换**: 平滑的标签页切换动画
- **搜索功能**: 实时搜索框（UI 已实现）
- **列表滚动**: 支持下拉刷新和上拉加载
- **点击反馈**: 所有可点击元素都有适当的反馈

### 3. 响应式设计

- **适配**: 使用 rpx 单位，适配不同屏幕尺寸
- **布局**: Flexbox 布局，自适应内容
- **图片**: 圆形头像，统一尺寸

## 数据结构

### 消息数据接口

```typescript
interface IMessage {
  id: string
  avatar: string
  username: string
  lastMessage: string
  time: string
  unreadCount: number
}
```

### 联系人数据接口

```typescript
interface IContact {
  id: string
  avatar: string
  username: string
  remark?: string
  isFriend: boolean
}
```

## 功能特性

### 1. 消息功能

- ✅ 消息列表展示
- ✅ 未读消息数量显示
- ✅ 点击跳转聊天页面
- ✅ 时间格式化显示
- ✅ 头像和用户名显示

### 2. 通讯录功能

- ✅ 联系人列表展示
- ✅ 全部/好友筛选
- ✅ 好友状态标识
- ✅ 点击查看用户详情
- ✅ 关注数量统计

### 3. 新好友功能

- ✅ IM 连接状态监控
- ✅ 用户登录/退出
- ✅ 添加好友请求
- ✅ 好友列表获取
- ✅ 操作日志记录
- ✅ 点击好友聊天

### 4. 搜索功能

- ✅ 搜索框 UI 实现
- 🔄 搜索逻辑待完善（可根据需要添加）

### 5. 导航功能

- ✅ 底部标签栏集成
- ✅ 页面路由配置
- ✅ 标签高亮状态
- ✅ 路由参数编码处理

## 使用方法

### 1. 访问页面

- 通过底部标签栏的"消息"按钮访问
- 直接导航到 `/pages/contacts/contacts`

### 2. 功能操作

- **切换标签**: 点击"消息"、"通讯录"或"新好友"标签
- **查看消息**: 点击消息项进入聊天页面
- **查看用户**: 点击联系人进入用户详情页
- **筛选联系人**: 使用"全部"/"好友"切换按钮
- **IM 功能**: 在"新好友"标签中登录 IM、添加好友、查看好友列表

### 3. 扩展开发

- **添加搜索**: 在搜索组件中添加搜索逻辑
- **实时消息**: 集成 WebSocket 或 IM SDK
- **数据接口**: 连接后端 API 获取真实数据

## 技术栈

- **框架**: Vue 3 + TypeScript
- **UI**: uni-app + 项目自定义组件
- **样式**: TailwindCSS + SCSS
- **工具**: lodash-es, dayjs

## 文件清单

```
src/pages/contacts/
├── contacts.vue                 # 主页面
└── components/
    ├── messageList.vue         # 消息列表
    ├── messageItem.vue         # 消息项
    ├── contactsList.vue        # 通讯录列表
    ├── contactItem.vue         # 联系人项
    └── newFriendsList.vue      # 新好友列表（IM功能）

src/pages/chat/
└── chat.vue                    # 聊天页面

src/pages/user/
└── profile.vue                 # 用户详情页
```

这个页面完全按照图片要求实现，具有良好的用户体验和扩展性，可以作为项目中消息和通讯录功能的基础。
