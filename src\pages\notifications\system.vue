<route lang="json">
{
  "style": {
    "navigationBarTitleText": "系统通知"
  }
}
</route>

<template>
  <c-page>
    <view class="system-notifications">
      <!-- 通知列表 -->
      <view class="notification-list">
        <view
          v-for="notification in state.notifications"
          :key="notification.id"
          class="notification-item"
          @click="handleNotificationClick(notification)"
        >
          <!-- 时间标签 -->
          <view class="notification-time">{{ notification.time }}</view>

          <!-- 通知内容 -->
          <view class="notification-content">
            <view class="notification-type-title">{{ notification.type }}</view>
            <view class="notification-description">{{ notification.description }}</view>
            <view class="view-detail" @click.stop="viewDetail(notification)">
              <text class="detail-text">查看详情</text>
              <text class="detail-arrow">></text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="state.notifications.length === 0" class="empty-state">
          <text class="empty-text">暂无系统通知</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <c-loadmore :status="loadStatus" @reload="loadMore"></c-loadmore>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, onUnmounted } from 'vue'
import { notificationManager, type SystemNotification } from '@/utils/notification'

const state = reactive({
  notifications: [] as SystemNotification[],
  loadStatus: 'loadend' as 'loading' | 'loadend' | 'nomore',
})

// 计算属性
const loadStatus = computed(() => state.loadStatus)

// 处理通知点击
const handleNotificationClick = (notification: SystemNotification) => {
  // 标记为已读
  if (!notification.isRead) {
    notificationManager.markSystemNotificationRead(notification.id)
    notification.isRead = true
  }

  // 如果有详情链接，跳转到详情页
  if (notification.detailUrl) {
    uni.navigateTo({
      url: notification.detailUrl,
    })
  } else {
    // 显示通知详情
    uni.showModal({
      title: notification.title,
      content: notification.description,
      showCancel: false,
    })
  }
}

// 查看详情
const viewDetail = (notification: SystemNotification) => {
  // 标记为已读
  if (!notification.isRead) {
    notificationManager.markSystemNotificationRead(notification.id)
    notification.isRead = true
  }

  // 如果有详情链接，跳转到详情页
  if (notification.detailUrl) {
    uni.navigateTo({
      url: notification.detailUrl,
    })
  } else {
    uni.showToast({
      title: '暂无详情',
      icon: 'none',
    })
  }
}

// 标记全部已读
const markAllRead = () => {
  state.notifications.forEach((notification: SystemNotification) => {
    if (!notification.isRead) {
      notificationManager.markSystemNotificationRead(notification.id)
      notification.isRead = true
    }
  })

  uni.showToast({
    title: '已全部标记为已读',
    icon: 'success',
  })
}

// 加载更多
const loadMore = () => {
  // 这里可以实现分页加载逻辑
  state.loadStatus = 'nomore'
}

// 监听通知更新
const handleNotificationUpdate = (type: 'system' | 'interaction', data: any) => {
  if (type === 'system') {
    loadNotifications()
  }
}

// 加载通知数据
const loadNotifications = () => {
  state.notifications = notificationManager.getSystemNotifications()
}

// 生命周期
onMounted(() => {
  loadNotifications()
  notificationManager.addListener(handleNotificationUpdate)
})

onUnmounted(() => {
  notificationManager.removeListener(handleNotificationUpdate)
})
</script>

<style scoped lang="scss">
.system-notifications {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.notification-list {
  display: flex;
  flex-direction: column;
}

.notification-item {
  background-color: white;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:active {
    background-color: #f5f5f5;
  }
}

.notification-time {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notification-type-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.notification-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.view-detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #007aff;
}

.detail-arrow {
  font-size: 28rpx;
  color: #007aff;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
