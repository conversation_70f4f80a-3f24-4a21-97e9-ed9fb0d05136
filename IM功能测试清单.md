# 🧪 IM功能完整测试清单

## 📋 测试准备

### 🔑 测试用户账号
```
1. 管理员账号：
   - userID: administrator
   - 昵称: 管理员
   - 状态: 可登录IM

2. 普通用户1：
   - userID: 19573461517477745
   - 昵称: 牛牛1
   - 状态: 可登录IM

3. 普通用户2：
   - userID: 19573462910530810
   - 昵称: 贝贝1
   - 状态: 可登录IM
```

### 🛠️ 测试环境检查
- [ ] 确认IM SDK已正确配置
- [ ] 确认UserSig有效且未过期
- [ ] 确认网络连接正常
- [ ] 确认应用权限已开启

## 🚀 测试流程（从头到尾）

### 第一阶段：基础连接测试

#### 1. IM连接测试
**测试路径**: `/pages/admin/im-config`
- [ ] 打开IM配置管理页面
- [ ] 检查SDK配置信息显示正确
- [ ] 点击"测试连接"按钮
- [ ] 验证连接状态显示为"已连接"
- [ ] 检查日志输出无错误信息

#### 2. UserSig验证测试
**测试路径**: `/pages/admin/usersig-test`
- [ ] 打开UserSig测试页面
- [ ] 选择测试用户（administrator）
- [ ] 点击"获取UserSig"
- [ ] 验证UserSig生成成功
- [ ] 点击"验证UserSig"
- [ ] 确认验证通过

#### 3. IM登录测试
**测试路径**: `/pages/chat/test`
- [ ] 打开IM功能测试页面
- [ ] 选择用户"administrator"
- [ ] 点击"登录IM"
- [ ] 验证登录状态显示为"已登录"
- [ ] 检查用户信息显示正确

### 第二阶段：用户管理测试

#### 4. 通讯录功能测试
**测试路径**: 底部导航 → 消息 → 通讯录
- [ ] 查看通讯录列表
- [ ] 验证显示3个真实IM用户
- [ ] 切换"全部"、"好友"、"新好友"标签
- [ ] 验证筛选功能正常

#### 5. 好友申请处理测试
**测试路径**: 通讯录 → 新好友
- [ ] 查看新好友申请列表
- [ ] 点击"同意"按钮处理申请
- [ ] 验证好友关系建立成功
- [ ] 点击"拒绝"按钮处理申请
- [ ] 验证申请被正确拒绝

#### 6. 黑名单管理测试
**测试路径**: 聊天设置 → 黑名单管理
- [ ] 打开黑名单管理页面
- [ ] 验证页面正常加载（无报错）
- [ ] 添加用户到黑名单
- [ ] 验证黑名单列表更新
- [ ] 解除用户黑名单
- [ ] 验证解除操作成功

### 第三阶段：聊天功能测试

#### 7. 单聊功能测试
**测试路径**: 通讯录 → 选择好友 → 开始聊天
- [ ] 点击好友头像进入聊天页面
- [ ] 发送文本消息
- [ ] 验证消息发送成功
- [ ] 验证消息显示正确
- [ ] 验证消息状态（已发送/已读）

#### 8. 消息类型测试
**测试路径**: 聊天页面 → 更多功能
- [ ] 发送表情消息
- [ ] 发送图片消息（如果支持）
- [ ] 发送语音消息（如果支持）
- [ ] 验证各类型消息正常显示

#### 9. 消息操作测试
**测试路径**: 聊天页面 → 长按消息
- [ ] 长按消息显示操作菜单
- [ ] 测试消息撤回功能
- [ ] 测试消息转发功能
- [ ] 测试消息复制功能

### 第四阶段：群聊功能测试

#### 10. 群聊创建测试
**测试路径**: 通讯录 → 创建群聊
- [ ] 点击"创建群聊"按钮
- [ ] 选择群成员（至少2人）
- [ ] 设置群名称和群头像
- [ ] 创建群聊成功
- [ ] 验证群聊出现在消息列表

#### 11. 群聊管理测试
**测试路径**: 群聊页面 → 群设置
- [ ] 进入群聊设置页面
- [ ] 修改群名称
- [ ] 修改群公告
- [ ] 添加群成员
- [ ] 移除群成员
- [ ] 设置管理员权限

#### 12. 群成员管理测试
**测试路径**: 群设置 → 群成员管理
- [ ] 查看群成员列表
- [ ] 设置成员角色（管理员/普通成员）
- [ ] 禁言群成员
- [ ] 解除禁言
- [ ] 踢出群成员

### 第五阶段：高级功能测试

#### 13. 消息搜索测试
**测试路径**: 聊天页面 → 搜索消息
- [ ] 打开消息搜索页面
- [ ] 输入关键词搜索
- [ ] 验证搜索结果正确
- [ ] 点击搜索结果跳转到对应消息

#### 14. 聊天设置测试
**测试路径**: 聊天页面 → 设置
- [ ] 设置消息免打扰
- [ ] 设置聊天置顶
- [ ] 清空聊天记录
- [ ] 验证设置生效

#### 15. 通知功能测试
**测试路径**: 消息列表 → 系统通知/互动通知
- [ ] 查看系统通知列表
- [ ] 标记通知已读
- [ ] 查看互动通知列表
- [ ] 查看收藏通知列表

### 第六阶段：API接口测试

#### 16. 用户管理API测试
- [ ] 测试用户信息获取接口
- [ ] 测试用户状态更新接口
- [ ] 测试用户强制下线接口
- [ ] 测试用户统计接口

#### 17. 消息管理API测试
- [ ] 测试消息发送接口
- [ ] 测试消息撤回接口
- [ ] 测试消息已读标记接口
- [ ] 测试消息搜索接口

#### 18. 会话管理API测试
- [ ] 测试会话创建接口
- [ ] 测试会话置顶接口
- [ ] 测试会话免打扰接口
- [ ] 测试会话删除接口

## 🔍 测试要点

### ✅ 成功标准
1. **连接稳定**: IM连接状态稳定，无频繁断线
2. **消息可靠**: 消息发送成功率100%，无丢失
3. **功能完整**: 所有核心功能正常工作
4. **性能良好**: 页面响应速度快，无卡顿
5. **错误处理**: 异常情况有友好提示

### ⚠️ 注意事项
1. **网络环境**: 确保网络连接稳定
2. **用户权限**: 确认用户有相应操作权限
3. **数据同步**: 多端登录时注意数据同步
4. **内存管理**: 长时间使用注意内存占用
5. **日志记录**: 保留测试过程中的日志信息

### 🐛 常见问题排查
1. **登录失败**: 检查UserSig是否过期
2. **消息发送失败**: 检查网络连接和权限
3. **好友申请失败**: 检查用户状态和权限
4. **群聊创建失败**: 检查成员数量和权限
5. **API调用失败**: 检查接口地址和参数

## 📊 测试报告模板

### 测试环境
- 测试时间: ____
- 测试人员: ____
- 设备信息: ____
- 网络环境: ____

### 测试结果
- 通过项目: __ / __
- 失败项目: __ / __
- 成功率: __%

### 问题记录
1. 问题描述: ____
   - 复现步骤: ____
   - 预期结果: ____
   - 实际结果: ____
   - 严重程度: ____

### 建议改进
1. ____
2. ____
3. ____

---

## 🚀 快速测试入口

### 开发测试页面
- IM配置管理: `/pages/admin/im-config`
- UserSig测试: `/pages/admin/usersig-test`
- IM功能测试: `/pages/chat/test`

### 用户功能页面
- 消息列表: 底部导航 → 消息
- 通讯录: 底部导航 → 消息 → 通讯录
- 聊天页面: 通讯录 → 选择用户 → 开始聊天

### 管理功能页面
- 黑名单管理: 聊天设置 → 黑名单管理
- 群聊管理: 群聊页面 → 群设置
- 通知管理: 消息列表 → 系统通知/互动通知

按照这个清单逐项测试，可以全面验证IM系统的功能完整性和稳定性！
