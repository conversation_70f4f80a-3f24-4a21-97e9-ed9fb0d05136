<route lang="json">
{
  "style": {
    "navigationBarTitleText": ""
  },
  "auth": false
}
</route>
<template>
  <c-page>
    <view class="h-full flex-1 bg-white p-2xl">
      <view class="mb-sm">
        <text class="text-3xl text-main font-medium">找回密码</text>
      </view>
      <view class="mb-xl">
        <text class="text-base text-[#73747B]">验证码已通过短息发送至 180****6167</text>
      </view>
      <view class="form-cell flex-row items-center mb-[30rpx]" v-if="step === 0">
        <input type="text" v-model="formData.mobile" class="flex-1" placeholder="请输入手机号" />
      </view>
      <view class="form-cell flex-row items-center mb-[30rpx]" v-if="step === 1">
        <input type="number" v-model="formData.code" class="flex-1" placeholder="请输入验证码" />
        <view class="pl-3 leading-4 ml-3" @click="sendSms">
          <text class="mini-btn text-secondary" v-if="smsState.currentTime >= 0">{{
            `${smsState.currentTime}s重新获取`
          }}</text>
          <text v-else class="mini-btn text-[#576B95]"> 获取验证码</text>
        </view>
      </view>
      <view class="form-cell flex-row items-center mb-[30rpx]" v-if="step === 2">
        <input
          type="password"
          v-model="formData.password"
          class="flex-1"
          placeholder="请输入密码"
        />
      </view>
      <view class="w-full mt-5xl">
        <c-button type="primary" block size="large" :disabled="isDisabled" @click="handleSubmit()">
          {{ step < 2 ? '下一步' : '完成' }}
        </c-button>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { smsSend } from '@/api/app'
import { message } from '@/utils/util'
import { onBackPress } from '@dcloudio/uni-app'

// import { ref } from 'vue'
const step = ref(0)
const formData = reactive({
  mobile: '',
  code: '',
  password: '',
})

const isDisabled = computed(() => {
  return (
    (step.value === 0 && !formData.mobile) ||
    (step.value === 1 && !formData.code) ||
    (step.value === 2 && !formData.password)
  )
})

/**
 * 发送验证码
 */
const smsState = reactive({
  maxTime: 60,
  currentTime: -1,
  timer: null as any,
})

const sendSms = async () => {
  if (smsState.currentTime >= 0) {
    return
  }
  if (!formData.mobile || formData.mobile.length !== 11) {
    message.warning('手机号不合法')
    return
  }
  // console.log('开始发送')
  await smsSend(formData.mobile)
  smsState.currentTime = smsState.maxTime
  message.warning('发送成功')
  smsState.timer = setInterval(() => {
    if (smsState.currentTime <= 0) {
      smsState.currentTime = -1
      clearInterval(smsState.timer)
      return
    }
    smsState.currentTime--
  }, 1000)
}

const telReg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/

const handleSubmit = function () {
  if (step.value === 0) {
    if (telReg.test(formData.mobile)) {
      step.value++
    } else {
      message.warning('手机号格式不正确')
    }
    return
  }
  if (step.value === 1) {
    if (formData.code) {
      step.value++
    } else {
      message.warning('请输入验证码')
    }
    return
  }
}

onBackPress((options) => {
  console.log(options)
  if (options.from === 'backbutton' && step.value > 0) {
    step.value--
    if (step.value === 1) {
      formData.password = ''
    }
    if (step.value === 0) {
      formData.code = ''
    }
    return true
  }
  return false
})
</script>

<style scoped lang="scss">
.form-cell {
  height: 82rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f8f8f8;
  input {
    height: 100%;
  }
}
</style>
