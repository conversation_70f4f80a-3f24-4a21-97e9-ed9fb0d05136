<template>
  <view class="chat-list bg-white relative z-10 rounded-t-[30rpx] overflow-scroll p-[20rpx]">
    <view class="flex-row items-center justify-between mb-[24rpx]">
      <view class="flex-row h-[56rpx] items-center justify-center">
        <!-- <c-icon size="28" type="qingchu" color="#999999"></c-icon> -->
        <text class="ml-[10rpx] text-[30rpx] text-title">我的关注(56)</text>
      </view>
      <view class="flex-row h-[66rpx] items-center p-[6rpx] rounded-[24rpx] bg-[#F4F6FA]">
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'all' ? 'bg-white' : '']"
          @click="switchStatus('all')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'all' ? 'bg-white text-title' : 'text-muted']"
            >全部</text
          >
        </view>
        <view
          class="justify-center items-center w-[96rpx] h-[54rpx] rounded-[20rpx]"
          :class="[state.queryForm.status === 'friend' ? 'bg-white' : '']"
          @click="switchStatus('friend')"
        >
          <text
            class="text-[28rpx]"
            :class="[state.queryForm.status === 'friend' ? 'bg-white text-title' : 'text-muted']"
            >好友</text
          >
        </view>
      </view>
    </view>
    <friendItem v-for="item in state.dataList" :key="item.id" :data="item"></friendItem>
    <c-loadmore :status="state.loadStatus" @reload="getData()"></c-loadmore>
  </view>
</template>

<script setup lang="ts">
import type { IFriend } from 'types/chat.interface'
import friendItem from './friendItem.vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import { IM_CONFIG } from '@/config/im'
type Status = 'all' | 'friend'

const state = reactive({
  currentTab: 'carSource',
  loading: false,
  isPage: true,
  showFilter: false,
  loadStatus: 'loadend',
  queryForm: {
    current: 1,
    size: 20,
    status: 'all' as Status,
  },
  dataList: [] as IFriend[],
})

const dataList: IFriend[] = [
  // 添加测试账号
  {
    id: 'administrator',
    avatar: '/static/images/user/tx.png',
    username: '管理员',
    userID: 'administrator',
    userSig:
      'eJwtzNEKgjAYBeB32W0h-7TZErwMAiUhJ0R3s636M3VsQ4To3TPz8nzncN5E5GUwaEsSEgZA1nNGpTuPN5xZqhY7dN5K39tl4FQjjUFFEhoDUKCcw7-Ro0GrJ2eMhQCLemx-tmUxBYj4bnnB*-SfiaMyFYi6zorXUwlZNpRfxtGds7yKVtfO9XtWnIbHYZOSzxdePzUQ',
  },
  {
    id: '19573461517477745',
    avatar: '/static/images/user/tx.png',
    username: '牛牛1',
    userID: '19573461517477745',
    userSig:
      'eJwtzUELgkAQBeD-sueQWdvZHYWOBVZ6qMCuwa4xa8aiJkX03zPzON97vHmL0-4YDa4VqYgjEIvpZuvuPVc8sUzQLJWWKI0yxiicS52tLyGwFanUABIkEfwT9wzcutERMQaYtefmZwa1BFBI8wpfxx9rf65wubWdLup84x4uaYKl28HvspIwf2UWgy-KgTytxOcLdtIy2g__',
  },
  {
    id: '19573462910530810',
    avatar: '/static/images/user/tx.png',
    username: '贝贝1',
    userID: '19573462910530810',
    userSig:
      'eJw1zc0KgkAUBeB3mXXEuaOjo9CijUUI0Z-VMppRLtFgaiFF756ZLe93Due*xDbdjB-2ErGQY4hRf7OxruGce6ZIhZ4fyIigPGj6l2pzOZUlGxFTABBIa-wS25Zc2c6VUhIYtOHr10IVEKCkHla46H64JM*OqckPs*VinXjPKl1ltW39WxFp8HzqWuDudtneP0-E*wNyljLX',
  },
  // 保留原有的群组数据
  {
    id: '4',
    avatar: '',
    username: '福特车源服务交流群',
  },
  {
    id: '5',
    avatar: '',
    username: '福特车源服务交流群',
  },
]

const getData = function () {
  state.loadStatus = 'loading'
  return new Promise((reslove) => {
    setTimeout(() => {
      const _dataList = cloneDeep(dataList)
      state.dataList.push(
        ..._dataList.map((item) => {
          item.id = item.id + dayjs().valueOf()
          return item
        })
      )
      state.loadStatus = 'loadend'
      reslove(true)
      uni.stopPullDownRefresh()
    }, 200)
  })
}
// getData()
const initData = function () {
  state.queryForm.current = 1
  state.dataList = []
  getData()
}

const loadMore = function () {
  state.queryForm.current++
  getData()
}

const switchStatus = function (status: Status) {
  state.queryForm.status = status
  initData()
}

defineExpose({
  loadMore,
  getData,
  initData,
})
</script>

<style scoped lang="scss">
.btn {
  border: 1rpx solid #f4f6fa;
}
</style>
