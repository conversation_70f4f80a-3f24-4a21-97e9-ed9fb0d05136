<route lang="json">
{
  "style": {
    "navigationBarTitleText": "聊天文件",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <c-page>
    <view class="chat-files-container">
      <!-- 文件类型筛选 -->
      <view class="filter-section">
        <view class="filter-tabs">
          <view 
            v-for="tab in filterTabs" 
            :key="tab.type"
            class="filter-tab"
            :class="{ 'active': currentFilter === tab.type }"
            @click="switchFilter(tab.type)"
          >
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </view>
      </view>

      <!-- 文件列表 -->
      <view class="files-section">
        <view v-if="filteredFiles.length === 0" class="empty-state">
          <image src="/static/images/im/icon.png" class="empty-icon" mode="aspectFit" />
          <text class="empty-text">暂无{{ getCurrentFilterName() }}</text>
        </view>
        
        <view v-else class="files-list">
          <!-- 图片文件 -->
          <view v-if="currentFilter === 'image'" class="image-grid">
            <view 
              v-for="file in filteredFiles" 
              :key="file.id"
              class="image-item"
              @click="previewImage(file)"
            >
              <image :src="file.url" class="image-thumbnail" mode="aspectFill" />
              <view class="image-info">
                <text class="image-time">{{ formatTime(file.timestamp) }}</text>
              </view>
            </view>
          </view>

          <!-- 其他文件 -->
          <view v-else class="file-list">
            <view 
              v-for="file in filteredFiles" 
              :key="file.id"
              class="file-item"
              @click="openFile(file)"
            >
              <view class="file-icon-wrapper">
                <image :src="getFileIcon(file.type)" class="file-icon" mode="aspectFit" />
              </view>
              <view class="file-details">
                <text class="file-name">{{ file.name }}</text>
                <view class="file-meta">
                  <text class="file-size">{{ formatFileSize(file.size) }}</text>
                  <text class="file-time">{{ formatTime(file.timestamp) }}</text>
                </view>
              </view>
              <view class="file-actions">
                <view class="action-btn" @click.stop="downloadFile(file)">
                  <image src="/static/images/im/fj.png" class="action-icon" mode="aspectFit" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </c-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'

interface ChatFile {
  id: string
  name: string
  type: 'image' | 'video' | 'audio' | 'document' | 'other'
  url: string
  size: number
  timestamp: number
  sender: string
  senderName: string
}

// 响应式数据
const conversationId = ref('')
const conversationName = ref('')
const currentFilter = ref<string>('all')
const fileList = ref<ChatFile[]>([])

// 筛选选项
const filterTabs = ref([
  { type: 'all', name: '全部' },
  { type: 'image', name: '图片' },
  { type: 'video', name: '视频' },
  { type: 'audio', name: '语音' },
  { type: 'document', name: '文档' },
  { type: 'other', name: '其他' }
])

// 计算属性
const filteredFiles = computed(() => {
  if (currentFilter.value === 'all') {
    return fileList.value
  }
  return fileList.value.filter(file => file.type === currentFilter.value)
})

// 生命周期
onLoad((options) => {
  conversationId.value = options.conversationId || ''
  conversationName.value = decodeURIComponent(options.conversationName || '聊天文件')
  
  uni.setNavigationBarTitle({
    title: `${conversationName.value} - 文件`
  })
})

onMounted(() => {
  loadChatFiles()
})

// 加载聊天文件
const loadChatFiles = async () => {
  try {
    // TODO: 调用API获取聊天文件列表
    // 这里使用模拟数据
    fileList.value = [
      {
        id: '1',
        name: '项目文档.pdf',
        type: 'document',
        url: '/static/images/im/icon.png',
        size: 1024 * 1024 * 2.5, // 2.5MB
        timestamp: Date.now() - 86400000,
        sender: 'user1',
        senderName: '张三'
      },
      {
        id: '2',
        name: '聊天截图.jpg',
        type: 'image',
        url: '/static/images/user/tx.png',
        size: 1024 * 512, // 512KB
        timestamp: Date.now() - 3600000,
        sender: 'user2',
        senderName: '李四'
      },
      {
        id: '3',
        name: '语音消息.mp3',
        type: 'audio',
        url: '/static/images/im/icon.png',
        size: 1024 * 256, // 256KB
        timestamp: Date.now() - 1800000,
        sender: 'user1',
        senderName: '张三'
      }
    ]
  } catch (error) {
    console.error('加载聊天文件失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

// 切换筛选
const switchFilter = (type: string) => {
  currentFilter.value = type
}

// 获取当前筛选名称
const getCurrentFilterName = (): string => {
  const tab = filterTabs.value.find(tab => tab.type === currentFilter.value)
  return tab ? tab.name : '文件'
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('MM-DD HH:mm')
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + 'KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
  }
}

// 获取文件图标
const getFileIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    'document': '/static/images/im/fj.png',
    'audio': '/static/images/im/icon.png',
    'video': '/static/images/im/icon.png',
    'other': '/static/images/im/fj.png'
  }
  return iconMap[type] || '/static/images/im/fj.png'
}

// 预览图片
const previewImage = (file: ChatFile) => {
  const urls = filteredFiles.value
    .filter(f => f.type === 'image')
    .map(f => f.url)
  
  const current = urls.indexOf(file.url)
  
  uni.previewImage({
    urls,
    current
  })
}

// 打开文件
const openFile = (file: ChatFile) => {
  if (file.type === 'image') {
    previewImage(file)
    return
  }
  
  // 其他类型文件的处理
  uni.showActionSheet({
    itemList: ['下载文件', '转发文件'],
    success: (res) => {
      if (res.tapIndex === 0) {
        downloadFile(file)
      } else if (res.tapIndex === 1) {
        forwardFile(file)
      }
    }
  })
}

// 下载文件
const downloadFile = (file: ChatFile) => {
  uni.showLoading({
    title: '下载中...'
  })
  
  uni.downloadFile({
    url: file.url,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        // 可以选择保存到相册或文件管理器
        if (file.type === 'image') {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '已保存到相册',
                icon: 'success'
              })
            }
          })
        }
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'error'
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'error'
      })
    }
  })
}

// 转发文件
const forwardFile = (file: ChatFile) => {
  uni.navigateTo({
    url: `/pages/chat/forward-message?content=${encodeURIComponent('[文件] ' + file.name)}&messageId=${file.id}`
  })
}
</script>

<style scoped lang="scss">
.chat-files-container {
  padding: 20rpx;
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-tabs {
  display: flex;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 8rpx;
}

.filter-tab {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  border-radius: 8rpx;
  transition: all 0.2s;
}

.filter-tab.active {
  background-color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-text {
  font-size: 26rpx;
  color: #666;
}

.filter-tab.active .tab-text {
  color: #1989fa;
  font-weight: bold;
}

.files-section {
  flex: 1;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 图片网格布局 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-thumbnail {
  width: 100%;
  height: 100%;
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20rpx 12rpx 8rpx;
}

.image-time {
  font-size: 20rpx;
  color: white;
}

/* 文件列表布局 */
.file-list {
  background-color: white;
  border-radius: 12rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:active {
  background-color: #f8f8f8;
}

.file-icon-wrapper {
  margin-right: 24rpx;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
}

.file-details {
  flex: 1;
}

.file-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.file-size,
.file-time {
  font-size: 24rpx;
  color: #999;
}

.file-actions {
  margin-left: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.action-btn:active {
  background-color: #e8e8e8;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}
</style>
